# Release Notes

## ZDS DataQuest v0.1.0 (2025-05-26)

We are excited to introduce the first release of ZDS DataQuest - an intelligent and comprehensive data access management platform for the ZDS Platform ecosystem.

### Highlights

- **🚀 Smart Request Management**: Easily create and track access requests to ZDS Platform, Atlas Reports, and adhoc data
- **📊 Atlas Dashboard Integration**: Easily create and track access requests to Atlas reports and dashboards from a unified interface
- **🔐 Access Rights Management**: View and manage access permissions to ZDS systems transparently
- **🌙 Modern Interface**: Dark mode support and responsive design for optimal user experience
- **🔍 Intelligent Search**: Advanced search system for quick information retrieval

### Key Features

#### 📝 Request Management
- **ZDS Platform Access Requests**: Request access to Whitelist Tool, Funnel Tool, and Ads Report
- **Atlas Report/Dashboard Requests**: Register for access to reports and dashboards on Atlas system
- **Adhoc Data Requests**: Request custom data for specialized analysis needs
- **Status Flow Tracking**: Monitor request status in real-time with detailed timeline
- **Timeline Management**: View change history and request processing progress

#### 🔐 Access Rights Management
- **ZDS Rights Dashboard**: View overview of current access permissions to ZDS systems
- **Expiration Tracking**: Manage validity periods of access permissions
- **Access History**: View usage history and permission changes

#### 🔔 Notification System
- **In-app Notifications**: Receive direct notifications within the application about request status
- **Email Notifications**: Automatic email alerts for important updates
- **Notification Customization**: Configure types of notifications to receive

#### 📋 Release Notes Management
- **Update Management Page**: Track new features and application improvements
- **Version History**: View details of released versions
- **Update Notifications**: Receive information about new features

#### 💬 Feedback System
- **Send Feedback**: Contribute feedback directly to the development team
- **Feedback Tracking**: View processing status of submitted feedback
- **Collaborative Development**: Participate in building and perfecting the application

#### 🎨 User Experience
- **Dark Mode**: Switch between light and dark interfaces
- **Responsive Design**: Optimized for all screen sizes
- **Multi-language**: Support for Vietnamese and English
- **Intelligent Search**: Quick search with AI-powered functionality

### Getting Started

1. **Login**: Use your LDAP account to access the system
2. **Create Request**: Click "+ New Request" to create new access requests
3. **Track Progress**: Use Dashboard to monitor request status
4. **Manage Access**: View current access permissions in Access Management section
5. **Settings**: Customize notifications and interface in Settings

### System Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Stable internet connection
- Access to VNG internal network
- Valid LDAP account

### Support

For assistance, please:
- Use the Feedback feature within the application
- Contact the ZDS DataQuest team via email
- Refer to the user guide documentation in the application

---

## ZDS DataQuest v0.1.1 (2025-05-27)

Building on the solid foundation of v0.1.0, we're excited to announce significant enhancements that expand ZDS DataQuest's capabilities and improve user experience.

### 🎯 What's New

#### � Enhanced Feedback System
- **New Feedback Management Page**: Complete redesign of feedback system with dedicated list and detail views
- **Feedback List View**: Modern card-based layout displaying all submitted feedback with status tracking
- **Interactive Feedback Details**: Click on any feedback to view comprehensive details in a modal
- **File Attachment Support**:
  - View and download attached files directly from feedback details
  - Image preview functionality for visual attachments
  - Support for multiple file formats (PDF, DOC, images, etc.)
- **Improved Navigation**: Separate pages for viewing feedback list and creating new feedback
- **Status Indicators**: Color-coded status and priority badges for better visual organization

#### 📎 Advanced Adhoc Data Requests
- **File Upload/Download System**: Complete file management integration for adhoc data requests
- **Multi-format File Support**: Upload and manage various file formats (CSV, Excel, JSON, PDF, images, etc.)
- **File Operations**:
  - Secure file upload with progress tracking
  - Direct file download from request details
  - File size and type validation
  - File preview for supported formats
- **Enhanced Request Details**: Dedicated file section showing all attached data files
- **Improved User Experience**: Streamlined workflow for data file management

#### 🎨 UI/UX Improvements
- **Modern Interface Design**: Updated visual design with improved spacing and typography
- **Enhanced Card Layouts**: Better organized information display across all pages
- **Responsive Design Enhancements**: Improved mobile and tablet experience
- **Interactive Elements**: Enhanced hover effects and smooth transitions
- **Better Navigation**: Improved page flow and user journey
- **Accessibility Improvements**: Better contrast and keyboard navigation support

### � Technical Enhancements

- **File Management API**: Robust backend integration for file operations
- **Modal System**: Reusable modal components for better user interaction
- **State Management**: Improved state handling for complex UI interactions
- **Error Handling**: Enhanced error messages and user feedback
- **Performance Optimization**: Faster page loads and smoother interactions
- **Security**: Secure file upload and download with proper authentication

### � User Experience Improvements

- **Intuitive Workflows**: Simplified processes for common tasks
- **Visual Feedback**: Clear status indicators and progress tracking
- **Consistent Design**: Unified design language across all features
- **Mobile Optimization**: Better experience on mobile devices
- **Loading States**: Improved loading indicators and skeleton screens

### 📋 Detailed Changes

#### Feedback System Overhaul
- Split feedback functionality into separate list and creation pages
- Added comprehensive feedback detail modal with full information display
- Implemented file attachment viewing and downloading capabilities
- Enhanced feedback status tracking and visual indicators

#### Adhoc Data Request Enhancement
- Integrated complete file management system
- Added file upload progress tracking and validation
- Implemented secure file download functionality
- Enhanced request detail page with dedicated file section

#### UI/UX Polish
- Updated color schemes and typography for better readability
- Improved button designs and interactive elements
- Enhanced responsive layouts for all screen sizes
- Added smooth animations and transitions throughout the application

### 🔄 Migration Notes

- Existing feedback data remains fully compatible
- All previous adhoc data requests maintain their functionality
- No user action required for upgrade

### 🎯 Coming Next

- Advanced file sharing capabilities
- Enhanced request workflow management
- Additional file format support
- Bulk operations for file management

---

Thank you for using ZDS DataQuest. We look forward to your feedback as we continue to develop and enhance the application in future releases.
