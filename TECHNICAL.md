# Technical Documentation - Form Management

## Technology Stack

### Frontend
- **Framework**: React 19.1.0
- **Routing**: React Router DOM 7.4.1
- **HTTP Client**: Axios 1.8.4
- **UI Components**: 
  - React Icons 5.5.0
  - Styled Components 6.1.17
- **Testing**: 
  - Jest
  - React Testing Library 16.3.0
  - Testing Library DOM 10.4.0
  - Testing Library Jest DOM 6.6.3
  - Testing Library User Event 13.5.0
- **Build Tools**: Create React App (React Scripts 5.0.1)
- **Performance Monitoring**: Web Vitals 2.1.4

### Backend Integration
- RESTful API integration via Axios
- JWT-based authentication
- Environment-based configuration (REACT_APP_API_URL)

### State Management
- React Context API for global state (ThemeContext, etc.)
- Local component state with useState
- Local storage for persistent data (user preferences, authentication tokens)

### UI/UX Features
- Responsive design
- Dark/Light theme support
- Multi-language support (English/Vietnamese)
- Collapsible sidebar navigation
- Search functionality
- Notification system
- Kanban and grid view options

### Development Tools
- ESLint for code quality
- Browserslist for browser compatibility

## Architecture

### Component Structure
- **App**: Main application container
- **Layout Components**:
  - Sidebar: Navigation menu
  - NotificationCenter: User notifications
- **Page Components**:
  - Dashboard
  - Requests (with various request types)
  - Users
  - Reports
  - Settings
  - WhitelistPlatform
  - AskAI
- **Shared Components**:
  - Form elements
  - Modal dialogs
  - Status badges
  - Search components

### Data Flow
1. API requests are made through the apiService utility
2. Component state is updated with API response data
3. UI renders based on component state
4. User interactions trigger state updates and/or API calls

### Authentication Flow
1. User logs in via Login component
2. JWT token is stored in localStorage with expiration time
3. Token is checked on application startup
4. Expired tokens trigger automatic logout
5. Protected routes redirect to login if no valid token exists

### Theming System
- Theme preferences stored in localStorage
- ThemeContext provides theme state to all components
- CSS variables used for consistent theming

## Environment Configuration
- Development: Local API server (http://localhost:3014)
- Production: Configured via REACT_APP_API_URL environment variable

## Browser Compatibility
- Production: Modern browsers (>0.2% market share)
- Development: Latest Chrome, Firefox, and Safari versions
