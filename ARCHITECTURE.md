# Architecture Documentation

## Overview

The Form Management application is a React-based web application designed to manage access requests and data queries. This document outlines the architectural design, patterns, and decisions that shape the application.

## System Architecture

### High-Level Architecture

The application follows a client-side rendering architecture with the following components:

```
┌─────────────────────────────────────────────────────────────┐
│                      Client Browser                         │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    React Application                         │
│                                                             │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────────┐    │
│  │   Routing   │◄──┤   Context   │◄──┤  Components     │    │
│  └─────┬───────┘   └─────────────┘   └─────────────────┘    │
│        │                 ▲                    ▲              │
│        ▼                 │                    │              │
│  ┌─────────────┐   ┌─────┴───────┐   ┌────────┴────────┐    │
│  │    Pages    │──►│   Services  │──►│     Utilities    │    │
│  └─────────────┘   └─────────────┘   └─────────────────┘    │
│                          │                                   │
└──────────────────────────┼───────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────┐
│                       REST API                              │
└─────────────────────────────────────────────────────────────┘
```

### Key Components

1. **Routing Layer**: Manages navigation between different pages
2. **Context Layer**: Provides global state management
3. **Components Layer**: Reusable UI components
4. **Pages Layer**: Page-specific components and layouts
5. **Services Layer**: API communication and business logic
6. **Utilities Layer**: Helper functions and shared utilities

## Frontend Architecture

### Component Architecture

The application follows a component-based architecture with a focus on reusability and separation of concerns.

#### Component Types

1. **Container Components**
   - Manage state and data fetching
   - Connect to context providers
   - Pass data to presentational components

2. **Presentational Components**
   - Focus on UI rendering
   - Receive data via props
   - Minimal internal state
   - Highly reusable

3. **Layout Components**
   - Define page structure
   - Manage responsive behavior
   - Provide consistent UI framework

4. **Feature Components**
   - Combine multiple components for specific features
   - Encapsulate feature-specific logic
   - Maintain internal state for the feature

### State Management

The application uses a combination of state management approaches:

1. **React Context API**
   - Global state management
   - Theme context for appearance settings
   - Authentication context for user state
   - Language context for internationalization

2. **Component State**
   - Local state using useState hook
   - Component-specific state
   - Form input state

3. **URL State**
   - Route parameters for navigation
   - Query parameters for filters and search

4. **Persistent State**
   - localStorage for user preferences
   - localStorage for authentication tokens
   - localStorage for view preferences

### Data Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  User Action    │────►│  State Update   │────►│   Re-render     │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       ▲                        │
        │                       │                        │
        ▼                       │                        ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Event Handler  │     │  Data Received  │     │   Side Effects  │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       ▲                        │
        │                       │                        │
        ▼                       │                        │
┌─────────────────┐     ┌─────────────────┐             │
│  API Service    │────►│  API Response   │◄────────────┘
└─────────────────┘     └─────────────────┘
```

1. User interactions trigger event handlers
2. Event handlers may call API services
3. API responses update application state
4. State changes trigger re-renders
5. Side effects may trigger additional API calls

## Code Organization

### Directory Structure

```
src/
├── components/           # Reusable UI components
│   ├── common/           # Shared components (buttons, inputs, etc.)
│   ├── layout/           # Layout components
│   └── feature/          # Feature-specific components
├── config/               # Configuration files
│   ├── api.config.js     # API endpoints and settings
│   └── translations.js   # Language translations
├── context/              # Context providers
│   ├── ThemeContext.js   # Theme management
│   └── AuthContext.js    # Authentication state
├── pages/                # Page components
│   ├── Dashboard.js      # Dashboard page
│   ├── Login.js          # Login page
│   └── requests/         # Request-related pages
├── services/             # API and utility services
│   ├── api.service.js    # API communication
│   └── auth.service.js   # Authentication logic
├── styles/               # CSS files
│   ├── global.css        # Global styles
│   └── variables.css     # CSS variables
├── utils/                # Utility functions
│   ├── date.utils.js     # Date formatting utilities
│   └── validation.utils.js # Validation utilities
├── App.js                # Main application component
├── App.css               # Main application styles
└── index.js              # Application entry point
```

### Module Dependencies

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│    Pages    │────►│  Components │────►│  Utilities  │
└─────────────┘     └─────────────┘     └─────────────┘
       │                   │                   ▲
       │                   │                   │
       ▼                   ▼                   │
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Context   │     │   Services  │────►│    Config   │
└─────────────┘     └─────────────┘     └─────────────┘
```

- Pages depend on Components and Context
- Components depend on Services and Utilities
- Services depend on Config
- Circular dependencies are avoided

## Authentication Architecture

### Authentication Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Login Form │────►│ Auth Service│────►│  API Call   │
└─────────────┘     └─────────────┘     └─────────────┘
                                               │
┌─────────────┐     ┌─────────────┐     ┌─────▼───────┐
│ Protected   │◄────┤ Auth Context│◄────┤ Store Token │
│  Routes     │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
```

1. User enters credentials in Login Form
2. Auth Service sends credentials to API
3. On successful authentication, token is stored in localStorage
4. Auth Context is updated with user information
5. Protected routes check Auth Context for access control

### Token Management

- JWT tokens are stored in localStorage
- Token expiration is checked on application startup
- Expired tokens trigger automatic logout
- Token is included in API request headers

## Routing Architecture

### Route Structure

```
/                           # Redirects to /dashboard or /login
├── /login                  # Login page
├── /dashboard              # Dashboard page
├── /requests               # Request list page
│   ├── /types              # Request type selection
│   ├── /platform-select    # Platform selection
│   ├── /platform           # Platform request form
│   ├── /database           # Database request form
│   ├── /adhoc              # Adhoc data request form
│   ├── /report             # Report request form
│   └── /detail             # Request detail page
├── /users                  # User management (admin only)
├── /reports                # Reports page
├── /settings               # Settings page
├── /notifications          # Notifications page
├── /askai                  # Ask AI page
└── /whitelist              # Whitelist platform
```

### Route Protection

- Public routes: `/login`
- Protected routes: All other routes
- Role-based protection:
  - Admin routes: `/users`
  - User routes: All non-admin routes

## API Integration

### API Service Pattern

```javascript
// api.service.js
import axios from 'axios';
import { API_CONFIG } from '../config/api.config';

const apiClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  headers: API_CONFIG.HEADERS,
  timeout: API_CONFIG.TIMEOUT
});

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    // Handle common errors (401, 403, etc.)
    if (error.response && error.response.status === 401) {
      // Handle unauthorized
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

const apiService = {
  get: (url, params) => apiClient.get(url, { params }),
  post: (url, data) => apiClient.post(url, data),
  put: (url, data) => apiClient.put(url, data),
  delete: (url) => apiClient.delete(url)
};

export default apiService;
```

### API Endpoint Organization

API endpoints are centralized in the `api.config.js` file:

```javascript
// api.config.js
export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:3014',
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  TIMEOUT: 30000, // 30 seconds
};

export const ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/ldaplogin',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile',
  },
  USERS: {
    // User endpoints
  },
  REQUEST: {
    // Request endpoints
  },
  NOTIFICATION: {
    // Notification endpoints
  }
};
```

## Theming Architecture

### Theme Implementation

The application uses a theme context to manage appearance:

```javascript
// ThemeContext.js
import React, { createContext, useState, useContext, useEffect } from 'react';

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    const savedTheme = localStorage.getItem('theme');
    return savedTheme || 'light';
  });

  useEffect(() => {
    localStorage.setItem('theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
```

### CSS Variables

Theme colors and styles are defined using CSS variables:

```css
/* variables.css */
:root {
  /* Light theme variables */
  --primary-color: #4a6cf7;
  --secondary-color: #6c757d;
  --background-color: #ffffff;
  --text-color: #333333;
  /* ... other variables */
}

[data-theme="dark"] {
  /* Dark theme variables */
  --primary-color: #5d7bf9;
  --secondary-color: #adb5bd;
  --background-color: #121212;
  --text-color: #f8f9fa;
  /* ... other variables */
}
```

## Internationalization Architecture

### Language Implementation

The application supports multiple languages through a language context:

```javascript
// LanguageContext.js
import React, { createContext, useState, useContext, useEffect } from 'react';
import translations from '../config/translations';

const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    const savedLanguage = localStorage.getItem('language');
    return savedLanguage || 'en';
  });

  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  const changeLanguage = (lang) => {
    setLanguage(lang);
  };

  const t = (key) => {
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
```

### Translation Structure

```javascript
// translations.js
export default {
  en: {
    'dashboard.title': 'Dashboard',
    'requests.title': 'Requests',
    'login.username': 'Username',
    'login.password': 'Password',
    'login.submit': 'Login',
    // ... other translations
  },
  vi: {
    'dashboard.title': 'Trang chủ',
    'requests.title': 'Yêu cầu',
    'login.username': 'Tên đăng nhập',
    'login.password': 'Mật khẩu',
    'login.submit': 'Đăng nhập',
    // ... other translations
  }
};
```

## Performance Considerations

### Optimization Techniques

1. **Code Splitting**
   - Route-based code splitting
   - Dynamic imports for large components

2. **Memoization**
   - React.memo for expensive components
   - useMemo for expensive calculations
   - useCallback for stable callbacks

3. **Virtualization**
   - Virtual scrolling for long lists
   - Pagination for large data sets

4. **Lazy Loading**
   - Lazy loading of images
   - Lazy loading of components

5. **Caching**
   - API response caching
   - localStorage for persistent data

## Security Architecture

### Security Measures

1. **Authentication**
   - JWT-based authentication
   - Token expiration and refresh
   - Secure token storage

2. **Authorization**
   - Role-based access control
   - Component-level permissions
   - Route protection

3. **Data Protection**
   - HTTPS for all API communication
   - Input validation and sanitization
   - XSS protection

4. **Session Management**
   - Automatic session timeout
   - Session invalidation on logout
   - Single active session policy

## Error Handling

### Error Handling Strategy

1. **API Error Handling**
   - Centralized error handling in API service
   - Error response normalization
   - Retry logic for transient errors

2. **UI Error Handling**
   - Error boundaries for component errors
   - Fallback UI for error states
   - User-friendly error messages

3. **Logging**
   - Error logging to console
   - Error reporting to backend (if available)
   - Error context for debugging

## Deployment Architecture

### Build Process

1. Create React App build process
2. Environment-specific configuration
3. Static asset optimization
4. Bundle size analysis

### Deployment Options

1. **Static Hosting**
   - AWS S3 + CloudFront
   - Netlify
   - Vercel

2. **Server-Based Hosting**
   - Node.js server
   - Nginx
   - Apache

## Future Architecture Considerations

1. **State Management Evolution**
   - Consider Redux for more complex state
   - Evaluate React Query for data fetching

2. **Performance Improvements**
   - Server-side rendering options
   - Progressive Web App features
   - Web Workers for heavy computations

3. **Scalability**
   - Micro-frontend architecture
   - Module federation
   - Design system implementation

4. **Accessibility**
   - WCAG compliance
   - Screen reader optimization
   - Keyboard navigation improvements
