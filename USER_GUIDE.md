# User Guide - Form Management

## Table of Contents
1. [Getting Started](#getting-started)
2. [Dashboard](#dashboard)
3. [Request Management](#request-management)
4. [Whitelist Platform](#whitelist-platform)
5. [User Management](#user-management)
6. [Notifications](#notifications)
7. [Settings](#settings)
8. [Ask AI](#ask-ai)
9. [Tips and Tricks](#tips-and-tricks)

## Getting Started

### Logging In
1. Navigate to the application URL
2. Enter your LDAP username and password
3. Click "Login"
4. Upon successful authentication, you'll be directed to the Dashboard

### Navigation
- Use the sidebar on the left to navigate between different sections
- Collapse/expand the sidebar using the arrow button at the top
- Use the search bar in the header to quickly find features
- Click on your profile in the sidebar to view your user information

### Theme and Language
- Toggle between light and dark themes using the moon/sun icon in the sidebar footer
- Change language between English and Vietnamese in the Settings page

## Dashboard

The Dashboard provides an overview of key metrics and recent activity:

### Key Metrics
- Total requests by status
- Recent activity
- Pending approvals (for approvers)
- Quick access to common actions

### Actions
- Create a new request using the "+ New Request" button
- View detailed metrics by clicking on the respective cards
- Navigate to specific request lists by clicking on status counts

## Request Management

### Creating a Request
1. Click the "+ New Request" button in the header
2. Select the request type:
   - Platform Access
   - Database Access
   - Adhoc Data
   - Report Access
3. Fill in the required information for your selected request type
4. Submit the request

### Viewing Requests
1. Navigate to "Requests" in the sidebar
2. View requests in either Grid or Kanban view (toggle in the top right)
3. Filter requests by:
   - Request Type
   - Status
   - Due Date
   - Search term
4. Sort requests by creation date (newest/oldest)
5. Adjust the number of items displayed per page

### Request Details
1. Click on any request to view its details
2. View request information, status, and history
3. Add comments if needed
4. Perform actions based on your role:
   - Users: View status, add comments
   - Approvers: Approve/reject, add comments
   - Admins: All actions, including editing

## Whitelist Platform

The Whitelist Platform allows you to query and analyze whitelist data:

### Query Tab
1. Enter SQL queries directly or use the AI prompt to generate queries
2. Run queries using the "Run Query" button
3. View results in the table below
4. Save frequently used queries for future use

### Query Builder Tab
1. Build complex queries visually without writing SQL
2. Add conditions using the "+ Rule" button
3. Group conditions with AND/OR operators
4. Create nested condition groups for complex logic
5. View the generated SQL in real-time

### History Tab
View and reuse your previously executed queries:
1. Click on any historical query to load it
2. View execution time and result count
3. Copy queries to clipboard

### Saved Queries Tab
Manage your saved queries:
1. Load saved queries with a single click
2. Delete queries you no longer need
3. Copy queries to clipboard

## User Management

*Note: This section is only available to administrators*

### Viewing Users
1. Navigate to "User Management" in the sidebar
2. View all users in the system
3. Filter and search for specific users

### Managing Users
1. Create new users with the "+ Add User" button
2. Edit existing users by clicking on them
3. Assign roles (Admin, Approver, User)
4. Manage user permissions

## Notifications

### Viewing Notifications
1. Click the bell icon in the header to view recent notifications
2. Navigate to "Notifications" in the sidebar for a full list
3. Notifications are color-coded by type:
   - Blue: Information
   - Green: Success
   - Yellow: Warning
   - Red: Error

### Managing Notifications
1. Click on a notification to view related content
2. Mark notifications as read/unread
3. Clear all notifications if needed

## Settings

### Personal Settings
1. Navigate to "Settings" in the sidebar
2. Update language preference (English/Vietnamese)
3. Configure notification preferences
4. Manage display preferences (items per page, default views)

### System Settings
*Note: Some settings are only available to administrators*
1. Configure system-wide defaults
2. Manage integration settings
3. View system information

## Ask AI

The Ask AI feature provides assistance with using the application:

1. Navigate to "Ask AI" in the sidebar
2. Type your question in natural language
3. Receive guidance, suggestions, and help with tasks
4. Use the AI to help generate queries for the Whitelist Platform

## Tips and Tricks

### Keyboard Shortcuts
- `Ctrl+/` or `Cmd+/`: Open search
- `Esc`: Close modals and dialogs
- `Ctrl+Enter` or `Cmd+Enter`: Submit forms

### Efficiency Tips
- Use the search feature to quickly navigate to specific features
- Save frequently used queries in the Whitelist Platform
- Use the Kanban view for a visual representation of request workflow
- Configure your preferred items per page to reduce pagination
- Utilize filters to quickly find relevant requests

### Troubleshooting
- If you encounter errors, try refreshing the page
- Clear your browser cache if you experience unusual behavior
- Contact your system administrator for persistent issues
- Use the Ask AI feature for help with specific tasks
