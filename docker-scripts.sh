#!/bin/bash

# Docker management scripts for Form Management App

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Production commands
prod_build() {
    print_info "Building production image..."
    docker-compose build frontend
    print_success "Production image built successfully!"
}

prod_start() {
    print_info "Starting production app..."
    docker-compose up -d frontend
    print_success "Production app started at http://localhost:3000"
}

prod_stop() {
    print_info "Stopping production app..."
    docker-compose down
    print_success "Production app stopped!"
}

prod_logs() {
    print_info "Showing production logs..."
    docker-compose logs -f frontend
}

# Development commands
dev_start() {
    print_info "Starting development app..."
    docker-compose --profile dev up -d frontend-dev
    print_success "Development app started at http://localhost:3001"
}

dev_stop() {
    print_info "Stopping development app..."
    docker-compose --profile dev down
    print_success "Development app stopped!"
}

dev_logs() {
    print_info "Showing development logs..."
    docker-compose logs -f frontend-dev
}

# Utility commands
status() {
    print_info "Container status:"
    docker-compose ps
}

clean() {
    print_warning "This will remove all containers, networks, and images!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning up Docker resources..."
        docker-compose down --rmi all -v
        docker system prune -f
        print_success "Cleanup completed!"
    else
        print_info "Cleanup cancelled."
    fi
}

health() {
    print_info "Checking application health..."
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        print_success "Production app is healthy!"
    else
        print_error "Production app is not responding!"
    fi
    
    if curl -f http://localhost:3001 > /dev/null 2>&1; then
        print_success "Development app is healthy!"
    else
        print_warning "Development app is not running or not responding."
    fi
}

# Help function
show_help() {
    echo "Form Management App - Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Production Commands:"
    echo "  prod:build    Build production Docker image"
    echo "  prod:start    Start production app (http://localhost:3000)"
    echo "  prod:stop     Stop production app"
    echo "  prod:logs     Show production app logs"
    echo ""
    echo "Development Commands:"
    echo "  dev:start     Start development app (http://localhost:3001)"
    echo "  dev:stop      Stop development app"
    echo "  dev:logs      Show development app logs"
    echo ""
    echo "Utility Commands:"
    echo "  status        Show container status"
    echo "  health        Check application health"
    echo "  clean         Clean up all Docker resources"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 prod:start     # Start production app"
    echo "  $0 dev:start      # Start development app"
    echo "  $0 status         # Check container status"
}

# Main script logic
main() {
    check_docker
    
    case "${1:-help}" in
        "prod:build")
            prod_build
            ;;
        "prod:start")
            prod_start
            ;;
        "prod:stop")
            prod_stop
            ;;
        "prod:logs")
            prod_logs
            ;;
        "dev:start")
            dev_start
            ;;
        "dev:stop")
            dev_stop
            ;;
        "dev:logs")
            dev_logs
            ;;
        "status")
            status
            ;;
        "health")
            health
            ;;
        "clean")
            clean
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
