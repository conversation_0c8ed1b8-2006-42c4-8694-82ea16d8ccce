from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..database import get_db
from ..models import Request, User, DataSource, Report, Activity, SystemStatus
from ..auth import get_current_user

router = APIRouter(prefix="/dashboard", tags=["dashboard"])

# Pydantic models for response data
class SummaryStats(BaseModel):
    pending_requests: int
    approved_this_month: int
    data_sources_count: int
    reports_count: int

class ActivityItem(BaseModel):
    id: int
    type: str  # 'approved', 'new', 'pending', etc.
    title: str
    description: str
    timestamp: datetime

class AccessItem(BaseModel):
    id: int
    name: str
    type: str  # 'database', 'report', etc.

class SystemStatusItem(BaseModel):
    name: str
    status: str  # 'online', 'offline', 'degraded'
    percentage: int  # 0-100

class SystemStatusResponse(BaseModel):
    overall_status: str  # 'operational', 'degraded', 'offline'
    last_checked: datetime
    services: List[SystemStatusItem]

class DashboardResponse(BaseModel):
    summary: SummaryStats
    recent_activity: List[ActivityItem]
    user_access: List[AccessItem]
    system_status: SystemStatusResponse

@router.get("/stats", response_model=SummaryStats)
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get summary statistics for the dashboard"""
    
    # Get current month's start date
    first_day_of_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    
    # Count pending requests
    pending_count = db.query(Request).filter(Request.status == "pending").count()
    
    # Count approved requests this month
    approved_count = db.query(Request).filter(
        Request.status == "approved",
        Request.updated_at >= first_day_of_month
    ).count()
    
    # Count data sources
    data_sources_count = db.query(DataSource).count()
    
    # Count reports
    reports_count = db.query(Report).count()
    
    return SummaryStats(
        pending_requests=pending_count,
        approved_this_month=approved_count,
        data_sources_count=data_sources_count,
        reports_count=reports_count
    )

@router.get("/activity", response_model=List[ActivityItem])
async def get_recent_activity(
    limit: int = Query(5, ge=1, le=20),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get recent activity for the dashboard"""
    
    # Get recent activities from the database
    activities = db.query(Activity).order_by(Activity.timestamp.desc()).limit(limit).all()
    
    result = []
    for activity in activities:
        activity_type = "pending"
        if "approved" in activity.action.lower():
            activity_type = "approved"
        elif "new" in activity.action.lower() or "added" in activity.action.lower():
            activity_type = "new"
        
        result.append(ActivityItem(
            id=activity.id,
            type=activity_type,
            title=activity.action,
            description=activity.description,
            timestamp=activity.timestamp
        ))
    
    return result

@router.get("/access", response_model=List[AccessItem])
async def get_user_access(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user access information for the dashboard"""
    
    # This would typically query a user_access or permissions table
    # For this example, we'll return mock data based on the current user
    
    # Get data sources the user has access to
    data_sources = db.query(DataSource).filter(
        DataSource.id.in_(current_user.data_source_access)
    ).all()
    
    # Get reports the user has access to
    reports = db.query(Report).filter(
        Report.id.in_(current_user.report_access)
    ).all()
    
    result = []
    
    # Add data sources to result
    for ds in data_sources:
        result.append(AccessItem(
            id=ds.id,
            name=ds.name,
            type="database"
        ))
    
    # Add reports to result
    for report in reports:
        result.append(AccessItem(
            id=report.id,
            name=report.name,
            type="report"
        ))
    
    return result

@router.get("/system-status", response_model=SystemStatusResponse)
async def get_system_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get system status information for the dashboard"""
    
    # Get the latest system status records
    status_records = db.query(SystemStatus).order_by(SystemStatus.checked_at.desc()).limit(3).all()
    
    services = []
    overall_status = "operational"
    
    for record in status_records:
        status = "online" if record.is_operational else "offline"
        percentage = 100 if record.is_operational else 0
        
        if not record.is_operational:
            overall_status = "degraded"
        
        services.append(SystemStatusItem(
            name=record.service_name,
            status=status,
            percentage=percentage
        ))
    
    # If no services are operational, set overall status to offline
    if all(service.status == "offline" for service in services):
        overall_status = "offline"
    
    return SystemStatusResponse(
        overall_status=overall_status,
        last_checked=datetime.now() - timedelta(minutes=5),  # Mock last checked time
        services=services
    )

@router.get("/", response_model=DashboardResponse)
async def get_dashboard_data(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all dashboard data in a single request"""
    
    # Get summary stats
    summary = await get_dashboard_stats(db, current_user)
    
    # Get recent activity
    activity = await get_recent_activity(5, db, current_user)
    
    # Get user access
    access = await get_user_access(db, current_user)
    
    # Get system status
    status = await get_system_status(db, current_user)
    
    return DashboardResponse(
        summary=summary,
        recent_activity=activity,
        user_access=access,
        system_status=status
    )