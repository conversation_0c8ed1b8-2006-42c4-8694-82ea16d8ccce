# Gợi ý chi tiết về hệ thống quản lý file

## Gi<PERSON>i thích chi tiết về hệ thống lưu trữ file

### Cách thức hoạt động của hệ thống lưu trữ file trên server

H<PERSON> thống lưu trữ file được đề xuất hoạt động theo mô hình lưu trữ file trực tiếp trên server backend, kết hợp với việc lưu trữ metadata trong cơ sở dữ liệu. <PERSON><PERSON><PERSON> là cách thức hoạt động chi tiết:

#### Lưu trữ vật lý

- **C<PERSON>u trúc thư mục phân cấp**: File được lưu trữ trong một cấu trúc thư mục có tổ chức, phân chia theo mục đích sử dụng và mối quan hệ với các yêu cầu (requests).

- **<PERSON><PERSON> tách theo ngày/tháng**: <PERSON><PERSON> tr<PERSON>h quá nhiều file trong một thư mục (c<PERSON> thể gây vấn đề về hiệu suất với hệ thống file), các file được phân tách theo thời gian (năm/tháng/ngày).

- **Tên file duy nhất**: Mỗi file được lưu với tên duy nhất bao gồm timestamp, user_id, tên gốc và UUID để đảm bảo không có xung đột tên file.

#### Quản lý metadata

- **Cơ sở dữ liệu quan hệ**: Thông tin về file (metadata) được lưu trong cơ sở dữ liệu PostgreSQL, cho phép tìm kiếm, lọc và quản lý hiệu quả.

- **Soft delete**: Khi xóa file, hệ thống không xóa ngay file vật lý mà chỉ đánh dấu trong cơ sở dữ liệu (deleted_at), giúp khôi phục nếu cần.

- **Tracking đầy đủ**: Hệ thống theo dõi mọi thao tác với file (upload, download, share, delete) thông qua bảng audit_logs.

### Ưu điểm của phương pháp lưu trữ trên server

#### Kiểm soát hoàn toàn

- **Toàn quyền kiểm soát**: Bạn có toàn quyền kiểm soát cách lưu trữ, mã hóa và quản lý file.

- **Tích hợp chặt chẽ**: Dễ dàng tích hợp với logic ứng dụng và các quy trình xử lý file.

- **Bảo mật tùy chỉnh**: Có thể triển khai các biện pháp bảo mật phù hợp với yêu cầu cụ thể của ứng dụng.

#### Đơn giản hóa kiến trúc

- **Không cần dịch vụ bên ngoài**: Không phụ thuộc vào dịch vụ lưu trữ bên thứ ba.

- **Triển khai nhanh chóng**: Không cần cấu hình và tích hợp với hệ thống lưu trữ riêng biệt.

- **Backup đơn giản**: Backup cùng với dữ liệu ứng dụng, không cần quy trình riêng.

#### Chi phí thấp

- **Không có chi phí bổ sung**: Không phải trả phí cho dịch vụ lưu trữ bên ngoài.

- **Tận dụng tài nguyên hiện có**: Sử dụng không gian đĩa có sẵn trên server.

### Cách xử lý các thách thức

#### Quản lý không gian đĩa

- **Monitoring**: Hệ thống giám sát không gian đĩa liên tục, cảnh báo khi sắp đầy.

- **Retention policy**: Chính sách lưu giữ tự động xóa file cũ hoặc không sử dụng sau một thời gian.

- **Nén file**: File có thể được nén để tiết kiệm không gian, đặc biệt là các file văn bản và bảng tính.

#### Hiệu suất

- **Chunked upload**: Chia nhỏ file lớn thành các chunk khi upload, giúp xử lý file lớn mà không gặp vấn đề về timeout hoặc memory.

- **Streaming download**: Sử dụng streaming để download file lớn, không tải toàn bộ file vào memory.

- **Background processing**: Xử lý file nặng (như quét virus, mã hóa) trong background tasks để không chặn request chính.

#### Bảo mật

- **Kiểm tra MIME type**: Xác thực loại file thực sự, không chỉ dựa vào phần mở rộng.

- **Quét virus**: Quét file trước khi lưu trữ để đảm bảo không chứa mã độc.

- **Mã hóa**: Mã hóa file nhạy cảm trên đĩa để bảo vệ dữ liệu.

- **Phân quyền chi tiết**: Kiểm soát ai có thể xem, tải xuống, chia sẻ hoặc xóa file.

### Quy trình xử lý file

#### Quy trình upload file

1. **Nhận file từ client**: Thông qua API endpoint `/files/upload` hoặc chunked upload cho file lớn.

2. **Xác thực file**: Kiểm tra kích thước, định dạng, và quét virus nếu cần.

3. **Tạo thư mục nếu cần**: Tạo cấu trúc thư mục dựa trên ngày hiện tại và request ID.

4. **Lưu file với tên duy nhất**: Sử dụng quy ước đặt tên để đảm bảo duy nhất.

5. **Tính toán checksum**: Tạo SHA-256 hash của file để kiểm tra tính toàn vẹn.

6. **Lưu metadata vào database**: Lưu thông tin về file vào bảng `files`.

7. **Liên kết với request**: Tạo bản ghi trong bảng `file_request_mapping`.

8. **Ghi audit log**: Ghi nhận hành động upload vào bảng `file_audit_logs`.

#### Quy trình download file

1. **Nhận request download**: Thông qua API endpoint `/files/{file_id}/download`.

2. **Kiểm tra quyền truy cập**: Xác minh người dùng có quyền download file.

3. **Tìm file trong database**: Lấy thông tin file từ bảng `files`.

4. **Kiểm tra tính toàn vẹn**: So sánh checksum để đảm bảo file không bị thay đổi.

5. **Stream file về client**: Sử dụng streaming để không tải toàn bộ file vào memory.

6. **Cập nhật thống kê**: Tăng download_count và cập nhật last_accessed_at.

7. **Ghi audit log**: Ghi nhận hành động download vào bảng `file_audit_logs`.

## 1. Kiến trúc hệ thống file

### Cấu trúc thư mục
```
/app_storage
├── uploads                  # Thư mục gốc cho tất cả file upload
│   ├── requests             # File liên quan đến requests
│   │   ├── request_123      # Thư mục cho request cụ thể
│   │   │   ├── original     # File gốc
│   │   │   └── processed    # File đã xử lý (nếu có)
│   ├── temp                 # File tạm thời
│   ├── shared               # File được chia sẻ giữa nhiều requests
│   └── templates            # Template files
├── backups                  # Backup files
└── encrypted                # File đã mã hóa (tách riêng để quản lý quyền)
```

### Quy ước đặt tên file
```
{timestamp}_{user_id}_{original_filename}_{uuid}.{extension}
```
Ví dụ: `20231125_123_report_data_550e8400-e29b-41d4-a716-************.xlsx`

## 2. Metadata và Database Schema

### Bảng `files`
```sql
CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(512) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(128) NOT NULL,
    extension VARCHAR(20),
    checksum VARCHAR(64),  -- SHA-256 hash
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key_id INTEGER,
    uploaded_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,  -- Soft delete
    is_public BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP
);
```

### Bảng `file_request_mapping`
```sql
CREATE TABLE file_request_mapping (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id),
    request_id INTEGER NOT NULL REFERENCES requests(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### Bảng `file_shares`
```sql
CREATE TABLE file_shares (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id),
    shared_by INTEGER NOT NULL REFERENCES users(id),
    shared_with INTEGER REFERENCES users(id),
    shared_with_email VARCHAR(255),  -- Cho external sharing
    permission VARCHAR(20) NOT NULL, -- read, edit, admin
    access_token VARCHAR(64),        -- Cho link sharing
    expires_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_accessed_at TIMESTAMP
);
```

### Bảng `file_versions`
```sql
CREATE TABLE file_versions (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id),
    version_number INTEGER NOT NULL,
    file_path VARCHAR(512) NOT NULL,
    file_size BIGINT NOT NULL,
    checksum VARCHAR(64),
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    comment TEXT
);
```

### Bảng `file_audit_logs`
```sql
CREATE TABLE file_audit_logs (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id),
    user_id INTEGER NOT NULL REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- upload, download, share, delete, etc.
    ip_address VARCHAR(45),
    user_agent TEXT,
    details JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## 3. API Endpoints chi tiết

### Upload File
```python
@router.post("/files/upload", response_model=FileResponse)
async def upload_file(
    request: Request,
    file: UploadFile = File(...),
    request_id: Optional[int] = Form(None),
    description: Optional[str] = Form(None),
    tags: Optional[List[str]] = Form(None),
    is_encrypted: bool = Form(False),
    encryption_key_id: Optional[int] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """
    Upload file với các options:
    - Liên kết với request
    - Thêm tags và mô tả
    - Mã hóa file nếu cần
    """
    # Implementation...
```

### Chunked Upload cho file lớn
```python
@router.post("/files/chunked-upload/start", response_model=ChunkedUploadResponse)
async def start_chunked_upload(
    filename: str = Form(...),
    file_size: int = Form(...),
    mime_type: str = Form(...),
    request_id: Optional[int] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """Bắt đầu quá trình upload file lớn theo chunks"""
    # Implementation...

@router.post("/files/chunked-upload/chunk")
async def upload_chunk(
    upload_id: str = Form(...),
    chunk_number: int = Form(...),
    chunk: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """Upload một chunk của file lớn"""
    # Implementation...

@router.post("/files/chunked-upload/complete", response_model=FileResponse)
async def complete_chunked_upload(
    upload_id: str = Form(...),
    current_user: User = Depends(get_current_user)
):
    """Hoàn thành quá trình upload file lớn"""
    # Implementation...
```

### File Management
```python
@router.get("/files", response_model=PaginatedFileList)
async def list_files(
    request_id: Optional[int] = None,
    search: Optional[str] = None,
    mime_type: Optional[str] = None,
    tags: Optional[List[str]] = Query(None),
    uploaded_by: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    sort_by: str = "created_at",
    sort_order: str = "desc",
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(get_current_user)
):
    """Lấy danh sách file với nhiều options lọc và sắp xếp"""
    # Implementation...

@router.get("/files/{file_id}", response_model=FileDetailResponse)
async def get_file_details(
    file_id: int,
    current_user: User = Depends(get_current_user)
):
    """Lấy thông tin chi tiết về file"""
    # Implementation...

@router.put("/files/{file_id}", response_model=FileResponse)
async def update_file_metadata(
    file_id: int,
    file_update: FileUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """Cập nhật metadata của file"""
    # Implementation...

@router.delete("/files/{file_id}")
async def delete_file(
    file_id: int,
    permanent: bool = False,
    current_user: User = Depends(get_current_user)
):
    """Xóa file (soft delete hoặc permanent)"""
    # Implementation...
```

### File Download và Preview
```python
@router.get("/files/{file_id}/download")
async def download_file(
    file_id: int,
    request: Request,
    version: Optional[int] = None,
    current_user: User = Depends(get_current_user)
):
    """Download file với tracking và kiểm tra quyền"""
    # Implementation...

@router.get("/files/{file_id}/preview")
async def preview_file(
    file_id: int,
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """Preview file (cho các định dạng hỗ trợ như PDF, image)"""
    # Implementation...

@router.get("/files/shared/{token}")
async def access_shared_file(
    token: str,
    request: Request
):
    """Truy cập file được chia sẻ qua link (không cần đăng nhập)"""
    # Implementation...
```

### File Sharing
```python
@router.post("/files/{file_id}/share", response_model=FileShareResponse)
async def share_file(
    file_id: int,
    share_request: FileShareRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Chia sẻ file với:
    - Người dùng nội bộ (qua user_id)
    - Email bên ngoài
    - Tạo public link
    """
    # Implementation...

@router.get("/files/shares", response_model=PaginatedShareList)
async def list_file_shares(
    file_id: Optional[int] = None,
    shared_by_me: bool = False,
    shared_with_me: bool = False,
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(get_current_user)
):
    """Lấy danh sách file được chia sẻ"""
    # Implementation...

@router.delete("/files/shares/{share_id}")
async def remove_file_share(
    share_id: int,
    current_user: User = Depends(get_current_user)
):
    """Hủy chia sẻ file"""
    # Implementation...
```

### File Versioning
```python
@router.post("/files/{file_id}/versions", response_model=FileVersionResponse)
async def upload_new_version(
    file_id: int,
    file: UploadFile = File(...),
    comment: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """Upload phiên bản mới của file"""
    # Implementation...

@router.get("/files/{file_id}/versions", response_model=List[FileVersionResponse])
async def list_file_versions(
    file_id: int,
    current_user: User = Depends(get_current_user)
):
    """Lấy danh sách các phiên bản của file"""
    # Implementation...
```

### File Audit
```python
@router.get("/files/{file_id}/audit", response_model=PaginatedAuditLogList)
async def get_file_audit_logs(
    file_id: int,
    action: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(get_current_user)
):
    """Lấy audit logs cho file"""
    # Implementation...
```

## 4. Tính năng nâng cao

### Xử lý file
```python
@router.post("/files/{file_id}/process", response_model=FileProcessResponse)
async def process_file(
    file_id: int,
    process_request: FileProcessRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Xử lý file với các options:
    - Chuyển đổi định dạng (PDF to Image, Excel to CSV, etc.)
    - Nén file
    - Trích xuất text (OCR)
    - Redact thông tin nhạy cảm
    """
    # Implementation...
```

### Mã hóa và bảo mật
```python
@router.post("/files/{file_id}/encrypt", response_model=FileResponse)
async def encrypt_file(
    file_id: int,
    encryption_request: FileEncryptionRequest,
    current_user: User = Depends(get_current_user)
):
    """Mã hóa file với key cụ thể"""
    # Implementation...

@router.post("/files/{file_id}/decrypt", response_model=FileResponse)
async def decrypt_file(
    file_id: int,
    current_user: User = Depends(get_current_user)
):
    """Giải mã file (nếu có quyền)"""
    # Implementation...
```

### Tìm kiếm và phân loại
```python
@router.get("/files/search", response_model=PaginatedFileList)
async def search_files(
    query: str,
    content_search: bool = False,  # Tìm trong nội dung file
    file_types: Optional[List[str]] = Query(None),
    date_range: Optional[str] = None,
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(get_current_user)
):
    """Tìm kiếm file với full-text search"""
    # Implementation...

@router.post("/files/batch-tag", response_model=BatchOperationResponse)
async def batch_tag_files(
    batch_request: BatchTagRequest,
    current_user: User = Depends(get_current_user)
):
    """Thêm tags cho nhiều file cùng lúc"""
    # Implementation...
```

## 5. Frontend Components

### File Upload Component
```jsx
const FileUpload = ({ requestId, onUploadComplete }) => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState({});
  const [errors, setErrors] = useState({});

  // Drag & drop functionality
  const onDrop = useCallback((acceptedFiles) => {
    setFiles(prev => [...prev, ...acceptedFiles.map(file => ({
      file,
      id: uuidv4(),
      status: 'pending'
    }))]);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: true
  });

  // Upload logic with progress tracking
  const uploadFiles = async () => {
    setUploading(true);

    const uploads = files.filter(f => f.status === 'pending').map(async (fileObj) => {
      const formData = new FormData();
      formData.append('file', fileObj.file);
      if (requestId) formData.append('request_id', requestId);

      try {
        const response = await axios.post('/api/files/upload', formData, {
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            setProgress(prev => ({
              ...prev,
              [fileObj.id]: percentCompleted
            }));
          }
        });

        return { id: fileObj.id, status: 'success', response: response.data };
      } catch (error) {
        return { id: fileObj.id, status: 'error', error: error.response?.data?.detail || 'Upload failed' };
      }
    });

    const results = await Promise.all(uploads);

    // Update file statuses
    setFiles(prev => prev.map(f => {
      const result = results.find(r => r.id === f.id);
      if (!result) return f;
      return { ...f, status: result.status, response: result.response, error: result.error };
    }));

    setUploading(false);

    // Notify parent component
    const successful = results.filter(r => r.status === 'success').map(r => r.response);
    if (successful.length > 0 && onUploadComplete) {
      onUploadComplete(successful);
    }
  };

  return (
    <div className="file-upload-container">
      <div {...getRootProps()} className="dropzone">
        <input {...getInputProps()} />
        <p>Drag & drop files here, or click to select files</p>
      </div>

      {files.length > 0 && (
        <div className="file-list">
          {files.map(fileObj => (
            <div key={fileObj.id} className={`file-item ${fileObj.status}`}>
              <div className="file-info">
                <span className="file-name">{fileObj.file.name}</span>
                <span className="file-size">{formatFileSize(fileObj.file.size)}</span>
              </div>

              {fileObj.status === 'pending' && progress[fileObj.id] && (
                <div className="progress-bar">
                  <div
                    className="progress"
                    style={{ width: `${progress[fileObj.id]}%` }}
                  />
                </div>
              )}

              {fileObj.status === 'error' && (
                <div className="error-message">{fileObj.error}</div>
              )}

              <button
                className="remove-btn"
                onClick={() => setFiles(prev => prev.filter(f => f.id !== fileObj.id))}
              >
                Remove
              </button>
            </div>
          ))}
        </div>
      )}

      <div className="actions">
        <button
          className="upload-btn"
          disabled={uploading || files.filter(f => f.status === 'pending').length === 0}
          onClick={uploadFiles}
        >
          {uploading ? 'Uploading...' : 'Upload Files'}
        </button>
      </div>
    </div>
  );
};
```

### File Manager Component
```jsx
const FileManager = ({ requestId }) => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0
  });

  // Fetch files
  const fetchFiles = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.page,
        page_size: pagination.pageSize,
        sort_by: sortBy,
        sort_order: sortOrder,
        search: searchTerm || undefined,
        mime_type: filterType || undefined
      };

      if (requestId) {
        params.request_id = requestId;
      }

      const response = await axios.get('/api/files', { params });
      setFiles(response.data.items);
      setPagination(prev => ({
        ...prev,
        total: response.data.total
      }));
    } catch (error) {
      console.error('Error fetching files:', error);
      // Show error notification
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.pageSize, sortBy, sortOrder, searchTerm, filterType, requestId]);

  useEffect(() => {
    fetchFiles();
  }, [fetchFiles]);

  // Handle file selection
  const toggleFileSelection = (fileId) => {
    setSelectedFiles(prev => {
      if (prev.includes(fileId)) {
        return prev.filter(id => id !== fileId);
      } else {
        return [...prev, fileId];
      }
    });
  };

  // Handle file download
  const downloadFile = async (fileId) => {
    try {
      const response = await axios.get(`/api/files/${fileId}/download`, {
        responseType: 'blob'
      });

      // Get filename from Content-Disposition header
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'download';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch && filenameMatch.length === 2) filename = filenameMatch[1];
      }

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Error downloading file:', error);
      // Show error notification
    }
  };

  // Render file grid/list
  const renderFiles = () => {
    if (loading) {
      return <div className="loading-spinner">Loading...</div>;
    }

    if (files.length === 0) {
      return (
        <div className="empty-state">
          <p>No files found</p>
        </div>
      );
    }

    if (viewMode === 'grid') {
      return (
        <div className="file-grid">
          {files.map(file => (
            <div
              key={file.id}
              className={`file-card ${selectedFiles.includes(file.id) ? 'selected' : ''}`}
              onClick={() => toggleFileSelection(file.id)}
            >
              <div className="file-icon">
                {getFileIcon(file.mime_type)}
              </div>
              <div className="file-info">
                <div className="file-name" title={file.original_filename}>
                  {file.original_filename}
                </div>
                <div className="file-meta">
                  <span>{formatFileSize(file.file_size)}</span>
                  <span>{formatDate(file.created_at)}</span>
                </div>
              </div>
              <div className="file-actions">
                <button onClick={(e) => { e.stopPropagation(); downloadFile(file.id); }}>
                  Download
                </button>
                <button onClick={(e) => { e.stopPropagation(); /* Show share dialog */ }}>
                  Share
                </button>
              </div>
            </div>
          ))}
        </div>
      );
    } else {
      return (
        <table className="file-table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedFiles.length === files.length}
                  onChange={() => {
                    if (selectedFiles.length === files.length) {
                      setSelectedFiles([]);
                    } else {
                      setSelectedFiles(files.map(f => f.id));
                    }
                  }}
                />
              </th>
              <th onClick={() => handleSort('original_filename')}>
                Name {sortBy === 'original_filename' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('file_size')}>
                Size {sortBy === 'file_size' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('created_at')}>
                Date {sortBy === 'created_at' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {files.map(file => (
              <tr
                key={file.id}
                className={selectedFiles.includes(file.id) ? 'selected' : ''}
              >
                <td>
                  <input
                    type="checkbox"
                    checked={selectedFiles.includes(file.id)}
                    onChange={() => toggleFileSelection(file.id)}
                  />
                </td>
                <td>
                  <div className="file-name-cell">
                    {getFileIcon(file.mime_type)}
                    <span>{file.original_filename}</span>
                  </div>
                </td>
                <td>{formatFileSize(file.file_size)}</td>
                <td>{formatDate(file.created_at)}</td>
                <td>
                  <div className="file-actions">
                    <button onClick={() => downloadFile(file.id)}>Download</button>
                    <button onClick={() => { /* Show share dialog */ }}>Share</button>
                    <button onClick={() => { /* Show more options */ }}>More</button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }
  };

  // Handle sorting
  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  return (
    <div className="file-manager">
      <div className="file-manager-header">
        <div className="search-filter">
          <input
            type="text"
            placeholder="Search files..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            <option value="">All file types</option>
            <option value="image/">Images</option>
            <option value="application/pdf">PDFs</option>
            <option value="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">
              Excel
            </option>
            <option value="text/">Text</option>
          </select>
        </div>

        <div className="view-controls">
          <button
            className={viewMode === 'grid' ? 'active' : ''}
            onClick={() => setViewMode('grid')}
          >
            Grid
          </button>
          <button
            className={viewMode === 'list' ? 'active' : ''}
            onClick={() => setViewMode('list')}
          >
            List
          </button>
        </div>
      </div>

      {selectedFiles.length > 0 && (
        <div className="batch-actions">
          <span>{selectedFiles.length} files selected</span>
          <button onClick={() => { /* Batch download */ }}>Download</button>
          <button onClick={() => { /* Batch share */ }}>Share</button>
          <button onClick={() => { /* Batch delete */ }}>Delete</button>
        </div>
      )}

      <div className="file-container">
        {renderFiles()}
      </div>

      <div className="pagination">
        <button
          disabled={pagination.page === 1}
          onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
        >
          Previous
        </button>
        <span>
          Page {pagination.page} of {Math.ceil(pagination.total / pagination.pageSize)}
        </span>
        <button
          disabled={pagination.page >= Math.ceil(pagination.total / pagination.pageSize)}
          onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
        >
          Next
        </button>
      </div>
    </div>
  );
};
```

## 6. Bảo mật và Hiệu suất

### Bảo mật File
1. **Kiểm tra MIME type**
   - Xác thực MIME type thực tế của file, không chỉ dựa vào extension
   - Chỉ cho phép các định dạng file an toàn

2. **Virus/Malware Scanning**
   - Quét file trước khi lưu trữ
   - Cách triển khai: ClamAV hoặc tích hợp với dịch vụ quét virus bên ngoài

3. **Mã hóa File**
   - Mã hóa file nhạy cảm tại rest
   - Sử dụng AES-256 với key rotation

4. **Kiểm soát truy cập**
   - Kiểm tra quyền truy cập trước mỗi thao tác file
   - Phân quyền chi tiết (read, write, share, delete)

### Hiệu suất
1. **Chunked Upload**
   - Cho phép upload file lớn theo chunks
   - Hỗ trợ resume upload khi bị gián đoạn

2. **Streaming Download**
   - Sử dụng streaming response thay vì tải toàn bộ file vào memory
   - Giảm memory usage trên server

3. **Caching**
   - Cache metadata file để giảm database queries
   - Sử dụng HTTP caching cho file tĩnh

4. **Background Processing**
   - Xử lý file nặng (scanning, encryption, processing) trong background tasks
   - Sử dụng Celery hoặc FastAPI background tasks

## 7. Triển khai và Mở rộng

### Triển khai
1. **Phân chia thư mục**
   - Phân chia file theo ngày/tháng để tránh quá nhiều file trong một thư mục
   - Ví dụ: `/uploads/2023/11/25/{file}`

2. **Backup Strategy**
   - Backup định kỳ cho cả file và metadata
   - Sử dụng incremental backup để tiết kiệm không gian

3. **Monitoring**
   - Giám sát disk usage
   - Cảnh báo khi disk space thấp
   - Theo dõi file access patterns

### Mở rộng trong tương lai
1. **Chuyển sang Object Storage**
   - Thiết kế để dễ dàng chuyển sang MinIO/S3 trong tương lai
   - Tách biệt logic xử lý file và storage backend

2. **Content Extraction**
   - Trích xuất text từ file để hỗ trợ full-text search
   - OCR cho file hình ảnh và PDF

3. **AI Integration**
   - Phân loại file tự động
   - Phát hiện thông tin nhạy cảm
   - Tóm tắt nội dung file
