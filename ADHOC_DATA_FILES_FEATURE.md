# Adhoc Data Files Feature

## Overview

This feature adds a new section to the Request Detail page that displays data files for requests with `request_type = 'adhoc-data'`. The section appears between the Request Information section and the Comments section, providing users with the ability to view and download data files associated with the request.

## Implementation Details

### API Integration

The feature uses the existing `FILES.GET_BY_REQUEST` API endpoint configured in `src/config/api.config.js`:

```javascript
FILES: {
  GET_BY_REQUEST: '/files/by_request',
  DOWNLOAD: (id) => `/files/download?file_id=${id}`,
  // ... other endpoints
}
```

### API Request Format

```javascript
GET /files/by_request?request_id={requestId}
```

### Expected API Response Format

```json
[
  {
    "id": 176,
    "filename": "20250529_130611_59342788-28b6-4a14-87c9-94b457c4f169.json",
    "original_filename": "openapi (11).json",
    "file_path": "uploads/user_files/2/20250529_130611_59342788-28b6-4a14-87c9-94b457c4f169.json",
    "file_size": 47783,
    "file_size_mb": 0.05,
    "mime_type": "application/json",
    "description": "Adhoc data file: openapi (11).json",
    "user_id": 2,
    "request_id": 12,
    "feedback_id": null,
    "status": "active",
    "created_at": "2025-05-29T13:06:11.900144",
    "updated_at": null
  }
]
```

## UI Components

### RequestDataFileSection Component

Located in `src/pages/requests/RequestDetail.js`, this component:

1. **Fetches data files** using the FILES.GET_BY_REQUEST API
2. **Displays file information** including:
   - Original filename
   - File size (formatted)
   - MIME type
   - Upload date
   - Description (if available)
3. **Provides download functionality** for individual files and bulk download
4. **Shows appropriate states**:
   - Loading state while fetching files
   - Empty state when no files are available
   - Error state if API call fails

### Features

#### Individual File Download
- Each file has a "Download" button
- Uses the FILES.DOWNLOAD API endpoint
- Creates a blob download link for the user

#### Bulk Download
- "Download All" button in the section header
- Downloads all files sequentially with a small delay between downloads
- Provides user feedback during the download process

#### File Type Icons
- Different colored icons based on MIME type:
  - JSON files: Orange
  - CSV files: Blue
  - Excel files: Green
  - PDF files: Red
  - Images: Green
  - Default: Gray

#### Responsive Design
- File items display in a clean card layout
- Hover effects for better user interaction
- Mobile-friendly responsive design

## Conditional Rendering

The Data Files section only appears when:
```javascript
request["request_base"].request_type === "adhoc-data"
```

This ensures the section is only shown for relevant request types.

## Styling

Custom CSS classes added to `src/styles/RequestDetail.css`:

- `.request-data-file-section` - Main section container
- `.data-files-list` - Container for file items
- `.data-file-item` - Individual file item styling
- `.btn-download-all` - Bulk download button styling
- `.btn-download` - Individual download button styling

## Error Handling

The component handles various error scenarios:

1. **API Errors**: Shows error message if file fetching fails
2. **Download Errors**: Shows alert if individual file download fails
3. **Empty State**: Shows appropriate message when no files are available
4. **Loading State**: Shows spinner while data is being fetched

## Testing

Test file: `src/pages/requests/__tests__/RequestDataFileSection.test.js`

Tests cover:
- Rendering data files section for adhoc-data requests
- Not rendering for other request types
- Displaying file information correctly
- Download button functionality
- Loading and empty states
- Error handling

## Usage Example

When viewing a request detail page for an adhoc-data request, users will see:

1. **Section Header**: "Data Files (X)" with a "Download All" button
2. **File List**: Each file showing:
   - File icon (based on type)
   - Original filename
   - File metadata (size, type, date)
   - Description (if available)
   - Download button
3. **Interactive Elements**:
   - Hover effects on file items
   - Click to download individual files
   - Click "Download All" for bulk download

## Browser Compatibility

The feature uses modern browser APIs:
- `window.URL.createObjectURL()` for blob downloads
- `document.createElement()` for dynamic link creation
- Standard fetch API through the existing apiService

Compatible with all modern browsers that support ES6+ features.

## Future Enhancements

Potential improvements could include:
- File preview functionality for supported formats
- Drag and drop file upload during request processing
- File versioning support
- Progress indicators for large file downloads
- File sharing capabilities
