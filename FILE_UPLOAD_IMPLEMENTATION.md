# File Upload Implementation for Feedback System

## Overview

Đã implement việc upload file thật cho trang Feedback với logic upload ngay khi chọn file (immediate upload) thay vì đợi đến khi submit form. <PERSON><PERSON><PERSON><PERSON> này cho phép hiển thị lỗi upload riêng biệt cho từng file và cải thiện UX.

## Changes Made

### 1. API Service Updates (`src/services/api.service.js`)

Thêm method `postFormData` để xử lý multipart/form-data:

```javascript
postFormData: (url, formData, onUploadProgress) => {
  return apiClient.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: onUploadProgress,
  });
}
```

### 2. API Configuration Updates (`src/config/api.config.js`)

Thêm endpoints mới:

```javascript
FEEDBACK: {
  CREATE: '/feedback/create',
  // ... existing endpoints
},
FILES: {
  UPLOAD: '/files/upload',                          // New endpoint
  DOWNLOAD: (id) => `/files/download?file_id=${id}`,
  DELETE: (id) => `/files/delete?file_id=${id}`,
}
```

### 3. Feedback Component Updates (`src/pages/Feedback.js`)

#### File Validation
- Maximum file size: 10MB
- Allowed types: JPG, PNG, GIF, PDF, DOC, DOCX, TXT
- Real-time validation when files are selected

#### Upload Process
1. **File Selection**: Validate files and add to attachments state
2. **Upload**: When form is submitted, upload files first using FormData
3. **Progress Tracking**: Real-time upload progress for each file
4. **Status Management**: Track file status (pending, uploading, uploaded, error)
5. **Feedback Submission**: After successful upload, submit feedback with file references

#### Key Features
- **Immediate Upload**: Files upload ngay khi được chọn
- **Individual Error Handling**: Mỗi file có error message riêng
- **Retry Functionality**: Button retry cho file upload failed
- **Real-time Progress**: Progress bar cho từng file
- **Smart Form Submission**: Block submit khi có file đang upload hoặc failed

#### New State Management
```javascript
const [attachments, setAttachments] = useState([]);

// Each attachment object now includes:
{
  id: unique_id,
  file: File_object,
  name: string,
  size: number,
  status: 'uploading' | 'uploaded' | 'error',
  uploadProgress: number,
  uploadedFileId: string | null,
  errorMessage: string | null
}
```

### 4. UI/UX Improvements (`src/styles/Feedback.css`)

#### Upload Progress Visualization
- Progress bars for each uploading file
- Status indicators (✓ Uploaded, ✗ Failed)
- Color-coded attachment items based on status
- Disabled remove button during upload

#### Visual States
- **Pending**: Default appearance
- **Uploading**: Green border, progress bar, percentage display
- **Uploaded**: Success background, checkmark
- **Error**: Error background, error icon

## Technical Flow

### 1. File Selection & Immediate Upload
```javascript
handleFileSelect(e) → validateFile() → createAttachmentObjects() → uploadSingleFile() for each file
```

### 2. Individual File Upload
```javascript
uploadSingleFile(attachment) →
  - Create FormData
  - Call apiService.postFormData()
  - Track progress with onUploadProgress callback
  - Update attachment status and progress
  - Handle success/error states individually
```

### 3. Form Submission
```javascript
handleSubmit() →
  - Check for uploading files (block submission)
  - Check for failed uploads (show error)
  - Get uploaded files
  - Submit feedback with file references
```

## Backend Requirements

Backend cần implement các endpoints sau:

### File Upload Endpoint
```
POST /files/upload
Content-Type: multipart/form-data

Body:
- file: File
- description: string (optional)

Response:
{
  "file_id": "unique_file_id",
  "filename": "original_filename",
  "size": file_size_in_bytes,
  "upload_date": "ISO_timestamp"
}
```

### Feedback Creation Endpoint
```
POST /feedback/create
Content-Type: application/json

Body:
{
  "subject": "string",
  "category": "string",
  "description": "string",
  "priority": "string",
  "attachments": [
    {
      "id": "file_id",
      "name": "filename",
      "size": file_size,
      "original_name": "original_filename"
    }
  ]
}
```

## Error Handling

1. **File Validation Errors**: Displayed immediately when files are selected
2. **Individual Upload Errors**: Each file shows specific error message
3. **Retry Mechanism**: Failed uploads can be retried with retry button
4. **Form Submission Blocking**:
   - Block submit if files are still uploading
   - Block submit if there are failed uploads (with clear error message)
5. **Network Errors**: Proper error messages with retry capability
6. **Form Validation**: Existing validation still applies

### Error States
- **Validation Error**: File too large or wrong type → Show error message
- **Upload Error**: Network/server error → Show error in file item with retry button
- **Submission Error**: Failed uploads present → Block form submission with instruction

## Security Considerations

1. **File Type Validation**: Both frontend and backend validation
2. **File Size Limits**: 10MB per file limit
3. **Authentication**: All uploads require valid user token
4. **File Storage**: Backend should implement secure file storage

## Testing

Để test functionality:

1. **File Selection**: Test với các file types khác nhau
2. **Upload Progress**: Test với file lớn để xem progress bar
3. **Error Handling**: Test với file quá lớn hoặc type không hỗ trợ
4. **Network Issues**: Test khi mất kết nối internet
5. **Multiple Files**: Test upload nhiều file cùng lúc

## Future Enhancements

1. **Drag & Drop**: Thêm drag & drop interface
2. **File Preview**: Preview image files trước khi upload
3. **Chunked Upload**: Cho file rất lớn (>50MB)
4. **Resume Upload**: Khả năng resume upload khi bị gián đoạn
5. **File Management**: View/download uploaded files
