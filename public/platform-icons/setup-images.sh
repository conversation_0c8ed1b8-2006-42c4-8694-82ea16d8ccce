#!/bin/bash

# This script helps you prepare the platform screenshots for use in the app

# Create the directory if it doesn't exist
mkdir -p public/platform-icons

echo "Setting up platform icons..."

# Copy default SVG if it doesn't exist
if [ ! -f public/platform-icons/default.svg ]; then
  echo "Creating default icon..."
  cat > public/platform-icons/default.svg << 'EOL'
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <rect width="200" height="200" fill="#f5f7fb"/>
  <path d="M100,60 L130,100 L100,140 L70,100 Z" fill="#4285F4"/>
  <circle cx="100" cy="100" r="20" fill="white"/>
  <text x="100" y="170" font-family="Arial" font-size="14" text-anchor="middle" fill="#666666">ZDS Platform</text>
</svg>
EOL
fi

# Instructions
echo "======================================"
echo "Platform icons setup complete!"
echo ""
echo "For best results, copy your screenshots to the following files:"
echo "- Whitelist Tool screenshot -> public/platform-icons/whitelist.jpeg"
echo "- Funnel Tool screenshot -> public/platform-icons/funnel.jpeg"
echo "- ZDSLab screenshot -> public/platform-icons/zdslab.jpeg"
echo "- Ads Report screenshot -> public/platform-icons/ads.jpeg"
echo "- SBV Report screenshot -> public/platform-icons/sbv.jpeg"
echo "- Merchant Campaign screenshot -> public/platform-icons/merchant.jpeg"
echo ""
echo "Make sure to optimize the images for web use (resize to ~450px width)"
echo "======================================" 