/* Global CSS Variables */
:root {
  /* Primary Colors */
  --primary-green: #00CF6A;
  --primary-blue: #0033C9;

  /* Secondary Colors */
  --secondary-light-blue: #00B4FF;
  --secondary-lime-green: #A1FF00;
  --secondary-purple: #6F0CDF;
  --secondary-yellow: #FFD729;
  --secondary-orange: #FF8A00;

  /* Background Colors */
  --bg-color: #f5f7fb;
  --content-bg: #ffffff;
  --hover-bg: #f5f7fb;
  --connection-bg: #f8f9fa;
  --filter-bg: #f8f9fa;

  /* Text Colors */
  --text-color: #333333;
  --text-secondary: #666666;
  --text-muted: #888888;

  /* Border and Input Colors */
  --border-color: #e0e0e0;
  --section-bg: #ffffff;
  --input-bg: #ffffff;
  --input-border: #ddd;

  /* Status Colors */
  --success-bg: #e7f7ed;
  --success-text: var(--primary-green);
  --error-bg: #ffeeee;
  --error-text: #d63031;

  /* Button Colors */
  --btn-primary-bg: var(--primary-blue);
  --btn-primary-hover: #002aa5;
  --btn-secondary-bg: #f5f5f5;
  --btn-secondary-hover: #e0e0e0;
  --btn-secondary-text: #333333;
  --btn-danger-bg: #f44336;
  --btn-danger-hover: #d32f2f;
  --btn-success-bg: var(--primary-green);
  --btn-success-hover: #00b85e;

  /* Other */
  --card-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  --filter-option-bg: rgba(0, 51, 201, 0.1);
  --filter-option-text: var(--primary-blue);
}

[data-theme="dark"] {
  /* Primary Colors */
  --primary-green: #00CF6A;
  --primary-blue: #0033C9;

  /* Secondary Colors */
  --secondary-light-blue: #00B4FF;
  --secondary-lime-green: #A1FF00;
  --secondary-purple: #6F0CDF;
  --secondary-yellow: #FFD729;
  --secondary-orange: #FF8A00;

  /* Background Colors */
  --bg-color: #1a1a1a;
  --content-bg: #111827;
  --section-bg: #1f2937;
  --hover-bg: #22272e;
  --connection-bg: #22272e;
  --filter-bg: #22272e;

  /* Text Colors */
  --text-color: #e6edf3;
  --text-secondary: #adbac7;
  --text-muted: #768390;

  /* Border and Input Colors */
  --border-color: #444c56;
  --section-bg: #22272e;
  --input-bg: #111827;
  --input-border: #444c56;

  /* Status Colors */
  --success-bg: rgba(0, 207, 106, 0.15);
  --success-text: var(--primary-green);
  --error-bg: #3a1e1e;
  --error-text: #ff5252;

  /* Button Colors */
  --btn-primary-bg: var(--primary-blue);
  --btn-primary-hover: #0040f0;
  --btn-secondary-bg: #333333;
  --btn-secondary-hover: #444444;
  --btn-secondary-text: #e6edf3;
  --btn-danger-bg: #f44336;
  --btn-danger-hover: #d32f2f;
  --btn-success-bg: var(--primary-green);
  --btn-success-hover: #00e676;

  /* Other */
  --card-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  --filter-option-bg: rgba(0, 51, 201, 0.2);
  --filter-option-text: #6b8aff;
}

/* Secondary Color Classes */
.color-light-blue {
  color: var(--secondary-light-blue);
}

.color-lime-green {
  color: var(--secondary-lime-green);
}

.color-purple {
  color: var(--secondary-purple);
}

.color-yellow {
  color: var(--secondary-yellow);
}

.color-orange {
  color: var(--secondary-orange);
}

.bg-light-blue {
  background-color: var(--secondary-light-blue);
}

.bg-lime-green {
  background-color: var(--secondary-lime-green);
}

.bg-purple {
  background-color: var(--secondary-purple);
}

.bg-yellow {
  background-color: var(--secondary-yellow);
}

.bg-orange {
  background-color: var(--secondary-orange);
}

/* Reset CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Segoe UI', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Main Layout */
.app-container {
  display: flex;
  min-height: 100vh;
  position: relative;
}

/* Content Area */
.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  transition: margin-left 0.3s ease, background-color 0.3s ease;
  margin-left: 280px;
}

.sidebar-collapsed .content {
  margin-left: 80px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 24px;
  background-color: var(--content-bg);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
}

.content-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-actions {
  display: flex;
  gap: 15px;
}

/* Common Button Styles */
.btn-primary,
.btn-secondary,
.btn-danger,
.btn-success {
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background-color: var(--btn-primary-bg);
  color: white;
}

.btn-primary:hover {
  background-color: var(--btn-primary-hover);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

.btn-secondary:hover {
  background-color: var(--btn-secondary-hover);
}

.btn-danger {
  background-color: var(--btn-danger-bg);
  color: white;
}

.btn-danger:hover {
  background-color: var(--btn-danger-hover);
}

.btn-success {
  background-color: var(--btn-success-bg);
  color: white;
}

.btn-success:hover {
  background-color: var(--btn-success-hover);
}

/* Secondary Color Buttons */
.btn-light-blue {
  background-color: var(--secondary-light-blue);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-light-blue:hover {
  background-color: #0095d9;
}

.btn-lime-green {
  background-color: var(--secondary-lime-green);
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-lime-green:hover {
  background-color: #8be000;
}

.btn-purple {
  background-color: var(--secondary-purple);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-purple:hover {
  background-color: #5c0ab8;
}

.btn-yellow {
  background-color: var(--secondary-yellow);
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-yellow:hover {
  background-color: #e6c000;
}

.btn-orange {
  background-color: var(--secondary-orange);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-orange:hover {
  background-color: #e67a00;
}


.btn-save,
.btn-reset {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-save {
  background-color: var(--btn-primary-bg);
  color: white;
}

.btn-save:hover {
  background-color: var(--btn-primary-hover);
}

.btn-reset {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

.btn-reset:hover {
  background-color: var(--btn-secondary-hover);
}

.btn-icon-left {
  font-size: 18px;
}

.btn-delete {
  background: none;
  border: none;
  color: var(--error-text);
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s;
}

.btn-delete:hover {
  color: var(--error-text);
  opacity: 0.8;
}


.btn-add {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--btn-primary-bg);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-add:hover {
  background-color: var(--btn-primary-hover);
}


/* Filter Settings Styles */
.filters-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.platform-filters {
  background-color: var(--input-bg);
  padding: 15px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.platform-filters h4 {
  margin: 0 0 15px 0;
  color: var(--text-color);
  font-size: 16px;
  text-transform: capitalize;
}

/* .filter-group {
  margin-bottom: 15px;
} */

.filter-group h5 {
  margin: 0 0 10px 0;
  color: var(--text-color);
  font-size: 14px;
  text-transform: capitalize;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  background-color: var(--filter-option-bg);
  color: var(--filter-option-text);
  border-radius: 12px;
  font-size: 13px;
}

.btn-remove-option {
  background: none;
  border: none;
  color: var(--text-color);
  opacity: 0.7;
  cursor: pointer;
  padding: 0;
  font-size: 16px;
  line-height: 1;
  transition: all 0.3s;
}

.btn-remove-option:hover {
  color: var(--error-text);
  opacity: 1;
}

.add-option input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.add-option input:focus {
  outline: none;
  border-color: var(--btn-primary-bg);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}



.btn-back {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f4f4f4;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-back:hover {
  background-color: #e0e0e0;
}

.btn-back svg {
  margin-right: 5px;
}



/* Common Search Box */
.search-box {
  position: relative;
  align-content: center;
}

.search-box input {
  padding: 8px 12px 8px 35px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 14px;
  width: 220px;
  transition: all 0.3s;
  outline: none;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.search-box input:focus {
  width: 280px;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 16px;
}

.content-body {
  background-color: var(--content-bg);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  padding: 24px;
  min-height: calc(100vh - 140px);
  transition: all 0.3s ease;
}

/* Common Button Styles */
.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.btn-icon:hover {
  background-color: #f5f5f5;
}

/* Common Status and Priority Badges */
.status-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green);
}

.status-badge.inactive {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

.status-badge.remove {
  background-color: #f2d4d4;
  color: #eb6464;
}

.status-badge.new {
  background-color: rgba(0, 51, 201, 0.1);
  color: var(--primary-blue);
}

.status-badge.pending {
  background-color: #fff8e1;
  color: #ffc107;
}

.status-badge.approved {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green);
}

.status-badge.rejected {
  background-color: #ffebee;
  color: #f44336;
}

.status-badge.in_progress {
  background-color: rgba(0, 51, 201, 0.1);
  color: var(--primary-blue);
}

.status-badge.done {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green);
}

.status-badge.closed {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

/* Secondary Color Badges */
.badge-light-blue {
  background-color: rgba(0, 180, 255, 0.15);
  color: var(--secondary-light-blue);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.badge-lime-green {
  background-color: rgba(161, 255, 0, 0.15);
  color: var(--secondary-lime-green);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.badge-purple {
  background-color: rgba(111, 12, 223, 0.15);
  color: var(--secondary-purple);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.badge-yellow {
  background-color: rgba(255, 215, 41, 0.15);
  color: var(--secondary-yellow);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.badge-orange {
  background-color: rgba(255, 138, 0, 0.15);
  color: var(--secondary-orange);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

/* Dark theme support for status badges */
[data-theme="dark"] .status-badge.new {
  background-color: rgba(0, 51, 201, 0.2);
  color: #6b8aff;
}

[data-theme="dark"] .status-badge.pending {
  background-color: #2d2a1a;
  color: #ffd54f;
}

[data-theme="dark"] .status-badge.reviewed {
  background-color: rgba(0, 151, 167, 0.2);
  color: #4dd0e1;
}

[data-theme="dark"] .status-badge.approved {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green);
}

[data-theme="dark"] .status-badge.rejected {
  background-color: #3d1a1a;
  color: #e57373;
}

[data-theme="dark"] .status-badge.in_progress {
  background-color: rgba(0, 51, 201, 0.2);
  color: #6b8aff;
}

[data-theme="dark"] .status-badge.done {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green);
}

[data-theme="dark"] .status-badge.closed {
  background-color: #2d333b;
  color: #adbac7;
}

[data-theme="dark"] .status-badge.active {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green);
}

/* Dark theme support for secondary color badges */
[data-theme="dark"] .badge-light-blue {
  background-color: rgba(0, 180, 255, 0.2);
}

[data-theme="dark"] .badge-lime-green {
  background-color: rgba(161, 255, 0, 0.2);
}

[data-theme="dark"] .badge-purple {
  background-color: rgba(111, 12, 223, 0.2);
}

[data-theme="dark"] .badge-yellow {
  background-color: rgba(255, 215, 41, 0.2);
}

[data-theme="dark"] .badge-orange {
  background-color: rgba(255, 138, 0, 0.2);
}

.priority-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.priority-badge.high {
  background-color: #ffebee;
  color: #f44336;
}

.priority-badge.medium {
  background-color: rgba(0, 51, 201, 0.1);
  color: var(--primary-blue);
}

.priority-badge.low {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green);
}

/* Common Utilities */
.clickable {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.clickable:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.clickable-row {
  cursor: pointer;
}

.clickable-row:hover {
  background-color: var(--hover-bg);
}

.loading-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: var(--btn-primary-bg);
  background-color: var(--bg-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Pagination - Common across multiple components */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 5px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  background-color: var(--content-bg);
  color: var(--text-color);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.pagination-btn:hover {
  background-color: var(--hover-bg);
}

.pagination-btn.active {
  background-color: var(--btn-primary-bg);
  color: white;
  border-color: var(--btn-primary-bg);
}

/* Search Results Dropdown - Used in global search */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 5px;
  background-color: var(--content-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.search-result-item {
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: var(--hover-bg);
}

.search-result-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-color);
  margin-bottom: 4px;
}

.search-result-description {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Dark Theme Support for Common Elements */
[data-theme="dark"] .content-header {
  background-color: var(--content-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .content-header h1 {
  color: var(--text-color);
}

[data-theme="dark"] .content-body {
  background-color: var(--content-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .btn-icon:hover {
  background-color: #444c56;
}

[data-theme="dark"] .search-results {
  background-color: #2d333b;
  border-color: #444c56;
}

[data-theme="dark"] .search-result-item {
  border-color: #373e47;
}

[data-theme="dark"] .search-result-item:hover {
  background-color: #22272e;
}

[data-theme="dark"] .search-result-title {
  color: #e6edf3;
}

[data-theme="dark"] .search-result-description {
  color: #adbac7;
}

/* Media Queries for Responsive Design */
@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 16px 20px;
  }

  .header-actions {
    flex-wrap: wrap;
    width: 100%;
  }

  .search-box {
    order: 3;
    width: 100%;
    margin-top: 10px;
  }

  .search-box input {
    width: 100%;
  }

  .search-box input:focus {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 15px;
  }

  .content-header {
    padding: 15px;
    margin-bottom: 15px;
  }

  .content-body {
    padding: 15px;
    margin-bottom: 15px;
  }
}

/* Request Form Styles */
.request-form-container {
  background-color: var(--content-bg);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  padding: 24px;
  /* max-width: 800px; */
  margin: 0 auto;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.request-form-container h2 {
  margin-bottom: 24px;
  color: var(--text-color);
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--btn-primary-bg);
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

/* Style for readonly fields */
.form-group input.readonly-field {
  background-color: var(--hover-bg);
  color: var(--text-secondary);
  cursor: not-allowed;
  border-color: var(--border-color);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

/* Error message styling */
.error-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
  gap: 10px;
  background-color: var(--error-bg);
  color: var(--error-text);
  /* border-left: 4px solid var(--error-text); */
  animation: fadeIn 0.3s ease-in-out;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.error-message svg {
  color: var(--error-text);
  font-size: 20px;
  flex-shrink: 0;
}

/* Success message styling */
.success-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
  gap: 10px;
  background-color: var(--success-bg);
  color: var(--success-text);
  /* border-left: 4px solid var(--success-text); */
  animation: fadeIn 0.3s ease-in-out;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.success-message svg {
  color: var(--success-text);
  font-size: 20px;
  flex-shrink: 0;
}

/* .error-message {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 20px;
  color: #cf1322;
}

.error-message p {
  margin: 0;
} */



.toggle-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--input-border);
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--btn-primary-bg);
}

input:disabled + .toggle-slider {
  background-color: var(--input-border);
  cursor: not-allowed;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}


/* Dark theme adjustments */
.dark-theme .request-form-container {
  background-color: #1f1f1f;
}

.dark-theme .request-form-container h2 {
  color: #f0f0f0;
}

.dark-theme .form-group label {
  color: #f0f0f0;
}

.dark-theme .form-group input,
.dark-theme .form-group select,
.dark-theme .form-group textarea {
  background-color: #2a2a2a;
  border-color: #444;
  color: #f0f0f0;
}

.dark-theme .error-message {
  background-color: #2a1215;
  border-color: #5c262c;
  color: #ff7875;
}

/* Common Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: var(--content-bg);
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--card-shadow);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-weight: 500;
  color: var(--text-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-muted);
  transition: color 0.3s ease;
}

.modal-close:hover {
  color: var(--text-color);
}

.modal-body {
  padding: 20px;
  color: var(--text-color);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Modal error message styling */
.modal-error-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
  gap: 10px;
  background-color: var(--error-bg);
  color: var(--error-text);
  border: 1px solid var(--error-text);
  animation: fadeIn 0.3s ease-in-out;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.modal-error-message .error-icon {
  color: var(--error-text);
  font-size: 20px;
  flex-shrink: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark theme support for modals */
[data-theme="dark"] .modal-container {
  background-color: #2d333b;
  color: #e6edf3;
}

[data-theme="dark"] .modal-header {
  border-color: #444c56;
}

[data-theme="dark"] .modal-close {
  color: #adbac7;
}

[data-theme="dark"] .modal-error-message {
  background-color: #2a1215;
  border-color: #5c262c;
  color: #ff7875;
}

[data-theme="dark"] .modal-error-message .error-icon {
  color: #ff7875;
}
