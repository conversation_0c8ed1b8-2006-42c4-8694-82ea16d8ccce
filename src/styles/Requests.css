.request-page-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  /* margin-bottom: 20px; */
  padding: 0 0 15px;
  /* border-bottom: 1px solid var(--border-color); */
}

.request-filters {
  display: flex;
  gap: 15px;
  align-items: center;
}

.request-filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.request-filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

.request-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-color);
  min-width: 120px;
}

.request-search {
  position: relative;
  align-content: center;
  margin-left: auto;
}

.request-search-container {
  position: relative;
}

.request-search-input {
  padding: 8px 12px 8px 35px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 14px;
  width: 260px;
  transition: all 0.3s;
  outline: none;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.request-search-input:focus {
  width: 320px;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 16px;
}

.request-actions {
  display: flex;
  gap: 10px;
  margin-left: 10px;
}

.request-btn-refresh, .request-btn-clear, .request-btn-view-toggle {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 8px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  transition: all 0.2s ease;
}

.request-btn-refresh, .request-btn-view-toggle {
  font-size: 18px;
  padding: 5px;
}

.request-btn-refresh:hover, .request-btn-clear:hover, .request-btn-view-toggle:hover {
  background-color: var(--hover-bg);
  color: var(--primary-blue);
}

.request-btn-view-toggle.active {
  color: var(--primary-blue);
  background-color: rgba(0, 51, 201, 0.1);
}

.request-btn-clear:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.request-status-count {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  margin-top: 15px;
  padding-bottom: 5px;
  padding-top: 2px;
  width: 100%;
}

.request-status-btn {
  padding: 8px 16px;
  border-radius: 20px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.request-status-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Default style for the All button */
.request-status-btn.btn-all {
  background-color: #f0f2f5;
  color: #333;
}

.request-status-btn.btn-all.active {
  background-color: #4c6ef5;
  color: white;
}

/* Pending button */
.request-status-btn.btn-pending {
  background-color: #fff8e1;
  color: #856404;
}

.request-status-btn.btn-pending.active {
  background-color: #ffc107;
  color: white;
}

/* Reviewed button */
.request-status-btn.btn-reviewed {
  background-color: #e0f7fa;
  color: #0097a7;
}

.request-status-btn.btn-reviewed.active {
  background-color: #0097a7;
  color: white;
}

/* Approved button */
.request-status-btn.btn-approved {
  background-color: #e8f5e9;
  color: #155724;
}

.request-status-btn.btn-approved.active {
  background-color: #4caf50;
  color: white;
}

/* In Progress button */
.request-status-btn.btn-progress {
  background-color: #e3f2fd;
  color: #004085;
}

.request-status-btn.btn-progress.active {
  background-color: #2196f3;
  color: white;
}

/* Done button */
.request-status-btn.btn-done {
  background-color: #d1e7dd;
  color: #0f5132;
}

.request-status-btn.btn-done.active {
  background-color: #198754;
  color: white;
}

/* Rejected button */
.request-status-btn.btn-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.request-status-btn.btn-rejected.active {
  background-color: #dc3545;
  color: white;
}

/* Kanban View Styles */
.kanban-view {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  height: calc(100vh - 250px); /* Cố định chiều cao */
  min-height: 500px;
  max-height: 800px;
}

.kanban-column {
  min-width: 300px;
  width: 300px;
  background-color: var(--section-bg);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  display: flex;
  flex-direction: column;
  height: 100%; /* Đảm bảo chiều cao đầy đủ */
  max-height: 100%;
  flex-shrink: 0; /* Ngăn chặn co lại */
}

.kanban-column-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kanban-column-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.kanban-column-count {
  background-color: var(--hover-bg);
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 10px;
}

.kanban-column-content {
  padding: 10px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0; /* Quan trọng cho overflow-y hoạt động */
  max-height: calc(100% - 60px); /* Trừ đi chiều cao của header */
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.kanban-column-content::-webkit-scrollbar {
  width: 4px;
}

.kanban-column-content::-webkit-scrollbar-track {
  background: transparent;
}

.kanban-column-content::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
}

.kanban-item {
  background-color: var(--content-bg);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 12px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.kanban-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.kanban-item-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.kanban-item-info {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.kanban-item-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 11px;
  color: var(--text-muted);
  margin-top: 8px;
}

.kanban-empty {
  padding: 20px;
  text-align: center;
  color: var(--text-muted);
  font-size: 14px;
}

/* Status colors for kanban columns */
.kanban-column.pending .kanban-column-header {
  border-top: 3px solid #ffc107;
}

.kanban-column.reviewed .kanban-column-header {
  border-top: 3px solid #0097a7;
}

.kanban-column.approved .kanban-column-header {
  border-top: 3px solid #4caf50;
}

.kanban-column.in_progress .kanban-column-header {
  border-top: 3px solid #2196f3;
}

.kanban-column.done .kanban-column-header {
  border-top: 3px solid #198754;
}

.kanban-column.rejected .kanban-column-header {
  border-top: 3px solid #dc3545;
}

/* Dark mode support for kanban */
[data-theme="dark"] .kanban-column {
  background-color: var(--section-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .kanban-item {
  background-color: var(--content-bg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .kanban-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.request-empty {
  text-align: center;
  padding: 40px;
  background-color: var(--section-bg);
  border-radius: 8px;
  color: var(--text-secondary);
}

.request-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.request-item {
  background-color: var(--section-bg);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  padding: 16px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.request-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.request-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.request-item-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  word-break: break-word;
  color: var(--text-color);
}

.request-item-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.request-status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.request-status-reviewed {
  background-color: #e0f7fa;
  color: #0097a7;
}

.request-status-approved {
  background-color: #d4edda;
  color: #155724;
}

.request-status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.request-status-progress {
  background-color: #cce5ff;
  color: #004085;
}

.request-status-done {
  background-color: #d1e7dd;
  color: #0f5132;
}

.request-item-info {
  margin-bottom: 12px;
  font-size: 14px;
}

.request-item-info p {
  margin: 5px 0;
}

.request-item-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.request-item-footer {
  display: flex;
  justify-content: flex-end;
  font-size: 12px;
  color: var(--text-muted);
}

@media (max-width: 900px) {
  .request-page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .request-filters {
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 15px;
  }

  .request-search {
    width: 100%;
    margin-left: 0;
    margin-bottom: 15px;
  }

  .request-search-input,
  .request-search-input:focus {
    width: 100%;
  }

  .request-actions {
    margin-left: 0;
  }
}

/* Pagination styling */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  background-color: var(--content-bg);
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.pagination {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-btn {
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--content-bg);
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--hover-bg);
  transform: translateY(-2px);
}

.pagination-btn.active {
  background-color: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
  box-shadow: 0 4px 10px rgba(0, 51, 201, 0.2);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Dark mode support for pagination */
[data-theme="dark"] .pagination-container {
  background-color: var(--content-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .pagination-btn {
  background-color: var(--content-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

[data-theme="dark"] .pagination-btn:hover:not(:disabled) {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .pagination-btn.active {
  background-color: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
  box-shadow: 0 4px 10px rgba(0, 51, 201, 0.3);
}

@media (max-width: 768px) {
  .request-list {
    grid-template-columns: 1fr;
  }

  .request-filter-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .request-select {
    width: 100%;
  }

  .request-status-count {
    flex-wrap: wrap;
  }

  .pagination-container {
    flex-direction: column;
    gap: 15px;
  }

  .pagination {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
}