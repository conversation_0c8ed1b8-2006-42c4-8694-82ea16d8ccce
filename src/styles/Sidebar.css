/* Modern Sidebar Styles */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, var(--content-bg) 0%, var(--content-bg) 100%);
  height: 100vh;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible;
  z-index: 100;
  border-right: 1px solid var(--border-color);
}

.collapse-sidebar-fixed-btn {
  position: absolute;
  top: 36px;
  right: -12px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #6c5ce7;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  z-index: 101;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
  transition: all 0.3s ease;
}

.collapse-sidebar-fixed-btn:hover {
  background-color: #5b4bc9;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

[data-theme="dark"] .collapse-sidebar-fixed-btn {
  background-color: #6c5ce7;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

[data-theme="dark"] .collapse-sidebar-fixed-btn:hover {
  background-color: #5b4bc9;
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

.sidebar.collapsed .collapse-sidebar-fixed-btn {
  right: -12px;
  top: 36px;
}

.sidebar.collapsed {
  width: 80px;
}

/* Inner Content */
.sidebar-inner {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.sidebar-header {
  padding: 24px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
}

.sidebar.collapsed .sidebar-header {
  flex-direction: column;
  gap: 16px;
  padding: 20px 10px;
}

.sidebar.collapsed .sidebar-toggle {
  background-color: var(--hover-bg);
  width: 36px;
  height: 36px;
  margin-top: 8px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 100%;
}

.logo-container:hover {
  background-color: var(--hover-bg);
  transform: translateY(-2px);
}

.sidebar-logo {
  width: 36px;
  height: 36px;
  object-fit: contain;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.sidebar.collapsed .sidebar-logo {
  width: 42px;
  height: 42px;
  margin: 0 auto;
}

.logo {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(90deg, var(--primary-green) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  transition: all 0.3s ease;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 18px;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background-color: var(--hover-bg);
  color: var(--primary-blue);
  transform: scale(1.1);
}

/* User Info Styles */
.user-info {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 10px;
}

.user-info:hover {
  background-color: var(--hover-bg);
  transform: translateY(-2px);
}

.user-avatar {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  /* background: linear-gradient(135deg, var(--primary-blue-light) 0%, var(--hover-bg) 100%); */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.user-avatar-icon {
  width: 32px;
  height: 32px;
  color: var(--secondary-purple);
  transition: all 0.3s ease;
}

.user-details {
  flex: 1;
  min-width: 0;
  transition: all 0.3s ease;
}

.user-details h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-details p {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 4px 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar.collapsed .user-details {
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.sidebar.collapsed .user-avatar {
  width: 42px;
  height: 42px;
  margin: 0 auto;
}

/* Divider */
.sidebar-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
  margin: 8px 20px;
  opacity: 0.6;
}

/* Menu Styles */
.sidebar-menu {
  flex: 1;
  padding: 12px 10px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
  min-height: 0;
}

.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
}

.sidebar-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  position: relative;
  margin-bottom: 4px;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.menu-item-content {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.sidebar-menu li:hover {
  background-color: var(--hover-bg);
}

.sidebar-menu li.active {
  background: linear-gradient(90deg, rgba(0, 51, 201, 0.08) 0%, rgba(0, 51, 201, 0.03) 100%);
}

.active-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: linear-gradient(180deg, var(--primary-blue) 0%, var(--secondary-purple) 100%);
  border-radius: 4px 0 0 4px;
  transition: all 0.3s ease;
}

.menu-icon {
  font-size: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.sidebar-menu li.active .menu-icon {
  color: var(--secondary-purple);
}

.menu-label {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-color);
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-menu li.active .menu-label {
  color: var(--secondary-purple);
  font-weight: 600;
}

.sidebar.collapsed .menu-label {
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.sidebar.collapsed .sidebar-menu li {
  display: flex;
  justify-content: center;
  padding: 12px 0;
}

.sidebar.collapsed .menu-item-content {
  padding: 0px 0;
  justify-content: center;
  gap: 0;
}

.sidebar.collapsed .active-indicator {
  right: 0;
  width: 4px;
  height: 60%;
}

/* Footer Styles */
.sidebar-footer {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
  position: sticky;
  bottom: 0;
  background-color: var(--content-bg);
  z-index: 10;
}

.sidebar.collapsed .sidebar-footer {
  padding: 16px 10px;
  align-items: center;
}

.theme-toggle-btn, .logout-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  background: none;
  border: 1px solid transparent;
  padding: 12px 16px;
  text-align: left;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
  color: var(--text-color);
  width: 100%;
}

.theme-toggle-btn:hover {
  background-color: rgba(111, 12, 223, 0.08);
  border-color: rgba(111, 12, 223, 0.2);
  transform: translateY(-2px);
}

.logout-btn:hover {
  background-color: rgba(244, 67, 54, 0.08);
  border-color: rgba(244, 67, 54, 0.2);
  transform: translateY(-2px);
}

.footer-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.theme-icon {
  font-size: 18px;
  color: var(--secondary-purple);
  transition: all 0.3s ease;
}

.logout-icon {
  font-size: 18px;
  color: #f44336;
  transition: all 0.3s ease;
}

.footer-text {
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.sidebar.collapsed .theme-toggle-btn,
.sidebar.collapsed .logout-btn {
  padding: 12px;
  justify-content: center;
  width: 48px;
  height: 48px;
}

.sidebar.collapsed .footer-text {
  display: none;
}

.expand-sidebar-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #6c5ce7;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

.expand-sidebar-btn:hover {
  transform: scale(1.1);
  background-color: #5b4bc9;
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

[data-theme="dark"] .expand-sidebar-btn {
  background-color: #6c5ce7;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

[data-theme="dark"] .expand-sidebar-btn:hover {
  background-color: #5b4bc9;
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

/* Dark Mode Styles */
[data-theme="dark"] .sidebar {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .logo-container:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .user-avatar {
  background: linear-gradient(135deg, rgba(0, 51, 201, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
}

[data-theme="dark"] .sidebar-menu li:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .sidebar-menu li.active {
  background: linear-gradient(90deg, rgba(0, 51, 201, 0.15) 0%, rgba(0, 51, 201, 0.05) 100%);
}

[data-theme="dark"] .theme-toggle-btn:hover {
  background-color: rgba(111, 12, 223, 0.15);
  border-color: rgba(111, 12, 223, 0.3);
}

[data-theme="dark"] .logout-btn:hover {
  background-color: rgba(244, 67, 54, 0.15);
  border-color: rgba(244, 67, 54, 0.3);
}

[data-theme="dark"] .theme-icon {
  color: #9d65ff;
}

[data-theme="dark"] .logout-icon {
  color: #ff6b6b;
}