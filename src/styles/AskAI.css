/* AskAI Container */
.askai-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  background-color: var(--section-bg);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.askai-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.askai-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
}

.clear-conversation-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-conversation-btn:hover {
  background-color: var(--btn-secondary-hover);
}

/* Chat Container */
.chat-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Message Styles */
.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.user-message {
  align-self: flex-end;
  background-color: var(--primary-blue);
  color: white;
  border-bottom-right-radius: 4px;
}

.ai-message {
  align-self: flex-start;
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  border-bottom-left-radius: 4px;
}

.message-content {
  font-size: 15px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-timestamp {
  font-size: 11px;
  margin-top: 6px;
  align-self: flex-end;
  opacity: 0.7;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--text-muted);
  animation: typing 1s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

/* Input Container */
.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.message-input {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  min-height: 24px;
  max-height: 150px;
  resize: none;
  font-family: inherit;
  font-size: 15px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--input-bg);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  overflow-y: auto;
}

.message-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: var(--primary-blue);
  color: white;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.2s;
}

.send-button:hover {
  background-color: var(--btn-primary-hover);
  transform: scale(1.05);
}

.send-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
}

/* Dark Mode Support */
[data-theme="dark"] .ai-message {
  background-color: var(--input-bg);
  border-color: var(--border-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .message {
    max-width: 90%;
  }
  
  .askai-header {
    padding: 12px 16px;
  }
  
  .input-container {
    padding: 12px 16px;
  }
  
  .clear-conversation-btn span {
    display: none;
  }
}
