/* Whitelist Platform Container */
.whitelist-platform-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  background-color: var(--section-bg);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.whitelist-platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.whitelist-platform-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
}

/* Tabs Container */
.tabs-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.tabs-header {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--text-secondary);
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

.tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
}

.tab-icon {
  font-size: 18px;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* Query Tab */
.query-tab {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.prompt-section, .query-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.prompt-section h3, .query-section h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.prompt-input-container {
  display: flex;
  gap: 12px;
}

.prompt-input {
  flex: 1;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 15px;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.prompt-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.generate-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
  border: none;
  border-radius: 8px;
  background-color: var(--primary-blue);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.generate-button:hover {
  background-color: var(--btn-primary-hover);
}

.generate-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

.query-input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.query-input {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  background-color: var(--input-bg);
  color: var(--text-color);
  resize: vertical;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.query-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.query-actions {
  display: flex;
  gap: 12px;
}

.run-button, .save-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.run-button {
  background-color: var(--primary-green);
  color: white;
}

.run-button:hover {
  background-color: var(--btn-success-hover);
}

.save-button {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

.save-button:hover {
  background-color: var(--btn-secondary-hover);
}

.run-button:disabled, .save-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

.error-message {
  padding: 12px 16px;
  background-color: var(--error-bg);
  color: var(--error-text);
  border-radius: 8px;
  font-size: 14px;
}

/* Query Result */
.query-result {
  display: flex;
  flex-direction: column;
  gap: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--section-bg);
  border-bottom: 1px solid var(--border-color);
}

.result-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.result-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: var(--text-secondary);
}

.result-table-container {
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.result-table {
  width: 100%;
  border-collapse: collapse;
}

.result-table th, .result-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
}

.result-table th {
  background-color: var(--section-bg);
  font-weight: 600;
  color: var(--text-secondary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.result-table tr:hover td {
  background-color: var(--hover-bg);
}

/* History Tab */
.history-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.clear-history-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-history-button:hover {
  background-color: var(--btn-danger-bg);
  color: white;
}

.empty-state {
  padding: 40px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 15px;
  background-color: var(--section-bg);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background-color: var(--section-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-timestamp {
  font-size: 14px;
  color: var(--text-secondary);
}

.history-actions {
  display: flex;
  gap: 8px;
}

.history-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-action-button:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-2px);
}

.history-query {
  padding: 12px;
  background-color: var(--input-bg);
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color);
  white-space: pre-wrap;
  overflow-x: auto;
  margin: 0;
}

.history-result-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: var(--text-secondary);
}

/* Saved Tab */
.saved-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.saved-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.saved-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.saved-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.saved-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background-color: var(--section-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.saved-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.saved-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.saved-actions {
  display: flex;
  gap: 8px;
}

.saved-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.saved-action-button:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-2px);
}

.saved-query {
  padding: 12px;
  background-color: var(--input-bg);
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color);
  white-space: pre-wrap;
  overflow-x: auto;
  margin: 0;
}

.saved-timestamp {
  font-size: 13px;
  color: var(--text-secondary);
}

/* Save Dialog */
.save-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.save-dialog {
  width: 400px;
  padding: 24px;
  background-color: var(--section-bg);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.save-dialog h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.query-name-input {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 15px;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.query-name-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.save-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-button, .confirm-save-button {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cancel-button {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

.cancel-button:hover {
  background-color: var(--btn-secondary-hover);
}

.confirm-save-button {
  background-color: var(--primary-blue);
  color: white;
}

.confirm-save-button:hover {
  background-color: var(--btn-primary-hover);
}

.confirm-save-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

/* Query Builder Tab */
.builder-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.builder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.builder-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.query-builder-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* padding: 20px; */
  background-color: var(--section-bg);
  border-radius: 8px;
  /* border: 1px solid var(--border-color); */
}

.main-operator-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.main-operator-label {
  font-weight: 500;
  color: var(--text-color);
  margin-right: 8px;
}

.query-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  padding: 16px;
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  /* margin-bottom: 16px; */
}

/* Level 1 sub-groups (direct children of main groups) */
.query-group > .sub-groups-container {
  margin-top: 0px;
  position: relative;
  padding-left: 20px;
  margin-left: 0;
}

.query-group > .sub-groups-container > .query-group.sub-group {
  position: relative;
}

.query-group > .sub-groups-container > .query-group.sub-group::before {
  content: none;
}

/* Level 2 sub-groups (children of level 1 sub-groups) */
.query-group.sub-group > .sub-groups-container {
  margin-top: 0px;
  position: relative;
  padding-left: 20px;
  margin-left: 0;
}

.query-group.sub-group > .sub-groups-container > .query-group.level-3 {
  position: relative;
}

.query-group.sub-group > .sub-groups-container > .query-group.level-3::before {
  content: none;
}

.query-group.sub-group {
  position: relative;
  background-color: rgba(0, 184, 148, 0.05);
  border-color: rgba(0, 184, 148, 0.3);
  margin-left: 0;
  margin-bottom: 10px;
}

.query-group.sub-group .logical-operator-container::before {
  content: none;
}

.query-group.level-3 {
  margin-left: 0;
  background-color: rgba(108, 92, 231, 0.05);
  border-color: rgba(108, 92, 231, 0.3);
}

.query-group.level-3 .logical-operator-container::before {
  content: none;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-header-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.logical-operator-container {
  display: flex;
  gap: 0;
  background-color: var(--input-bg);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  margin-right: 8px;
  position: relative;
  z-index: 1; /* Ensure the operator is above the connecting lines */
}

.logical-operator-btn {
  padding: 8px 16px;
  border: none;
  background-color: transparent;
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
}

.logical-operator-btn.active {
  background-color: #6c5ce7;
  color: white;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  white-space: nowrap;
}

.add-rule-btn {
  background-color: var(--primary-green);
  color: white;
  border-color: var(--primary-green);
}

.remove-group-btn:hover {
  background-color: var(--btn-danger-bg);
  color: white;
  border-color: var(--btn-danger-bg);
}

.rules-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-left: 20px;
  position: relative;
  margin-left: 0;
}

.rule-row {
  display: flex;
  position: relative;
}

.rule-connector-line {
  display: none;
}

/* Connect rules/groups to parent logical operator */
.rules-container::before {
  content: none;
}

/* Connect all rules to parent logical operator */
.rule-row .rule-connector-line::before {
  content: none;
}

/* Connect sub-groups container to parent logical operator */
.sub-groups-container::before {
  content: none;
}

/* Ensure the vertical line from the logical operator extends down */
.group-header .logical-operator-container::after {
  content: none;
}

.rule-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.rule-field-container,
.rule-operator-container,
.rule-value-container {
  position: relative;
}

.select-container {
  position: relative;
}

.select-container select {
  appearance: none;
  padding: 8px 30px 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 14px;
  width: 100%;
  cursor: pointer;
}

.select-arrow {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

.rule-value-container input {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 14px;
  width: 100%;
}

.remove-rule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-rule-btn:hover {
  background-color: var(--btn-danger-bg);
  color: white;
  border-color: var(--btn-danger-bg);
}

.builder-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.delete-group-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.add-group-btn {
  background-color: var(--secondary-orange);
  color: white;
}

.delete-group-btn {
  background-color: var(--input-bg);
  color: var(--text-color);
  margin-left: auto;
}

.add-rule-btn-main:hover {
  background-color: var(--btn-success-hover);
}

.delete-group-btn:hover {
  background-color: var(--btn-danger-bg);
  color: white;
  border-color: var(--btn-danger-bg);
}

/* SQL Preview Section */
.sql-preview-section {
  margin-top: 24px;
}

.sql-preview-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.sql-preview-container {
  position: relative;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.sql-preview-container.error {
  border-color: var(--error-text);
}

.sql-preview {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: none;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  background-color: var(--input-bg);
  color: var(--text-color);
  resize: vertical;
}

.sql-preview:focus {
  outline: none;
}

.parsing-error-message {
  padding: 8px 12px;
  background-color: var(--error-bg);
  color: var(--error-text);
  font-size: 13px;
  border-top: 1px solid var(--error-text);
}

.builder-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.builder-footer-actions {
  display: flex;
  gap: 12px;
}

.clear-btn,
.apply-btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.clear-btn {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

.apply-btn {
  background-color: var(--primary-blue);
  color: white;
}

.clear-btn:hover {
  background-color: var(--btn-secondary-hover);
}

.apply-btn:hover {
  background-color: var(--btn-primary-hover);
}

/* Utility Classes */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .prompt-input-container, .query-actions {
    flex-direction: column;
  }

  .generate-button, .run-button, .save-button {
    width: 100%;
  }

  .tabs-header {
    overflow-x: auto;
  }

  .tab-button {
    padding: 12px 16px;
    white-space: nowrap;
  }

  .save-dialog {
    width: 90%;
    max-width: 400px;
  }

  .rule-content {
    flex-wrap: wrap;
  }

  .rule-field-container,
  .rule-operator-container,
  .rule-value-container {
    flex: 1 0 100%;
    margin-bottom: 8px;
  }

  .builder-actions {
    flex-wrap: wrap;
  }

  .add-group-btn,
  .add-rule-btn-main,
  .delete-group-btn {
    flex: 1;
  }
}
