.create-request-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.create-request-header {
  text-align: center;
  margin-bottom: 40px;
}

.create-request-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.create-request-header p {
  font-size: 16px;
  color: #666;
}

.request-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.request-type-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
}

.request-type-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.request-type-icon {
  padding: 32px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.icon-container {
  background-color: #fff;
  width: 84px;
  height: 84px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.request-type-content {
  padding: 24px;
  flex-grow: 1;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.request-type-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.request-type-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* Dark mode styles */
[data-theme="dark"] .request-type-card {
  background-color: var(--section-bg);
  border-color: #444c56;
}

[data-theme="dark"] .request-type-content {
  background-color: var(--content-bg);
  border-color: #444c56;
}

[data-theme="dark"] .request-type-content h3 {
  color: #e6edf3;
}

[data-theme="dark"] .request-type-content p {
  color: #adbac7;
}

[data-theme="dark"] .create-request-header h2 {
  color: #e6edf3;
}

[data-theme="dark"] .create-request-header p {
  color: #adbac7;
}

[data-theme="dark"] .icon-container {
  background-color: var(--content-bg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .request-type-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }

  .request-type-icon {
    padding: 24px 0;
  }

  .icon-container {
    width: 72px;
    height: 72px;
  }

  .request-type-content {
    padding: 16px;
  }
}

/* Animation for card hover */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
  }
}

.request-type-card:active {
  transform: scale(0.98);
}

/* Request Form Styles */
.request-page-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.request-form-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 30px;
  margin-bottom: 30px;
  position: relative;
}

.request-form-container h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #555;
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input[type="text"]:focus,
.form-group input[type="date"]:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #00CF6A;
  box-shadow: 0 0 0 2px rgba(0, 207, 106, 0.2);
  outline: none;
}

.form-group textarea {
  min-height: 120px;
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  font-size: 16px;
}

.btn-primary {
  background-color: #00CF6A;
  color: white;
}

.btn-primary:hover {
  background-color: #00b85e;
}

.btn-primary:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.error-message {
  background-color: #fee2e2;
  color: #ef4444;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
}

.success-modal h3 {
  color: #00CF6A;
  text-align: center;
  margin-bottom: 16px;
  font-size: 22px;
}

.success-modal p {
  text-align: center;
  margin-bottom: 20px;
  color: #555;
}

.request-details {
  /* background-color: #f8f9fa; */
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.request-details p {
  text-align: left;
  margin-bottom: 8px;
  color: #333;
}

.success-modal button {
  display: block;
  margin: 0 auto;
}

/* Dark mode styles for form */
[data-theme="dark"] .request-form-container {
  background-color: var(--section-bg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .request-form-container h2 {
  color: #e6edf3;
}

[data-theme="dark"] .form-group label {
  color: #adbac7;
}

[data-theme="dark"] .form-group input[type="text"],
[data-theme="dark"] .form-group input[type="date"],
[data-theme="dark"] .form-group textarea,
[data-theme="dark"] .form-group select {
  background-color: var(--input-bg);
  border-color: #444c56;
  color: #e6edf3;
}

[data-theme="dark"] .form-group input[type="text"]:focus,
[data-theme="dark"] .form-group input[type="date"]:focus,
[data-theme="dark"] .form-group textarea:focus,
[data-theme="dark"] .form-group select:focus {
  border-color: #00CF6A;
  box-shadow: 0 0 0 2px rgba(0, 207, 106, 0.3);
}

[data-theme="dark"] .btn-secondary {
  background-color: #444c56;
  color: #e6edf3;
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: #545d67;
}

[data-theme="dark"] .error-message {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

/* Dark mode styles for modal */
[data-theme="dark"] .modal-content {
  background-color: var(--section-bg);
}

[data-theme="dark"] .success-modal p {
  color: #adbac7;
}

/* [data-theme="dark"] .request-details {
  background-color: #22272e;
} */

[data-theme="dark"] .request-details p {
  color: #e6edf3;
}

/* Responsive adjustments for form */
@media (max-width: 768px) {
  .request-form-container {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
    gap: 10px;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
}