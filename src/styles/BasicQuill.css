/* Basic styles for ReactQuill editor */

.basic-quill-editor {
  /* border: 1px solid #ddd; */
  border-radius: 4px;
  margin-bottom: 20px;
}

/* Dark mode support */
[data-theme="dark"] .basic-quill-editor {
  border-color: #444;
  background-color: var(--input-bg);
  color: #e2e8f0;
}

/* Toolbar styles */
.basic-quill-editor .ql-toolbar {
  /* border-bottom: 1px solid #ddd; */
  background-color: #f8f9fa;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 8px;
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar {
  background-color: #1a202c;
  border-color: #444;
}

/* Toolbar buttons */
.basic-quill-editor .ql-toolbar button {
  margin-right: 3px;
}

.basic-quill-editor .ql-toolbar button:hover {
  color: #00CF6A; /* Primary green color */
}

.basic-quill-editor .ql-toolbar button.ql-active {
  color: #00CF6A;
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar button {
  color: #cbd5e0;
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar button:hover,
[data-theme="dark"] .basic-quill-editor .ql-toolbar button.ql-active {
  color: #00CF6A;
}

/* Dropdown styles */
.basic-quill-editor .ql-toolbar .ql-picker {
  color: #333;
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar .ql-picker {
  color: #cbd5e0;
}

.basic-quill-editor .ql-toolbar .ql-picker-options {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar .ql-picker-options {
  background-color: #2d3748;
  border-color: #444;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Color picker */
.basic-quill-editor .ql-toolbar .ql-color-picker .ql-picker-item {
  border-radius: 2px;
}

/* Editor area */
.basic-quill-editor .ql-container {
  font-size: 16px;
  min-height: 150px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

[data-theme="dark"] .basic-quill-editor .ql-container {
  background-color: #2d3748;
  border: 0;
}

/* Editor content area */
.basic-quill-editor .ql-editor {
  min-height: 180px;
  padding: 12px 15px;
  line-height: 1.5;
}

[data-theme="dark"] .basic-quill-editor .ql-editor {
  background-color: var(--input-bg);
  color: #e2e8f0;
}

/* Placeholder text */
.basic-quill-editor .ql-editor.ql-blank::before {
  font-style: italic;
  color: #aaa;
}

[data-theme="dark"] .basic-quill-editor .ql-editor.ql-blank::before {
  color: #718096;
}

/* Format styles */
.basic-quill-editor .ql-editor h1 {
  font-size: 2em;
  margin-bottom: 0.5em;
}

.basic-quill-editor .ql-editor h2 {
  font-size: 1.5em;
  margin-bottom: 0.5em;
}

.basic-quill-editor .ql-editor h3 {
  font-size: 1.17em;
  margin-bottom: 0.5em;
}

.basic-quill-editor .ql-editor p {
  margin-bottom: 0.8em;
}

.basic-quill-editor .ql-editor ul,
.basic-quill-editor .ql-editor ol {
  padding-left: 1.5em;
  margin-bottom: 0.8em;
}

/* Link styles */
.basic-quill-editor .ql-editor a {
  color: #0033C9; /* Primary blue color */
  text-decoration: underline;
}

[data-theme="dark"] .basic-quill-editor .ql-editor a {
  color: #63b3ed;
}

/* Focus state */
.basic-quill-editor:focus-within {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 2px rgba(0, 207, 106, 0.2);
}

[data-theme="dark"] .basic-quill-editor:focus-within {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 2px rgba(0, 207, 106, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .basic-quill-editor .ql-toolbar {
    flex-wrap: wrap;
    padding: 5px;
  }

  .basic-quill-editor .ql-toolbar button {
    margin-bottom: 5px;
  }

  .basic-quill-editor .ql-editor {
    min-height: 150px;
  }
}
