/* Request Success Modal Specific Styles */
.request-success-modal .modal-container {
  max-width: 500px;
}

.request-success-modal .modal-header {
  background-color: #4caf50;
  color: white;
  border-radius: 8px 8px 0 0;
}

.request-success-modal .modal-header h3 {
  color: white;
}

.request-success-modal .modal-body {
  text-align: center;
}

.request-success-modal .success-icon {
  font-size: 48px;
  color: #4caf50;
  margin-bottom: 20px;
}

.request-success-modal .success-message {
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
}

.request-success-modal .detail-row {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #e0e0e0;
}

.request-success-modal .detail-row:last-child {
  border-bottom: none;
}

.request-success-modal .detail-label {
  color: #666;
  font-weight: 500;
}

.request-success-modal .detail-value {
  color: #333;
  font-weight: 500;
}

.request-modal-actions {
  margin: 0px 20px 20px;
}

/* Dark theme support for Request Success Modal */
[data-theme="dark"] .request-success-modal .modal-header {
  background-color: #2d5a2d;
}

[data-theme="dark"] .request-success-modal .success-message {
  color: #e6edf3;
}

[data-theme="dark"] .request-success-modal .detail-row {
  border-color: #444c56;
}

[data-theme="dark"] .request-success-modal .detail-label {
  color: #adbac7;
}

[data-theme="dark"] .request-success-modal .detail-value {
  color: #e6edf3;
} 