.platform-select-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.platform-select-header {
  text-align: center;
  margin-bottom: 40px;
}

.platform-select-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.platform-select-header p {
  font-size: 16px;
  color: #666;
}

.platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.platform-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
}

.platform-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.platform-image-container {
  height: 200px;
  overflow: hidden;
  background-color: #f5f7fb;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding: 10px;
}

.platform-image {
  max-width: 100%;
  max-height: 180px;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.platform-content {
  padding: 24px;
  flex-grow: 1;
  background-color: #fff;
}

.platform-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.platform-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.platform-actions {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.platform-actions button {
  padding: 10px 24px;
  font-size: 16px;
}

/* Dark mode styles */
[data-theme="dark"] .platform-card {
  background-color: #2d333b;
  border-color: #444c56;
}

[data-theme="dark"] .platform-content {
  background-color: #22272e;
  border-color: #444c56;
}

[data-theme="dark"] .platform-content h3 {
  color: #e6edf3;
}

[data-theme="dark"] .platform-content p {
  color: #adbac7;
}

[data-theme="dark"] .platform-select-header h2 {
  color: #e6edf3;
}

[data-theme="dark"] .platform-select-header p {
  color: #adbac7;
}

[data-theme="dark"] .platform-image-container {
  background-color: #2d333b;
  border-color: #444c56;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .platform-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
  
  .platform-image-container {
    height: 160px;
  }
  
  .platform-image {
    max-height: 160px;
  }
  
  .platform-content {
    padding: 16px;
  }
}

/* Animation for card hover */
.platform-card:active {
  transform: scale(0.98);
} 