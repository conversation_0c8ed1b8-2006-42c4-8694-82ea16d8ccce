.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-color);
  transition: background-color 0.3s ease, color 0.3s ease;
  position: relative;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-image: radial-gradient(circle at 10% 20%, rgba(0, 51, 201, 0.05) 0%, rgba(111, 12, 223, 0.05) 90%);
}

[data-theme="dark"] .login-container {
  background-image: radial-gradient(circle at 10% 20%, rgba(0, 51, 201, 0.1) 0%, rgba(111, 12, 223, 0.1) 90%);
}

.login-theme-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.login-card-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 450px;
}

.login-card {
  background-color: var(--content-bg);
  border-radius: 20px;
  box-shadow: var(--card-shadow);
  width: 100%;
  padding: 40px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

[data-theme="dark"] .login-card {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .login-card:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.login-feature-icons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin: 24px 0;
}

.login-feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  font-size: 24px;
  color: white;
  transition: all 0.3s ease;
}

.login-feature-icon:hover {
  transform: translateY(-5px) rotate(5deg);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.bg-primary-green {
  background-color: var(--primary-green);
}

.bg-secondary-light-blue {
  background-color: var(--secondary-light-blue);
}

.bg-secondary-yellow {
  background-color: var(--secondary-yellow);
}

.bg-secondary-purple {
  background-color: var(--secondary-purple);
}

.login-header {
  text-align: center;
  margin-bottom: 20px;
}

.login-header h1 {
  color: var(--primary-blue);
  margin-bottom: 12px;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme="dark"] .login-header h1 {
  background: linear-gradient(135deg, var(--secondary-light-blue), var(--secondary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  color: var(--text-secondary);
  font-size: 16px;
}

.login-error {
  background-color: var(--error-bg);
  color: var(--error-text);
  padding: 14px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  /* border-left: 4px solid var(--error-text); */
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.login-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.login-form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 15px;
}

.login-form-group input {
  width: 100%;
  padding: 14px 16px;
  border: 1px solid var(--input-border);
  border-radius: 12px;
  font-size: 15px;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all 0.2s ease;
}

.login-form-group input:focus {
  border-color: var(--primary-blue);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
  transform: translateY(-2px);
}

[data-theme="dark"] .login-form-group input:focus {
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.2);
}

.login-form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  margin-top: 8px;
}

.login-remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
}

.login-remember-me input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary-blue);
}

.login-remember-me label {
  cursor: pointer;
  color: var(--text-secondary);
}

.forgot-password {
  color: var(--primary-blue);
  text-decoration: none;
  transition: all 0.2s;
  font-weight: 500;
}

[data-theme="dark"] .forgot-password {
  color: var(--secondary-light-blue);
}

.forgot-password:hover {
  text-decoration: underline;
  transform: translateY(-2px);
}

.login-button {
  background-color: var(--primary-blue);
  color: white;
  border: none;
  padding: 16px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.6s ease;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  background-color: var(--btn-primary-hover);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 51, 201, 0.3);
}

.login-button:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(0, 51, 201, 0.2);
}

.login-button:disabled {
  background-color: rgba(0, 51, 201, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.login-password-input-container {
  position: relative;
  width: 100%;
}

.login-password-input-container input {
  width: 100%;
  padding-right: 40px;
}

.login-password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.login-password-toggle:hover {
  color: var(--text-color);
}

.login-password-toggle:focus {
  outline: none;
}

/* Responsive styles */
@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }

  .login-form-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .login-header h1 {
    font-size: 28px;
  }

  .login-feature-icons {
    gap: 12px;
  }

  .login-feature-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

}