/* Event Management Page Styles */
.event-management-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background-color: var(--bg-color);
  min-height: 100vh;
}

.event-management-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: var(--content-bg);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: var(--secondary-hover);
  transform: translateY(-1px);
}

.event-management-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

/* Success Message */
.success-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background-color: var(--success-bg);
  color: var(--success-color);
  border: 1px solid var(--success-border);
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 500;
}

/* Filter and Search Bar */
.filter-search-bar {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--content-bg);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  flex-wrap: wrap;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 45px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-color-alpha);
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 16px;
}

.search-icon.spinning {
  animation: spin 1s linear infinite;
}

.clear-search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 16px;
  transition: color 0.2s ease;
}

.clear-search-icon:hover {
  color: var(--text-color);
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-container label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
}

.filter-select {
  padding: 10px 12px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-color-alpha);
}

.actions-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: var(--warning-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background-color: var(--warning-hover);
  transform: translateY(-1px);
}

.btn-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
  transform: translateY(-1px);
}

/* Results Summary */
.results-summary {
  margin-bottom: 20px;
  padding: 12px 16px;
  background-color: var(--info-bg);
  color: var(--info-color);
  border: 1px solid var(--info-border);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background-color: var(--error-bg);
  color: var(--error-color);
  border: 1px solid var(--error-border);
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 500;
}

/* Loading States */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
  color: var(--text-secondary);
  font-size: 14px;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 20px;
}

/* Event Table */
.event-table-container {
  background-color: var(--content-bg);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  margin-bottom: 20px;
}

.event-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.event-table th {
  background-color: var(--table-header-bg);
  color: var(--text-color);
  font-weight: 600;
  padding: 16px 12px;
  text-align: left;
  border-bottom: 2px solid var(--border-color);
  white-space: nowrap;
}

.event-table td {
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
  vertical-align: middle;
}

.event-table tr:hover {
  background-color: var(--hover-bg);
}

.event-table tr:last-child td {
  border-bottom: none;
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background-color: var(--success-bg);
  color: var(--success-color);
  border: 1px solid var(--success-border);
}

.status-badge.inactive {
  background-color: var(--error-bg);
  color: var(--error-color);
  border: 1px solid var(--error-border);
}

/* Action Button */
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

/* Back to Top Button */
.back-to-top-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 1000;
}

.back-to-top-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Scroll Sentinel */
.scroll-sentinel {
  height: 1px;
  margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .event-table-container {
    overflow-x: auto;
  }
  
  .event-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .event-management-container {
    padding: 15px;
  }
  
  .filter-search-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .search-container {
    min-width: auto;
  }
  
  .actions-container {
    justify-content: center;
  }
  
  .event-management-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .event-management-title {
    font-size: 24px;
  }
}
