.profile-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.profile-header h2 {
  margin: 0;
  font-size: 24px;
  color: var(--text-color);
}

.back-button,
.edit-button,
.cancel-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.back-button {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.back-button:hover {
  background-color: var(--hover-bg);
}

.edit-button {
  background-color: var(--primary-blue);
  color: white;
}

.edit-button:disabled {
  background-color: var(--text-secondary);
  opacity: 0.7;
  cursor: not-allowed;
}

.edit-button:hover:not(:disabled) {
  background-color: var(--primary-blue-dark);
}

.cancel-button {
  background-color: var(--danger-color);
  color: white;
}

.cancel-button:hover {
  background-color: var(--danger-color-dark);
}

.error-message,
.success-message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message {
  background-color: rgba(255, 76, 76, 0.1);
  color: #ff4c4c;
  border: 1px solid rgba(255, 76, 76, 0.3);
}

.success-message {
  background-color: rgba(0, 207, 106, 0.1);
  color: #00cf6a;
  border: 1px solid rgba(0, 207, 106, 0.3);
}

.profile-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (min-width: 768px) {
  .profile-content {
    grid-template-columns: 300px 1fr;
  }
}

.profile-avatar-section {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.avatar-icon {
  width: 120px;
  height: 120px;
  color: var(--secondary-purple);
}

.profile-basic-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: var(--text-color);
}

.user-role {
  margin: 0 0 4px 0;
  color: var(--primary-blue);
  font-weight: 500;
}

.user-username {
  margin: 0 0 4px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.user-job-title {
  margin: 0 0 4px 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
}

.user-department {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.profile-details {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.info-section,
.form-section {
  margin-bottom: 24px;
}

.info-section h3,
.form-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: var(--text-color);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.info-label {
  min-width: 150px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-weight: 500;
}

.info-value {
  color: var(--text-color);
}

.platforms-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.platform-item {
  background-color: var(--hover-bg);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
}

.platform-name {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.platform-access {
  font-size: 14px;
  color: var(--primary-blue);
}

/* Form styles */
.profile-edit-form {
  width: 100%;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  background-color: var(--primary-green);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.save-button:hover {
  background-color: var(--primary-green-dark);
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-secondary);
  font-size: 18px;
}

/* Dark mode specific adjustments */

/* CSS variables are defined in App.css or index.css */
:root {
  --primary-blue: #0033C9;
  --primary-blue-dark: #002aa3;
  --primary-blue-light: #335FD1;
  --primary-green: #00CF6A;
  --primary-green-dark: #00B85E;
  --secondary-purple: #6F0CDF;
  --danger-color: #FF4C4C;
  --danger-color-dark: #E53E3E;
  --text-color: #333333;
  --text-secondary: #666666;
  --border-color: #E5E7EB;
  --card-bg: #FFFFFF;
  --hover-bg: #F3F4F6;
  --input-bg: #FFFFFF;
}

[data-theme="dark"] {
  --text-color: #E5E7EB;
  --text-secondary: #9CA3AF;
  --border-color: #374151;
  --card-bg: #1F2937;
  --hover-bg: #374151;
  --input-bg: #111827;
}
