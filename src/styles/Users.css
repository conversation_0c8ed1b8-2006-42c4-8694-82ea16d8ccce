/* Users container */
.users-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Users header */
.users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  background-color: var(--section-bg);
  padding: 20px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.users-filters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-color);
  min-width: 120px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-group select:hover {
  border-color: var(--primary-blue);
}

.filter-group select:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
  outline: none;
}

.users-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.users-actions-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Column selector styling */
.column-selector-container {
  position: relative;
}

.column-selector-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--input-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.column-selector-btn:hover {
  background-color: var(--btn-secondary-hover);
  border-color: var(--primary-blue);
}

.column-selector-btn:focus,
.column-selector-btn.active {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
  outline: none;
}

.column-selector-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 250px;
  background-color: var(--content-bg);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  z-index: 100;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.column-selector-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.column-selector-header h4 {
  margin: 0;
  font-size: 16px;
  color: var(--text-color);
}

.column-selector-list {
  max-height: 300px;
  overflow-y: auto;
}

.column-selector-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.column-selector-item:hover {
  background-color: var(--hover-bg);
}

.column-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--input-bg);
  transition: all 0.2s ease;
}

.column-selector-item:hover .column-checkbox {
  border-color: var(--primary-blue);
}

.column-check-icon {
  color: var(--primary-blue);
  font-size: 14px;
}

/* Users table */
.users-table-container {
  overflow-x: auto;
  background-color: var(--content-bg);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
}

.users-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 12px;
  overflow: hidden;
}

.users-table td {
  padding: 5px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
}

.users-table th {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  user-select: none;
  position: relative;
  border-top: none;
  transition: all 0.2s ease;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.users-table th.sorted {
  color: var(--secondary-purple);
  background-color: rgba(0, 51, 201, 0.05);
}

.sort-icon {
  margin-left: 8px;
  font-size: 14px;
  vertical-align: middle;
  transition: all 0.2s ease;
}

.sort-inactive {
  opacity: 0.3;
}

.users-table th:hover {
  background-color: var(--hover-bg);
}

.users-table tr:last-child td {
  border-bottom: none;
}

.users-table tbody tr {
  transition: all 0.2s ease;
}

.users-table tbody tr:hover {
  background-color: var(--hover-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  z-index: 1;
  position: relative;
}

/* Role badges */
.role-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.role-badge.admin {
  background-color: #e3f2fd;
  color: #2196f3;
}

.role-badge.approver {
  background-color: #e8f5e9;
  color: #4caf50;
}

.role-badge.user {
  background-color: #f5f5f5;
  color: #757575;
}

/* User form */
.user-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.users-form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.users-form-group label {
  font-weight: 500;
  color: var(--text-secondary);
}

.users-form-group input,
.users-form-group select {
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

.role-description {
  font-size: 14px;
  color: var(--text-muted);
  font-style: italic;
  margin-top: -10px;
  margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .users-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .users-filters {
    width: 100%;
    margin-bottom: 15px;
    overflow-x: auto;
    padding-bottom: 10px;
    flex-wrap: nowrap;
  }

  .users-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .filter-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-group select {
    width: 100%;
  }

  .users-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .users-actions-buttons {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .column-selector-container {
    width: 100%;
  }

  .column-selector-btn {
    width: 100%;
    justify-content: center;
  }

  .column-selector-dropdown {
    width: 100%;
    position: relative;
    margin-top: 8px;
  }

  .users-search-box {
    width: 100%;
  }

  .users-search-box input,
  .users-search-box input:focus {
    width: 100%;
  }

  .btn-primary {
    width: 100%;
    justify-content: center;
  }

  .pagination-container {
    flex-direction: column;
    gap: 15px;
  }

  .pagination {
    width: 100%;
    justify-content: center;
  }
}

/* Array values styling */
.array-values-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.array-value-item {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 0.85em;
  white-space: nowrap;
}

/* Default array value style */
.array-value-default {
  background-color: #f0f0f0;
  color: #555;
}

/* Role-based colors */
.array-value-admin {
  background-color: #e4abdd;
  color: #840e51;
}

.array-value-approver {
  background-color: #edd9c4;
  color: #ca6f0e;
}

.array-value-user {
  background-color: #a8c1e7;
  color: #2475ee;
}

/* Role-based colors */
.array-value-whitelist {
  background-color: #e8eaf6;
  color: #1876f1;
}

.array-value-all {
  background-color: #e8eaf6;
  color: #f11818;
}

.array-value-funnel {
  background-color: #fff3e0;
  color: #d24fc0;
}

.array-value-zns {
  background-color: #fff3e0;
  color: #564738;
}

.array-value-sbv {
  background-color: #fce4ec;
  color: #c2185b;
}

.array-value-ads {
  background-color: #e0f7fa;
  color: #0097a7;
}

.array-value-config {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.array-value-tnex {
  background-color: #e8f5e9;
  color: #388e3c;
}

.array-value-shinhan {
  background-color: #e8f5e9;
  color: #dca358;
}

.array-value-samsung {
  background-color: #e8f5e9;
  color: #1dd5f1;
}

/* Status-based colors */
.array-value-active {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green, #388e3c);
}

.array-value-inactive {
  background-color: rgba(158, 158, 158, 0.15);
  color: #9e9e9e;
}

.array-value-removed {
  background-color: rgba(244, 67, 54, 0.15);
  color: #f44336;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: capitalize;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.status-badge.active {
  background-color: rgba(0, 207, 106, 0.15);
  color: var(--primary-green, #388e3c);
}

.status-badge.inactive {
  background-color: rgba(158, 158, 158, 0.15);
  color: #9e9e9e;
}

.status-badge.remove {
  background-color: rgba(244, 67, 54, 0.15);
  color: #f44336;
}

/* Dark mode support for column selector */
[data-theme="dark"] .column-selector-btn {
  background-color: var(--input-bg);
  color: var(--text-color);
  border-color: var(--border-color);
}

[data-theme="dark"] .column-selector-btn:hover,
[data-theme="dark"] .column-selector-btn:focus,
[data-theme="dark"] .column-selector-btn.active {
  background-color: var(--btn-secondary-hover);
  border-color: var(--primary-blue);
}

[data-theme="dark"] .column-selector-dropdown {
  background-color: var(--content-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .column-checkbox {
  border-color: var(--border-color);
  background-color: var(--input-bg);
}

[data-theme="dark"] .column-selector-item:hover .column-checkbox {
  border-color: var(--primary-blue);
}

/* Dark mode support for status badges */
[data-theme="dark"] .status-badge.active {
  background-color: rgba(0, 207, 106, 0.2);
  color: #66bb6a;
}

[data-theme="dark"] .status-badge.inactive {
  background-color: rgba(158, 158, 158, 0.2);
  color: #bdbdbd;
}

[data-theme="dark"] .status-badge.remove {
  background-color: rgba(244, 67, 54, 0.2);
  color: #ef5350;
}

/* Permission-based colors */
.array-value-read {
  background-color: #e3f2fd;
  color: #1976d2;
}

.array-value-write {
  background-color: #fff3e0;
  color: #f57c00;
}

/* Ensure table cells with array values don't overflow */
.users-table td {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}

/* Action buttons styling */
.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: var(--hover-bg);
  color: var(--text-secondary);
}

.btn-icon.view {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--secondary-light-blue);;
}

.btn-icon.view:hover {
  background-color: var(--secondary-light-blue);;
  color: white;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.btn-icon.edit {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.btn-icon.edit:hover {
  background-color: #4caf50;
  color: white;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-icon.delete {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.btn-icon.delete:hover {
  background-color: #f44336;
  color: white;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.btn-icon.delete:disabled {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Multi-select styling */
.multi-select {
  min-height: 120px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  width: 100%;
}

.multi-select option {
  padding: 8px;
  margin-bottom: 4px;
  border-radius: 4px;
}

.multi-select option:checked {
  background-color: #e3f2fd;
  color: #1976d2;
}

.form-help {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: var(--text-muted);
  font-style: italic;
}

/* Checkbox group styling */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  cursor: pointer;
}

.checkbox-text {
  font-size: 14px;
}

/* Toggle switch styling */
.toggle-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #2196F3;
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Radio button styling */
.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.radio-label input[type="radio"] {
  margin-right: 8px;
  cursor: pointer;
}

.radio-text {
  font-size: 14px;
}

/* Password input with suggestion */
.password-input-container {
  display: flex;
  gap: 10px;
  width: 100%;
}

.password-input-container input {
  flex: 1;
}

.password-suggestion-btn {
  background-color: var(--btn-secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0 10px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
  transition: background-color 0.2s;
  color: var(--text-color);
}

.password-suggestion-btn:hover {
  background-color: var(--btn-secondary-hover);
}

.password-suggestion-btn:disabled {
  background-color: var(--btn-secondary-bg);
  color: var(--text-muted);
  cursor: not-allowed;
  border-color: var(--border-color);
  opacity: 0.6;
}

.password-toggle-btn {
  background-color: var(--btn-secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0 10px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
  transition: background-color 0.2s;
  color: var(--text-color);
}

.password-toggle-btn:hover {
  background-color: var(--btn-secondary-hover);
}

/* Search box styling */
.users-search-box {
  position: relative;
  align-content: center;
}

.users-search-box input {
  padding: 12px 20px 12px 45px;
  border: 1px solid var(--border-color);
  border-radius: 30px;
  font-size: 14px;
  width: 250px;
  transition: all 0.3s ease;
  outline: none;
  background-color: var(--input-bg);
  color: var(--text-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.users-search-box input:focus {
  width: 320px;
  border-color: var(--primary-blue);
  box-shadow: 0 4px 10px rgba(0, 51, 201, 0.1);
  /* transform: translateY(-2px); */
}

.users-search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 16px;
  transition: color 0.3s ease;
}

.users-search-box input:focus + .users-search-icon {
  color: var(--primary-blue);
}

/* Pagination styling */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  background-color: var(--content-bg);
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.pagination {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-btn {
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: none;
  background-color: var(--hover-bg);
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-2px);
}

.pagination-btn.active {
  background-color: var(--primary-blue);
  color: white;
  box-shadow: 0 4px 10px rgba(0, 51, 201, 0.2);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}