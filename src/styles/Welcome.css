/* Welcome Page Styles */
.welcome-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.welcome-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.welcome-content {
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
  max-width: 800px;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.welcome-header {
  text-align: center;
  margin-bottom: 2rem;
}

.welcome-logo h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--secondary-light-blue), var(--secondary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.welcome-text h2 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.welcome-text h3 {
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.welcome-text p {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.welcome-progress {
  background: var(--background-secondary);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid var(--border-color);
}

.progress-header {
  text-align: center;
  margin-bottom: 2rem;
}

.progress-header h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.progress-step.pending {
  background: var(--background-tertiary);
  opacity: 0.6;
}

.progress-step.active {
  background: linear-gradient(135deg, rgba(0, 207, 106, 0.1), rgba(0, 51, 201, 0.1));
  border-color: var(--primary-green);
  box-shadow: 0 4px 20px rgba(0, 207, 106, 0.2);
}

.progress-step.completed {
  background: linear-gradient(135deg, rgba(0, 207, 106, 0.15), rgba(0, 51, 201, 0.15));
  border-color: var(--primary-green);
}

.step-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.progress-step.pending .step-icon {
  background: var(--background-primary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.progress-step.active .step-icon {
  background: var(--primary-green);
  color: white;
  animation: pulse 2s infinite;
}

.progress-step.completed .step-icon {
  background: var(--primary-green);
  color: white;
}

.step-content {
  flex: 1;
}

.step-content h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.step-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

.progress-complete {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.complete-message {
  margin-bottom: 2rem;
}

.complete-icon {
  font-size: 2rem;
  color: var(--primary-green);
  margin-bottom: 1rem;
  animation: bounceIn 0.6s ease;
}

.complete-message h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.complete-message p {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.btn-get-started {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-width: 150px;
}

.btn-get-started:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 207, 106, 0.3);
}

.btn-get-started:active {
  transform: translateY(0);
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 207, 106, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 207, 106, 0);
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.spin {
  animation: spin 1s linear infinite;
}

/* Dark mode adjustments */
[data-theme="dark"] .welcome-container {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

[data-theme="dark"] .welcome-content {
  background: var(--bg-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .welcome-container {
    padding: 1rem;
  }

  .welcome-content {
    padding: 2rem;
  }

  .welcome-logo h1 {
    font-size: 2rem;
  }

  .welcome-text h2 {
    font-size: 1.5rem;
  }

  .welcome-text h3 {
    font-size: 1.125rem;
  }

  .progress-step {
    padding: 1rem;
  }

  .step-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}
