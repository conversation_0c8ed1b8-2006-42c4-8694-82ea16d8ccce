.release-notes-container {
  /* padding: 20px; */
  /* max-width: 1200px; */
  margin: 0 auto;
  position: relative;
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #00CF6A;
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out, fadeOut 0.3s ease-in-out 2.7s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-10px); }
}

.error-message {
  background-color: #ff4d4f;
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-in-out;
}

.release-notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.release-notes-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.status-filter {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
}

.status-filter button {
  padding: 8px 16px;
  border: none;
  background-color: #f0f0f0;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.status-filter button.active {
  background-color: #0033C9;
  color: white;
}

.dark .status-filter button {
  background-color: #333;
  color: #ddd;
}

.dark .status-filter button.active {
  background-color: #0033C9;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.add-release-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #00CF6A;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.add-release-btn:hover {
  background-color: #00b85e;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #0033C9;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background-color: #0029a0;
}

.releases-list {
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 24px;
}

.release-items-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.version-tree-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  position: sticky;
  top: 24px;
  max-height: calc(100vh - 48px);
  overflow-y: auto;
  /* border: 0.8px solid #0033C9; */
}

.dark .version-tree-container {
  background-color: var(--section-bg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.version-tree-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark .version-tree-title {
  color: #eee;
}

.version-tree {
  margin-top: 16px;
}

.version-tree-node {
  margin-bottom: 8px;
}

.version-tree-parent {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 0;
  font-weight: 500;
}

.version-tree-parent:hover {
  color: #0033C9;
}

.dark .version-tree-parent:hover {
  color: #4d79ff;
}

.version-tree-parent.active {
  color: #0033C9;
  font-weight: 600;
}

.dark .version-tree-parent.active {
  color: #4d79ff;
}

.version-tree-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.version-tree-toggle.expanded {
  transform: rotate(90deg);
}

.version-tree-children {
  margin-left: 24px;
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease;
}

.version-tree-children.expanded {
  height: auto;
}

/* Media query for responsive design */
@media (max-width: 768px) {
  .releases-list {
    grid-template-columns: 1fr;
  }

  .version-tree-container {
    position: relative;
    top: 0;
    max-height: none;
    margin-bottom: 24px;
  }
}

.release-item {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  position: relative;
  overflow: hidden;
  border: 0.8px solid #0033C9;
}

.dark .release-item {
  background-color: var(--section-bg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Định dạng theo status */
.release-item.released {
  border-color: #00CF6A;
}

.release-item.upcoming {
  border-color: #0033C9;
}

/* .release-item-header {
  margin-bottom: 20px;
  position: relative;
}

.release-item-header.edit-mode {
  margin-bottom: 20px;
} */

.release-item-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.release-item-version {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  min-width: 200px;
}

.release-item-version h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.dark .release-item-version h2 {
  color: #eee;
}

.release-item-version input {
  width: 100%;
  padding: 10px;
  font-size: 18px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.dark .release-item-version input {
  background-color: #333;
  border-color: #444;
  color: #eee;
}

.release-item-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.release-status {
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 500;
  display: inline-block;
}

.release-status.released {
  background-color: rgba(0, 207, 106, 0.1);
  color: #00CF6A;
}

.release-status.upcoming {
  background-color: rgba(0, 51, 201, 0.1);
  color: #053ce0;
}

.dark .release-status.released {
  background-color: rgba(0, 207, 106, 0.2);
}

.dark .release-status.upcoming {
  background-color: rgba(59, 103, 235, 0.2);
}

.release-date {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.dark .release-date {
  color: #aaa;
}

.release-progress {
  width: 100%;
  margin-bottom: 10px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.release-progress.edit-mode {
  position: relative;
  padding: 10px 0;
}

.progress-container {
  position: relative;
  height: 20px;
  display: flex;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 5px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: visible;
  position: relative;
}

.dark .progress-bar {
  background-color: #333;
}

.progress-fill {
  height: 100%;
  background-color: #0033C9;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.release-item.released .progress-fill {
  background-color: #00CF6A;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #0033C9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.release-item.released .progress-thumb {
  background-color: #00CF6A;
}

.progress-slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 20px; /* Giảm chiều cao để chỉ bao phủ thanh tiến trình */
  opacity: 0;
  cursor: pointer;
  z-index: 3;
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: right;
  margin-top: 8px;
}

.dark .progress-text {
  color: #aaa;
}

.release-item-content {
  margin-bottom: 24px;
}

.release-item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.release-item-status {
  display: flex;
  gap: 16px;
  align-items: center;
}

.release-item-status label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.dark .release-item-status label {
  background-color: #333;
}

.release-item-status input[type="radio"] {
  margin: 0;
}

.release-item-status label:has(input:checked) {
  background-color: rgba(0, 51, 201, 0.1);
  color: #0033C9;
}

.release-item.released .release-item-status label:has(input[value="released"]:checked) {
  background-color: rgba(0, 207, 106, 0.1);
  color: #00CF6A;
}

.release-item-date {
  display: flex;
  align-items: center;
}

.release-item-date input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 140px;
}

.dark .release-item-date input {
  background-color: #333;
  border-color: #444;
  color: #eee;
}

/* Styles for edit mode are now handled by progress-slider */

.edit-btn, .delete-btn, .save-btn, .cancel-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  border: none;
}

.edit-btn {
  background-color: #0033C9;
  color: white;
}

.edit-btn:hover {
  background-color: #0029a0;
}

.delete-btn {
  background-color: #ff4d4f;
  color: white;
}

.delete-btn:hover {
  background-color: #ff3333;
}

.save-btn {
  background-color: #00CF6A;
  color: white;
}

.save-btn:hover {
  background-color: #00b85e;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.dark .cancel-btn {
  background-color: #333;
  color: #eee;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.dark .cancel-btn:hover {
  background-color: #444;
}

/* Form styles */
.release-form {
  border-color: #0033C9;
}

.markdown-editor-container {
  border-radius: 4px;
  overflow: hidden;
}

/* Fix for MDEditor visibility issues */
/* .w-md-editor {
  background-color: #fff;
}

.dark .w-md-editor {
  background-color: #1e1e1e;
}

.w-md-editor-text {
  color: #333;
}

.dark .w-md-editor-text {
  color: #eee;
} */
/*
.w-md-editor-text-input {
  color: #333 !important;
  background-color: #fff !important;
}

.dark .w-md-editor-text-input {
  color: #eee !important;
  background-color: #1e1e1e !important;
}

.w-md-editor-text-pre {
  color: #333 !important;
}

.dark .w-md-editor-text-pre {
  color: #eee !important;
}

.w-md-editor-toolbar {
  background-color: #f6f6f6;
  border-bottom: 1px solid #ddd;
}

.dark .w-md-editor-toolbar {
  background-color: #2d2d2d;
  border-bottom: 1px solid #444;
}

.w-md-editor-toolbar-divider {
  background-color: #ddd;
}

.dark .w-md-editor-toolbar-divider {
  background-color: #444;
}

.w-md-editor-toolbar button {
  color: #555;
}

.dark .w-md-editor-toolbar button {
  color: #bbb;
}

.w-md-editor-toolbar button:hover {
  color: #000;
  background-color: #e6e6e6;
}

.dark .w-md-editor-toolbar button:hover {
  color: #fff;
  background-color: #444;
}

.w-md-editor-preview {
  background-color: #fcfcfc;
  border-left: 1px solid #ddd;
}

.dark .w-md-editor-preview {
  background-color: #252525;
  border-left: 1px solid #444;
  color: #eee;
} */

/* MDEditor styling */
.markdown-editor-container {
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 20px;
}

/* Basic styling for MDEditor */
.w-md-editor {
  border: 1px solid #ddd;
}

.dark .w-md-editor {
  border-color: #444;
}

/* Fix for editor content area */
/* .w-md-editor-content {
  height: 300px !important;
} */

.cancel-btn, .save-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.dark .cancel-btn {
  background-color: #444;
  color: #eee;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.dark .cancel-btn:hover {
  background-color: #555;
}

.save-btn {
  background-color: #00CF6A;
  color: white;
}

.save-btn:hover {
  background-color: #00b85e;
}

/* Content styling */

.release-content {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.release-content h1 {
  font-size: 24px;
  margin-top: 0;
}

.release-content h2 {
  font-size: 20px;
}

.release-content h3 {
  font-size: 18px;
}

.release-content ul, .release-content ol {
  padding-left: 24px;
  margin-bottom: 16px;
}

.release-content li {
  margin-bottom: 8px;
}

.release-content p {
  margin-bottom: 16px;
}

.release-content code {
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: monospace;
}

.dark .release-content code {
  background-color: #333;
}

/* Ensure markdown content is properly styled in detail view */
.release-content pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 16px;
}

.dark .release-content pre {
  background-color: #333;
}

.release-content blockquote {
  border-left: 4px solid #ddd;
  padding-left: 16px;
  margin-left: 0;
  color: #666;
}

.dark .release-content blockquote {
  border-left-color: #555;
  color: #aaa;
}

.release-content a {
  color: #0033C9;
  text-decoration: none;
}

.release-content a:hover {
  text-decoration: underline;
}

.dark .release-content a {
  color: #4d79ff;
}

.error-message {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.dark .error-message {
  background-color: rgba(255, 77, 79, 0.1);
  border-color: rgba(255, 77, 79, 0.3);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
  position: relative;
}

.loading-spinner::after {
  content: "";
  width: 30px;
  height: 30px;
  border: 3px solid #ddd;
  border-top-color: #0033C9;
  border-radius: 50%;
  animation: spinner 0.8s linear infinite;
  position: absolute;
  top: calc(50% - 15px);
  left: calc(50% - 15px);
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.dark .loading-spinner {
  color: #aaa;
}

.dark .loading-spinner::after {
  border-color: #444;
  border-top-color: #0033C9;
}

.no-releases-message {
  background-color: #f5f5f5;
  padding: 24px;
  border-radius: 8px;
  text-align: center;
  color: #666;
  font-size: 16px;
  margin: 24px 0;
}

.dark .no-releases-message {
  background-color: #333;
  color: #aaa;
}
