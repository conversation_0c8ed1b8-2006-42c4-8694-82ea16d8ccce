.user-detail-container {
  margin: 0 auto;
  padding: 20px;
}

.user-detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.user-detail-header h2 {
  margin-left: 20px;
  font-size: 24px;
  color: var(--text-color, #333);
}

.user-detail-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.platform-section {
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: var(--card-shadow, 0 2px 4px rgba(0, 0, 0, 0.05));
  transition: all 0.3s ease;
  background-color: var(--content-bg, #ffffff);
}

.platform-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--hover-bg, #f9f9f9);
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  transition: background-color 0.3s;
}

.platform-title-bar:hover {
  background-color: var(--btn-secondary-hover, #f0f0f0);
}

.platform-title {
  margin: 0;
  font-size: 18px;
  color: var(--text-color, #333);
}

.toggle-icon {
  color: var(--text-secondary, #666);
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon svg {
  width: 20px;
  height: 20px;
}

.user-info-table {
  padding: 15px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.user-info-table table {
  width: 100%;
  border-collapse: collapse;
}

.user-info-table tr {
  border-bottom: 1px solid var(--border-color, #eee);
}

.user-info-table tr:last-child {
  border-bottom: none;
}

.user-info-table td {
  padding: 10px 0;
}

.info-label {
  width: 30%;
  font-weight: 600;
  color: var(--text-secondary, #555);
  text-transform: capitalize;
}

.user-info-value {
  width: 70%;
  color: var(--text-color, #333);
}

.array-values-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.array-value-item {
  display: inline-block;
  background-color: var(--btn-secondary-bg, #f0f0f0);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--text-color, #333);
}

.loading {
  display: flex;
  justify-content: center;
  padding: 40px;
  font-size: 18px;
  color: var(--text-secondary, #666);
}

.error-container {
  text-align: center;
  padding: 40px;
}

.error-message {
  color: var(--error-text, #d63031);
  margin-bottom: 20px;
  font-size: 16px;
  padding: 15px;
  background-color: var(--error-bg, #ffeeee);
  border-radius: 4px;
  border-left: 4px solid var(--error-text, #d63031);
}

.password-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.password-toggle-btn {
  background-color: var(--btn-secondary-bg, #f0f0f0);
  border: 1px solid var(--border-color, #ccc);
  border-radius: 4px;
  padding: 0 10px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
  transition: background-color 0.2s;
  color: var(--text-color, #333);
}

.password-toggle-btn:hover {
  background-color: var(--btn-secondary-hover, #e0e0e0);
}