/* Styles cho bảng và sắp xếp */
.tickets-table th {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.sort-icon {
  margin-left: 5px;
  font-size: 14px;
  vertical-align: middle;
}

.sort-inactive {
  opacity: 0.3;
}

/* Styles cho action buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s;
  color: #555;
}

.btn-icon.view:hover {
  background-color: #e3f2fd;
  color: var(--secondary-light-blue);;
}

.btn-icon.edit:hover {
  background-color: #e8f5e9;
  color: #4caf50;
}

.btn-icon.delete:hover {
  background-color: #ffebee;
  color: #f44336;
}

/* Styles cho pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 5px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  background-color: white;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  min-width: 40px;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f7fb;
}

.pagination-btn.active {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Ticket details styles */
.ticket-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ticket-detail-row {
  display: flex;
  align-items: flex-start;
}

.ticket-detail-label {
  width: 120px;
  font-weight: 500;
  color: #555;
}

.ticket-detail-value {
  flex: 1;
}

.detail-input,
.detail-select,
.detail-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.detail-textarea {
  min-height: 100px;
  resize: vertical;
}

/* Delete confirmation */
.delete-confirmation {
  text-align: center;
  padding: 20px 0;
}

.delete-confirmation p {
  margin-bottom: 15px;
}

/* Thêm vào cuối file */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

/* Table Styles */
.tickets-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.tickets-table th,
.tickets-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.tickets-table th {
  background-color: #f5f7fb;
  font-weight: 500;
  color: #555;
}

.tickets-table tr:hover {
  background-color: #f9f9f9;
}

/* Tickets Filters */
.tickets-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

/* Dark mode support */
[data-theme="dark"] .tickets-table th {
  background-color: #22272e;
  color: #adbac7;
}

[data-theme="dark"] .tickets-table th,
[data-theme="dark"] .tickets-table td {
  border-color: #444c56;
}

[data-theme="dark"] .tickets-table tr:hover {
  background-color: #2d3339;
}

[data-theme="dark"] .tickets-filters {
  border-color: #444c56;
}

[data-theme="dark"] .filter-group label {
  color: #adbac7;
}

[data-theme="dark"] .filter-group select {
  background-color: var(--input-bg);
  border-color: var(--border-color);
  /* border-color: #444c56; */
  color: #adbac7;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tickets-filters {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-group select {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .tickets-table th,
  .tickets-table td {
    padding: 10px 8px;
    font-size: 13px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
} 