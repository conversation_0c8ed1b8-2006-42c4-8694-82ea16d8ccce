/* Dashboard Styles */
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
}

.summary-card {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  /* --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); */
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-content {
  z-index: 1;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
}

.card-number {
  font-size: 32px;
  font-weight: 700;
}

.card-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

/* Card color variants */
.pending-card .card-number {
  color: #6366f1;
}

.pending-card .card-icon {
  background-color: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.approved-card .card-number {
  color: #10b981;
}

.approved-card .card-icon {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.data-sources-card .card-number {
  color: #3b82f6;
}

.data-sources-card .card-icon {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.reports-card .card-number {
  color: #8b5cf6;
}

.reports-card .card-icon {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

/* Section Containers */
.section-container {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

/* Recent Activity */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  background-color: #f9fafb;
  transition: background-color 0.2s;
}

.activity-item:hover {
  background-color: #f3f4f6;
}

.activity-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 16px;
}

.activity-icon.approved {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.activity-icon.new {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.activity-icon.pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #333;
}

.activity-description {
  color: #666;
  font-size: 14px;
}

.activity-time {
  color: #888;
  font-size: 13px;
  white-space: nowrap;
}

/* Dashboard Grid for bottom sections */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 16px;
  border-radius: 8px;
  border: none;
  background-color: #f9fafb;
  color: #333;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.quick-action-btn:hover {
  transform: translateX(5px);
}

.quick-action-btn .btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.data-btn:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.report-btn:hover {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.platform-btn:hover {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

/* My Access */
.access-section {
  margin-bottom: 16px;
}

.subsection-title {
  font-size: 15px;
  font-weight: 500;
  color: #555;
  margin-bottom: 12px;
}

.access-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.access-tag {
  padding: 6px 12px;
  background-color: #f3f4f6;
  border-radius: 16px;
  font-size: 13px;
  color: #555;
  transition: all 0.2s;
  cursor: pointer;
}

.access-tag:hover {
  background-color: #e5e7eb;
  color: #333;
}

/* System Status */
.system-status {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-overview {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.status-icon.operational {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.status-time {
  font-size: 13px;
  color: #888;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.status-label {
  font-size: 14px;
  color: #555;
  width: 120px;
}

.status-bar {
  flex: 1;
  height: 8px;
  background-color: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.status-progress {
  height: 100%;
  background-color: #10b981;
  border-radius: 4px;
}

.status-value {
  font-size: 14px;
  font-weight: 500;
  color: #10b981;
  width: 60px;
  text-align: right;
}

/* Dark Mode Support */
[data-theme="dark"] .summary-card,
[data-theme="dark"] .section-container {
  background-color: #1f2937;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .section-title,
[data-theme="dark"] .activity-title,
[data-theme="dark"] .status-title {
  color: #e5e7eb;
}

[data-theme="dark"] .card-title,
[data-theme="dark"] .activity-description,
[data-theme="dark"] .subsection-title,
[data-theme="dark"] .status-label {
  color: #9ca3af;
}

[data-theme="dark"] .activity-item {
  background-color: var(--content-bg);
}

[data-theme="dark"] .activity-item:hover {
  background-color: #1f2937;
}

[data-theme="dark"] .quick-action-btn {
  background-color: var(--input-bg);
  color: #e5e7eb;
}

[data-theme="dark"] .access-tag {
  background-color: var(--input-bg);
  color: #9ca3af;
}

[data-theme="dark"] .access-tag:hover {
  background-color: #1f2937;
  color: #e5e7eb;
}

[data-theme="dark"] .status-bar {
  background-color: var(--input-bg);
}

[data-theme="dark"] .status-overview {
  border-bottom-color: #374151;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }

  .activity-item {
    flex-direction: column;
  }

  .activity-time {
    align-self: flex-end;
  }
}