/* Custom styles for ReactQuill editor */

/* Wrapper and label styles */
.quill-editor-wrapper {
  margin-bottom: 20px;
}

.quill-editor-label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #555;
}

.required-mark {
  color: #ef4444;
  margin-left: 4px;
}

[data-theme="dark"] .quill-editor-label {
  color: #adbac7;
}

[data-theme="dark"] .required-mark {
  color: #f87171;
}

/* Main container */
.custom-quill-editor {
  /* border: 1px solid #ddd; */
  border-radius: 4px;
  font-family: inherit;
}

/* Dark mode support */
[data-theme="dark"] .custom-quill-editor {
  /* border-color: #444; */
  background-color: var(--input-bg);
  color: #e2e8f0;
}

/* Toolbar styles */
.custom-quill-editor .ql-toolbar {
  border-bottom: 1px solid #ddd;
  background-color: #f8f9fa;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 8px;
}

[data-theme="dark"] .custom-quill-editor .ql-toolbar {
  background-color: #1a202c;
  border-color: #444;
}

/* Toolbar buttons */
.custom-quill-editor .ql-toolbar button {
  margin-right: 5px;
}

.custom-quill-editor .ql-toolbar button:hover {
  color: #00CF6A; /* Primary green color */
}

.custom-quill-editor .ql-toolbar button.ql-active {
  color: #00CF6A;
}

[data-theme="dark"] .custom-quill-editor .ql-toolbar button {
  color: #cbd5e0;
}

[data-theme="dark"] .custom-quill-editor .ql-toolbar button:hover,
[data-theme="dark"] .custom-quill-editor .ql-toolbar button.ql-active {
  color: #00CF6A;
}

/* Editor area */
.custom-quill-editor .ql-container {
  font-size: 16px;
  height: calc(100% - 42px);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

[data-theme="dark"] .custom-quill-editor .ql-container {
  background-color: #2d3748;
}

/* Editor content area */
.custom-quill-editor .ql-editor {
  padding: 12px 15px;
  min-height: 150px;
  line-height: 1.5;
}

[data-theme="dark"] .custom-quill-editor .ql-editor {
  background-color: var(--input-bg);
  color: #e2e8f0;
}

/* Placeholder text */
.custom-quill-editor .ql-editor.ql-blank::before {
  font-style: italic;
  color: #aaa;
}

[data-theme="dark"] .custom-quill-editor .ql-editor.ql-blank::before {
  color: #718096;
}

/* Format styles */
.custom-quill-editor .ql-editor h1 {
  font-size: 2em;
  margin-bottom: 0.5em;
}

.custom-quill-editor .ql-editor h2 {
  font-size: 1.5em;
  margin-bottom: 0.5em;
}

.custom-quill-editor .ql-editor h3 {
  font-size: 1.17em;
  margin-bottom: 0.5em;
}

.custom-quill-editor .ql-editor p {
  margin-bottom: 0.8em;
}

.custom-quill-editor .ql-editor ul,
.custom-quill-editor .ql-editor ol {
  padding-left: 1.5em;
  margin-bottom: 0.8em;
}

/* Link styles */
.custom-quill-editor .ql-editor a {
  color: #0033C9; /* Primary blue color */
  text-decoration: underline;
}

[data-theme="dark"] .custom-quill-editor .ql-editor a {
  color: #63b3ed;
}

/* Dropdown menus */
.custom-quill-editor .ql-toolbar .ql-picker {
  color: #333;
}

[data-theme="dark"] .custom-quill-editor .ql-toolbar .ql-picker {
  color: #cbd5e0;
}

.custom-quill-editor .ql-toolbar .ql-picker-options {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .custom-quill-editor .ql-toolbar .ql-picker-options {
  background-color: #2d3748;
  border-color: #444;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Color picker */
.custom-quill-editor .ql-toolbar .ql-color-picker .ql-picker-item {
  border-radius: 2px;
}

/* Focus state */
.custom-quill-editor:focus-within {
  border-color: #00CF6A;
  box-shadow: 0 0 0 2px rgba(0, 207, 106, 0.2);
}

[data-theme="dark"] .custom-quill-editor:focus-within {
  border-color: #00CF6A;
  box-shadow: 0 0 0 2px rgba(0, 207, 106, 0.3);
}

/* Advanced Editor Styles */
.advanced-quill-wrapper {
  margin-bottom: 30px;
}

.advanced-editor {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.advanced-editor .ql-toolbar {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background: linear-gradient(to right, #f8f9fa, #f0f0f0);
  padding: 10px;
}

[data-theme="dark"] .advanced-editor .ql-toolbar {
  background: linear-gradient(to right, #1a202c, #2d3748);
}

.advanced-editor .ql-container {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.advanced-editor .ql-editor {
  min-height: 200px;
  font-size: 16px;
  line-height: 1.6;
}

/* Custom highlight format */
.advanced-editor .ql-editor mark {
  padding: 0 2px;
  border-radius: 2px;
}

.editor-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
}

[data-theme="dark"] .editor-info {
  background-color: #2d3748;
  color: #adbac7;
}

.editor-info p {
  margin: 5px 0;
}

/* Custom theme toolbar */
.custom-theme-toolbar {
  border-color: #00CF6A !important;
}

.custom-theme-editor {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .custom-quill-editor .ql-toolbar {
    flex-wrap: wrap;
    padding: 5px;
  }

  .custom-quill-editor .ql-toolbar button {
    margin-bottom: 5px;
  }

  .advanced-editor .ql-toolbar {
    padding: 8px 5px;
  }

  .advanced-editor .ql-editor {
    min-height: 150px;
  }
}
