/**
 * Custom Quill theme creator
 * 
 * This file demonstrates how to create a custom theme for ReactQuill
 * by extending the existing themes.
 */

// Function to create a custom theme
export const createCustomTheme = (Quill) => {
  if (!Quill) return;
  
  // Get the existing Snow theme
  const snowTheme = Quill.import('themes/snow');
  
  // Create a custom theme by extending the Snow theme
  class CustomTheme extends snowTheme {
    constructor(quill, options) {
      // Call the parent constructor
      super(quill, options);
      
      // Add custom initialization logic here
      this.quill.root.classList.add('custom-theme-editor');
    }
    
    // Override methods as needed
    extendToolbar(toolbar) {
      // Call the parent method
      super.extendToolbar(toolbar);
      
      // Add custom toolbar logic here
      toolbar.container.classList.add('custom-theme-toolbar');
    }
  }
  
  // Register the custom theme
  Quill.register('themes/custom', CustomTheme);
  
  return CustomTheme;
};

// Function to register custom formats
export const registerCustomFormats = (Quill) => {
  if (!Quill) return;
  
  // Example: Create a custom inline format for highlighting
  const Inline = Quill.import('blots/inline');
  
  class HighlightBlot extends Inline {
    static create(value) {
      const node = super.create();
      node.setAttribute('style', `background-color: ${value}`);
      return node;
    }
    
    static formats(node) {
      return node.getAttribute('style').split('background-color: ')[1];
    }
  }
  
  HighlightBlot.blotName = 'highlight';
  HighlightBlot.tagName = 'mark';
  
  // Register the custom format
  Quill.register('formats/highlight', HighlightBlot);
};

// Function to add custom handlers
export const addCustomHandlers = (quillInstance) => {
  if (!quillInstance) return;
  
  // Example: Add a custom handler for the highlight button
  const toolbar = quillInstance.getModule('toolbar');
  
  toolbar.addHandler('highlight', function(value) {
    const quill = this.quill;
    const range = quill.getSelection();
    
    if (range) {
      if (value) {
        quill.formatText(range.index, range.length, 'highlight', value);
      } else {
        quill.formatText(range.index, range.length, 'highlight', false);
      }
    }
  });
};

// Example of how to use these functions in a React component:
/*
import React, { useEffect, useRef } from 'react';
import ReactQuill from 'react-quill-new';
import { createCustomTheme, registerCustomFormats, addCustomHandlers } from './quillCustomTheme';

const CustomEditor = () => {
  const quillRef = useRef(null);
  
  useEffect(() => {
    if (quillRef.current) {
      const Quill = quillRef.current.getEditor().constructor;
      
      // Register custom theme
      createCustomTheme(Quill);
      
      // Register custom formats
      registerCustomFormats(Quill);
      
      // Add custom handlers
      const quillInstance = quillRef.current.getEditor();
      addCustomHandlers(quillInstance);
    }
  }, []);
  
  return (
    <ReactQuill
      ref={quillRef}
      theme="custom" // Use the custom theme
      modules={{
        toolbar: {
          container: [
            ['bold', 'italic', 'underline'],
            [{ 'highlight': ['yellow', 'pink', 'lightblue'] }], // Custom format
            ['clean']
          ]
        }
      }}
    />
  );
};

export default CustomEditor;
*/
