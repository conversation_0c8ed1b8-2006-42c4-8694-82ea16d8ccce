/**
 * Custom Quill themes and configurations
 */

// Custom toolbar options
export const toolbarOptions = {
  minimal: [
    ['bold', 'italic', 'underline'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    ['link'],
    ['clean']
  ],
  
  standard: [
    [{ 'header': [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'color': [] }, { 'background': [] }],
    ['link', 'image'],
    ['clean']
  ],
  
  advanced: [
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'font': [] }, { 'align': [] }],
    ['link', 'image', 'video'],
    ['blockquote', 'code-block'],
    ['clean']
  ]
};

// Custom formats
export const formats = [
  'header',
  'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'indent',
  'color', 'background',
  'font', 'align',
  'link', 'image', 'video',
  'blockquote', 'code-block'
];

// Custom modules
export const getModules = (toolbarType = 'standard') => {
  return {
    toolbar: {
      container: toolbarOptions[toolbarType] || toolbarOptions.standard,
      handlers: {
        // Custom handlers can be added here
      }
    },
    clipboard: {
      matchVisual: false // Prevents unwanted HTML paste artifacts
    },
    history: {
      delay: 1000,
      maxStack: 100,
      userOnly: true
    }
  };
};

// Custom Quill editor configurations
export const editorConfigs = {
  // Simple editor for basic text formatting
  simple: {
    theme: 'snow',
    modules: getModules('minimal'),
    formats: ['bold', 'italic', 'underline', 'list', 'bullet', 'link'],
    placeholder: 'Enter your text here...',
    style: {
      height: '150px',
      borderRadius: '4px'
    }
  },
  
  // Standard editor for most use cases
  standard: {
    theme: 'snow',
    modules: getModules('standard'),
    formats: formats,
    placeholder: 'Enter your text here...',
    style: {
      height: '200px',
      borderRadius: '4px'
    }
  },
  
  // Rich editor for advanced content creation
  rich: {
    theme: 'snow',
    modules: getModules('advanced'),
    formats: formats,
    placeholder: 'Enter your text here...',
    style: {
      height: '300px',
      borderRadius: '4px'
    }
  },
  
  // Bubble theme for inline editing
  bubble: {
    theme: 'bubble',
    modules: {
      toolbar: [
        ['bold', 'italic', 'link'],
        [{ 'header': 1 }, { 'header': 2 }],
        ['clean']
      ]
    },
    formats: ['bold', 'italic', 'link', 'header'],
    placeholder: 'Enter your text here...',
    style: {
      height: '150px',
      borderRadius: '4px'
    }
  }
};
