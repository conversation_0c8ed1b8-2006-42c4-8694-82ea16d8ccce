import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiArrowLeft,
  FiEdit2,
  FiSave,
  FiX,
  FiUser,
  FiMail,
  FiKey,
  FiShield,
} from "react-icons/fi";
import { BiUserCircle } from "react-icons/bi";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import { useTheme } from "../context/ThemeContext";
import { FiAlertCircle } from "react-icons/fi";
import "../styles/Profile.css";

const Profile = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({
    display_name: "",
    email: "",
    current_password: "",
    new_password: "",
    confirm_password: "",
  });
  const [successMessage, setSuccessMessage] = useState("");

  // Fetch user profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);
        // For demo purposes, using the provided API response directly
        // In a real implementation, you would use: const response = await apiService.get(ENDPOINTS.AUTH.PROFILE);
        // const response = {
        //   "id": 1,
        //   "username": "huyhh2",
        //   "display_name": "Huy. Hoàng Hải (2)",
        //   "role": "approver",
        //   "status": "active",
        //   "created_at": "2025-04-04T15:52:13.527569",
        //   "updated_at": "2025-04-28T21:18:09.983364",
        //   "given_name": "Huy",
        //   "surname": "Hoàng Hải",
        //   "department_code": "ZDS",
        //   "department": "Zalopay Data Services",
        //   "job_title": "Staff Data Engineer",
        //   "manager": "vinhtx2"
        // };
        const response = await apiService.get(ENDPOINTS.AUTH.PROFILE);

        if (response) {
          setProfileData(response);
          setFormData({
            display_name: response.display_name || "",
            email: response.email || "",
            current_password: "",
            new_password: "",
            confirm_password: "",
          });
        } else {
          setError("No profile data found");
        }
      } catch (error) {
        console.error("Error fetching profile data:", error);
        setError("Unable to load profile data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Validate form data
      if (
        formData.new_password &&
        formData.new_password !== formData.confirm_password
      ) {
        setError("New passwords do not match");
        return;
      }

      // Prepare data for API
      const updateData = {
        display_name: formData.display_name,
        email: formData.email,
      };

      // Only include password fields if the user is changing password
      if (formData.current_password && formData.new_password) {
        updateData.current_password = formData.current_password;
        updateData.new_password = formData.new_password;
      }

      // Call API to update profile
      const response = await apiService.post(
        ENDPOINTS.USERS.UPDATE,
        updateData
      );

      if (response) {
        // Update local user data
        const userData = JSON.parse(localStorage.getItem("user"));
        userData.name = formData.display_name;
        localStorage.setItem("user", JSON.stringify(userData));

        // Update profile data state
        setProfileData({
          ...profileData,
          display_name: formData.display_name,
          email: formData.email,
        });

        // Show success message
        setSuccessMessage("Profile updated successfully");
        setTimeout(() => setSuccessMessage(""), 3000);

        // Exit edit mode
        setEditMode(false);

        // Clear password fields
        setFormData({
          ...formData,
          current_password: "",
          new_password: "",
          confirm_password: "",
        });
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      setError("Failed to update profile. Please try again.");
      setTimeout(() => setError(""), 3000);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditMode(false);
    // Reset form data to current profile data
    if (profileData) {
      setFormData({
        display_name: profileData.display_name || "",
        email: profileData.email || "",
        current_password: "",
        new_password: "",
        confirm_password: "",
      });
    }
    setError(null);
  };

  // Go back to previous page
  const handleGoBack = () => {
    navigate(-1);
  };

  if (loading) {
    return (
      <div className="profile-container">
        <div className="loading-indicator">Loading profile data...</div>
      </div>
    );
  }

  return (
    <div className="profile-container">
      <div className="profile-header">
        <button className="back-button" onClick={handleGoBack}>
          <FiArrowLeft /> Back
        </button>
        <h2>User Profile</h2>
        {!editMode ? (
          <button
            className="edit-button"
            onClick={() => setEditMode(true)}
            disabled
          >
            <FiEdit2 /> Edit Profile
          </button>
        ) : (
          <button className="cancel-button" onClick={handleCancelEdit}>
            <FiX /> Cancel
          </button>
        )}
      </div>

      {error && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}
      {successMessage && (
        <div className="success-message">{successMessage}</div>
      )}

      <div className="profile-content">
        <div className="profile-avatar-section">
          <div className="profile-avatar">
            <BiUserCircle className="avatar-icon" />
          </div>
          <div className="profile-basic-info">
            <h3>{profileData?.display_name || "User"}</h3>
            <p className="user-role">
              {profileData?.role === "admin"
                ? "Administrator"
                : profileData?.role === "approver"
                ? "Approver"
                : "User"}
            </p>
            <p className="user-username">{profileData?.username}</p>
            <p className="user-job-title">{profileData?.job_title}</p>
            <p className="user-department">{profileData?.department}</p>
          </div>
        </div>

        <div className="profile-details">
          {editMode ? (
            <form onSubmit={handleSubmit} className="profile-edit-form">
              <div className="form-section">
                <h3>Personal Information</h3>
                <div className="form-group">
                  <label>
                    <FiUser /> Display Name
                  </label>
                  <input
                    type="text"
                    name="display_name"
                    value={formData.display_name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>
                    <FiMail /> Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="form-section">
                <h3>Change Password</h3>
                <div className="form-group">
                  <label>
                    <FiKey /> Current Password
                  </label>
                  <input
                    type="password"
                    name="current_password"
                    value={formData.current_password}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>
                    <FiKey /> New Password
                  </label>
                  <input
                    type="password"
                    name="new_password"
                    value={formData.new_password}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>
                    <FiKey /> Confirm New Password
                  </label>
                  <input
                    type="password"
                    name="confirm_password"
                    value={formData.confirm_password}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="form-actions">
                <button type="submit" className="save-button">
                  <FiSave /> Save Changes
                </button>
              </div>
            </form>
          ) : (
            <>
              <div className="info-section">
                <h3>Account Information</h3>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Username:
                  </span>
                  <span className="info-value">{profileData?.username}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Display Name:
                  </span>
                  <span className="info-value">
                    {profileData?.display_name}
                  </span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Given Name:
                  </span>
                  <span className="info-value">{profileData?.given_name}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Surname:
                  </span>
                  <span className="info-value">{profileData?.surname}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <FiShield /> Role:
                  </span>
                  <span className="info-value">
                    {profileData?.role === "admin"
                      ? "Administrator"
                      : profileData?.role === "approver"
                      ? "Approver"
                      : "User"}
                  </span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <FiShield /> Status:
                  </span>
                  <span className="info-value">{profileData?.status}</span>
                </div>
              </div>

              <div className="info-section">
                <h3>Professional Information</h3>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Job Title:
                  </span>
                  <span className="info-value">{profileData?.job_title}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Department:
                  </span>
                  <span className="info-value">
                    {profileData?.department} ({profileData?.department_code})
                  </span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Manager:
                  </span>
                  <span className="info-value">{profileData?.manager}</span>
                </div>
              </div>

              <div className="info-section">
                <h3>System Information</h3>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Created At:
                  </span>
                  <span className="info-value">
                    {new Date(profileData?.created_at).toLocaleString()}
                  </span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <FiUser /> Updated At:
                  </span>
                  <span className="info-value">
                    {new Date(profileData?.updated_at).toLocaleString()}
                  </span>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;
