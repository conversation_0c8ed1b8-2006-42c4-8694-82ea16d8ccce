import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiArrowLeft,
  FiPlus,
  FiMessageCircle,
  FiCalendar,
  FiUser,
  FiTag,
  FiAlertCircle,
  FiClock,
  FiCheckCircle,
  FiRefreshCw,
  FiX,
  FiDownload,
  FiEye,
  FiFile,
  FiImage,
} from "react-icons/fi";
import { useTheme } from "../context/ThemeContext";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import "../styles/Feedback.css";

const Feedback = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [feedbackList, setFeedbackList] = useState([]);
  const [selectedFeedback, setSelectedFeedback] = useState(null);
  const [showModal, setShowModal] = useState(false);

  // Fetch feedback list
  const fetchFeedback = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.get(ENDPOINTS.FEEDBACK.GET_ALL);
      setFeedbackList(response || []);
    } catch (err) {
      console.error("Error fetching feedback:", err);
      setError("Failed to load feedback list. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Load feedback on component mount
  useEffect(() => {
    fetchFeedback();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get category label
  const getCategoryLabel = (category) => {
    const categoryMap = {
      bug: "Bug Report",
      feature: "Feature Request",
      improvement: "Improvement Suggestion",
      general: "General Feedback",
      support: "Support Request",
      other: "Other",
    };
    return categoryMap[category] || category;
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    const colorMap = {
      low: "#10b981",
      medium: "#f59e0b",
      high: "#ef4444",
      urgent: "#dc2626",
    };
    return colorMap[priority] || "#6b7280";
  };

  // Get status color
  const getStatusColor = (status) => {
    const colorMap = {
      open: "#3b82f6",
      "in-progress": "#f59e0b",
      resolved: "#10b981",
      closed: "#6b7280",
    };
    return colorMap[status] || "#6b7280";
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "open":
        return <FiClock />;
      case "in-progress":
        return <FiRefreshCw />;
      case "resolved":
        return <FiCheckCircle />;
      case "closed":
        return <FiCheckCircle />;
      default:
        return <FiClock />;
    }
  };

  // Handle navigation to send feedback
  const handleAddFeedback = () => {
    navigate("/feedback/send");
  };

  // Handle back navigation
  const handleBack = () => {
    navigate("/dashboard");
  };

  // Handle feedback card click
  const handleFeedbackClick = (feedback) => {
    setSelectedFeedback(feedback);
    setShowModal(true);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedFeedback(null);
  };

  // Get file icon based on file type
  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

    if (imageExtensions.includes(extension)) {
      return <FiImage />;
    }
    return <FiFile />;
  };

  // Check if file is an image
  const isImageFile = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return imageExtensions.includes(extension);
  };

  // Handle file download
  const handleFileDownload = async (fileId, fileName) => {
    try {
      const response = await apiService.get(ENDPOINTS.FILES.DOWNLOAD(fileId), {
        responseType: 'blob'
      });

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  // Handle image preview
  const handleImagePreview = (fileId, fileName) => {
    const imageUrl = `${process.env.REACT_APP_API_URL || 'http://localhost:3014'}${ENDPOINTS.FILES.DOWNLOAD(fileId)}`;
    window.open(imageUrl, '_blank');
  };

  return (
    <div className="feedback-list-container">
      <div className="feedback-list-header">
        <button
          className="back-button"
          onClick={handleBack}
          title="Back to Dashboard"
        >
          <FiArrowLeft size={20} />
        </button>
        <div className="header-content">
          <div className="header-icon">
            <FiMessageCircle size={32} />
          </div>
          <div className="header-text">
            <h1>Feedback</h1>
            <p>View and manage feedback submissions</p>
          </div>
        </div>
        <button
          className="add-feedback-button"
          onClick={handleAddFeedback}
          title="Add New Feedback"
        >
          <FiPlus size={16} />
          Add Feedback
        </button>
      </div>

      <div className="feedback-list-content">
        {error && (
          <div className="error-message">
            <FiAlertCircle className="error-icon" />
            <span>{error}</span>
            <button
              className="retry-button"
              onClick={fetchFeedback}
              title="Retry"
            >
              <FiRefreshCw size={16} />
              Retry
            </button>
          </div>
        )}

        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading feedback...</p>
          </div>
        ) : feedbackList.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              <FiMessageCircle size={48} />
            </div>
            <h3>No Feedback Yet</h3>
            <p>No feedback has been submitted yet. Be the first to share your thoughts!</p>
            <button
              className="empty-action-button"
              onClick={handleAddFeedback}
            >
              <FiPlus size={16} />
              Add First Feedback
            </button>
          </div>
        ) : (
          <div className="feedback-grid">
            {feedbackList.map((feedback) => (
              <div
                key={feedback.id}
                className="feedback-card"
                onClick={() => handleFeedbackClick(feedback)}
              >
                <div className="feedback-card-header">
                  <div className="feedback-title">
                    <h3>{feedback.subject}</h3>
                    <div className="feedback-meta">
                      <span className="feedback-id">#{feedback.id}</span>
                      <span
                        className="feedback-status"
                        style={{ color: getStatusColor(feedback.status) }}
                      >
                        {getStatusIcon(feedback.status)}
                        {feedback.status}
                      </span>
                    </div>
                  </div>
                  <div className="feedback-priority">
                    <span
                      className="priority-badge"
                      style={{
                        backgroundColor: getPriorityColor(feedback.priority),
                        color: "white",
                      }}
                    >
                      {feedback.priority}
                    </span>
                  </div>
                </div>

                <div className="feedback-card-body">
                  <div className="feedback-description">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: feedback.description,
                      }}
                    />
                  </div>
                </div>

                <div className="feedback-card-footer">
                  <div className="feedback-details">
                    <div className="feedback-detail">
                      <FiTag size={14} />
                      <span>{getCategoryLabel(feedback.category)}</span>
                    </div>
                    <div className="feedback-detail">
                      <FiUser size={14} />
                      <span>User #{feedback.user_id}</span>
                    </div>
                    <div className="feedback-detail">
                      <FiCalendar size={14} />
                      <span>{formatDate(feedback.created_at)}</span>
                    </div>
                  </div>
                  {feedback.attachments && (
                    <div className="feedback-attachments">
                      <span className="attachment-count">
                        📎 {feedback.attachments.length} attachment(s)
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Feedback Detail Modal */}
      {showModal && selectedFeedback && (
        <div className="feedback-modal-overlay" onClick={handleCloseModal}>
          <div className="feedback-detail-modal" onClick={(e) => e.stopPropagation()}>
            <div className="feedback-modal-header">
              <div className="feedback-modal-title">
                <h2>Feedback Details</h2>
                <div className="feedback-modal-meta">
                  <span className="feedback-modal-id">#{selectedFeedback.id}</span>
                  <span
                    className="feedback-modal-status"
                    style={{ color: getStatusColor(selectedFeedback.status) }}
                  >
                    {getStatusIcon(selectedFeedback.status)}
                    {selectedFeedback.status}
                  </span>
                  <span
                    className="feedback-modal-priority"
                    style={{
                      backgroundColor: getPriorityColor(selectedFeedback.priority),
                      color: "white",
                    }}
                  >
                    {selectedFeedback.priority}
                  </span>
                </div>
              </div>
              <button
                className="feedback-modal-close"
                onClick={handleCloseModal}
                title="Close"
              >
                <FiX size={20} />
              </button>
            </div>

            <div className="feedback-modal-body">
              <div className="feedback-modal-section">
                <h3>{selectedFeedback.subject}</h3>
                <div className="feedback-modal-info">
                  <div className="feedback-modal-info-item">
                    <FiTag size={16} />
                    <span>{getCategoryLabel(selectedFeedback.category)}</span>
                  </div>
                  <div className="feedback-modal-info-item">
                    <FiUser size={16} />
                    <span>User #{selectedFeedback.user_id}</span>
                  </div>
                  <div className="feedback-modal-info-item">
                    <FiCalendar size={16} />
                    <span>{formatDate(selectedFeedback.created_at)}</span>
                  </div>
                  {selectedFeedback.updated_at && (
                    <div className="feedback-modal-info-item">
                      <FiClock size={16} />
                      <span>Updated: {formatDate(selectedFeedback.updated_at)}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="feedback-modal-section">
                <h4>Description</h4>
                <div
                  className="feedback-modal-description"
                  dangerouslySetInnerHTML={{
                    __html: selectedFeedback.description,
                  }}
                />
              </div>

              {selectedFeedback.attachments && selectedFeedback.attachments.length > 0 && (
                <div className="feedback-modal-section">
                  <h4>Attachments ({selectedFeedback.attachments.length})</h4>
                  <div className="feedback-modal-attachments">
                    {selectedFeedback.attachments.map((attachment, index) => (
                      <div key={index} className="feedback-modal-attachment">
                        <div className="attachment-info">
                          <div className="attachment-icon">
                            {getFileIcon(attachment.name || attachment.original_name || `file_${index}`)}
                          </div>
                          <div className="attachment-details">
                            <span className="attachment-name">
                              {attachment.name || attachment.original_name || `Attachment ${index + 1}`}
                            </span>
                            {attachment.size && (
                              <span className="attachment-size">
                                ({Math.round(attachment.size / 1024)} KB)
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="attachment-actions">
                          {isImageFile(attachment.name || attachment.original_name || '') && (
                            <button
                              className="attachment-action-btn preview"
                              onClick={() => handleImagePreview(attachment.id, attachment.name)}
                              title="Preview Image"
                            >
                              <FiEye size={16} />
                            </button>
                          )}
                          <button
                            className="attachment-action-btn download"
                            onClick={() => handleFileDownload(attachment.id, attachment.name || attachment.original_name)}
                            title="Download File"
                          >
                            <FiDownload size={16} />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Feedback;
