import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiArrowLeft,
  FiSearch,
  FiRefreshCw,
  FiAlertCircle,
  FiX,
  FiArrowUp,
  FiCalendar,
  FiActivity,
  FiDatabase,
  FiEye,
  FiFilter,
} from "react-icons/fi";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import "../styles/EventManagement.css";

const EventManagement = () => {
  const navigate = useNavigate();
  const [events, setEvents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Pagination state
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const PAGE_SIZE = 100;

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [productLineFilter, setProductLineFilter] = useState("");

  // Refs for infinite scroll
  const containerRef = useRef(null);

  // Back to top button state
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Fetch initial data on component mount
  useEffect(() => {
    document.title = "Event Management";
    fetchEvents(1, true);
  }, []);

  // Handle scroll for back to top button
  useEffect(() => {
    const handleScroll = () => {
      const scrollThreshold = window.innerHeight * 3;
      setShowBackToTop(window.scrollY > scrollThreshold);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Setup intersection observer for infinite scrolling
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (
          entries[0].isIntersecting &&
          hasMore &&
          !loadingMore &&
          events.length > 0
        ) {
          loadMoreEvents();
        }
      },
      { threshold: 0.1, rootMargin: "100px" }
    );

    const timer = setTimeout(() => {
      if (containerRef.current) {
        observer.observe(containerRef.current);
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, [hasMore, loadingMore, events.length]);

  // Fetch events from API with pagination, filtering, and search
  const fetchEvents = async (
    pageNum = 1,
    reset = false,
    searchTerm = searchQuery,
    statusFilterValue = statusFilter,
    productLineFilterValue = productLineFilter
  ) => {
    if (pageNum === 1) {
      setIsLoading(true);
    } else {
      setLoadingMore(true);
    }

    if (reset) {
      setError(null);
    }

    try {
      // Prepare request body for POST request
      const requestBody = {
        page: pageNum,
        limit: PAGE_SIZE,
      };

      // Add filters if they exist
      if (searchTerm) {
        requestBody.search = searchTerm;
      }
      if (statusFilterValue) {
        requestBody.event_status = statusFilterValue;
      }
      if (productLineFilterValue) {
        requestBody.product_line = productLineFilterValue;
      }

      const response = await apiService.post(
        ENDPOINTS.FUNNEL.GET_ALL_EVENTS,
        requestBody
      );

      let data = [];
      let total = 0;

      // Process API response
      if (response && response.result && Array.isArray(response.result)) {
        data = response.result;
        total = response.totalrows || 0;
      } else {
        throw new Error("Unexpected response format");
      }

      // Check if there are more items
      setHasMore(data.length >= PAGE_SIZE && (pageNum * PAGE_SIZE) < total);
      setTotalRows(total);

      // Update state
      if (reset || pageNum === 1) {
        setEvents(data);
        setPage(1);
      } else {
        setEvents((prevEvents) => [...prevEvents, ...data]);
        setPage(pageNum);
      }
    } catch (error) {
      console.error("Error fetching events:", error);
      if (reset || pageNum === 1) {
        setError("Failed to load events. Please try again.");
        setEvents([]);
        setHasMore(false);
      }
    } finally {
      if (pageNum === 1) {
        setIsLoading(false);
      } else {
        setLoadingMore(false);
      }
    }
  };

  // Load more events for infinite scrolling
  const loadMoreEvents = () => {
    if (!loadingMore && hasMore && events.length > 0) {
      fetchEvents(page + 1);
    }
  };

  // Debounce search function
  const useDebounce = (callback, delay) => {
    const timeoutRef = useRef(null);

    return (searchQuery) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(searchQuery);
      }, delay);
    };
  };

  // Handle search
  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  };

  // Debounced search
  const debouncedSearch = useDebounce((searchQuery) => {
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(1, true, searchQuery, statusFilter, productLineFilter);
  }, 500);

  // Handle status filter change
  const handleStatusFilterChange = (status) => {
    setStatusFilter(status);
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(1, true, searchQuery, status, productLineFilter);
  };

  // Handle product line filter change
  const handleProductLineFilterChange = (productLine) => {
    setProductLineFilter(productLine);
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(1, true, searchQuery, statusFilter, productLine);
  };

  // Clear all filters
  const clearFilters = () => {
    const emptySearch = "";
    const emptyStatus = "";
    const emptyProductLine = "";
    setSearchQuery(emptySearch);
    setStatusFilter(emptyStatus);
    setProductLineFilter(emptyProductLine);
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(1, true, emptySearch, emptyStatus, emptyProductLine);
  };

  // Refresh page function
  const refreshPage = () => {
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(1, true);
    setSuccessMessage("Page refreshed");
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // Format array data for display
  const formatArrayData = (data) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return "N/A";
    }
    return data.join(", ");
  };

  return (
    <div className="event-management-container">
      <div className="event-management-header">
        <button
          type="button"
          className="back-button"
          onClick={() => navigate("/dashboard")}
        >
          <FiArrowLeft /> Back
        </button>
        <h2 className="event-management-title">Event Management</h2>
      </div>

      {successMessage && (
        <div className="success-message">
          <FiActivity />
          <span>{successMessage}</span>
        </div>
      )}

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          className="back-to-top-button"
          onClick={scrollToTop}
          aria-label="Back to top"
        >
          <FiArrowUp />
        </button>
      )}

      {/* Filter and Search Bar */}
      <div className="filter-search-bar">
        <div className="search-container">
          {isLoading && searchQuery ? (
            <FiRefreshCw className="search-icon spinning" />
          ) : (
            <FiSearch className="search-icon" />
          )}
          <input
            type="text"
            placeholder="Search events by ID or name..."
            value={searchQuery}
            onChange={handleSearch}
            className="search-input"
          />
          {searchQuery && (
            <FiX
              className="clear-search-icon"
              onClick={() => {
                setSearchQuery("");
                setPage(1);
                setEvents([]);
                setHasMore(true);
                setIsLoading(true);
                fetchEvents(1, true, "", statusFilter, productLineFilter);
              }}
            />
          )}
        </div>

        <div className="filter-container">
          <label htmlFor="status-filter">Status:</label>
          <select
            id="status-filter"
            value={statusFilter}
            onChange={(e) => handleStatusFilterChange(e.target.value)}
            className="filter-select"
          >
            <option value="">All</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </select>
        </div>

        <div className="actions-container">
          {(searchQuery || statusFilter || productLineFilter) && (
            <button className="clear-filters-btn" onClick={clearFilters}>
              <FiFilter /> Clear Filters
            </button>
          )}
          <button
            type="button"
            className="btn-secondary"
            onClick={refreshPage}
            title="Refresh Page"
          >
            <FiRefreshCw /> Refresh
          </button>
        </div>
      </div>

      {/* Results Summary */}
      {!isLoading && events.length > 0 && (
        <div className="results-summary">
          <span>
            Showing {events.length} of {totalRows} events
            {hasMore && " (loading more as you scroll)"}
          </span>
        </div>
      )}

      {error && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}

      {isLoading && events.length === 0 ? (
        <div className="loading-spinner"></div>
      ) : events.length === 0 ? (
        <div className="empty-state">
          <FiDatabase className="empty-icon" />
          <p>No events found</p>
          {(searchQuery || statusFilter || productLineFilter) && (
            <button className="btn-secondary" onClick={clearFilters}>
              Clear Filters
            </button>
          )}
        </div>
      ) : (
        <>
          <div className="event-table-container">
            <table className="event-table">
              <thead>
                <tr>
                  <th>Event ID</th>
                  <th>Event Name</th>
                  <th>Event Status</th>
                  <th>Screen ID</th>
                  <th>Screen Name</th>
                  <th>Screen Status</th>
                  <th>Product Line</th>
                  <th>App ID</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {events.map((event) => (
                  <tr key={event.id}>
                    <td>{event.event_id || "N/A"}</td>
                    <td>{event.event_name || "N/A"}</td>
                    <td>
                      <span
                        className={`status-badge ${
                          event.event_status ? "active" : "inactive"
                        }`}
                      >
                        {event.event_status ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td>{event.screen_id || "N/A"}</td>
                    <td>{event.screen_name || "N/A"}</td>
                    <td>
                      <span
                        className={`status-badge ${
                          event.screen_status ? "active" : "inactive"
                        }`}
                      >
                        {event.screen_status ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td>{formatArrayData(event.product_line)}</td>
                    <td>{formatArrayData(event.app_id)}</td>
                    <td>
                      <button
                        className="btn-icon"
                        title="View Details"
                        onClick={() => {
                          // TODO: Implement view details functionality
                          console.log("View event details:", event);
                        }}
                      >
                        <FiEye />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Infinite scroll loading indicator */}
          {loadingMore && (
            <div className="loading-more">
              <div className="loading-spinner small"></div>
              <span>Loading more events...</span>
            </div>
          )}

          {/* Sentinel element for infinite scroll */}
          <div ref={containerRef} className="scroll-sentinel"></div>
        </>
      )}
    </div>
  );
};

export default EventManagement;
