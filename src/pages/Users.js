import React, { useState, useEffect, useRef } from "react";
import {
  FiEdit,
  FiTrash2,
  FiUserPlus,
  FiUserCheck,
  FiChevronUp,
  FiChevronDown,
  FiSearch,
  FiAlertCircle,
  FiEye,
  FiColumns,
  FiCheck,
} from "react-icons/fi";
import "../styles/Users.css";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import { useNavigate } from "react-router-dom";

const Users = () => {
  const navigate = useNavigate();
  const usernameInputRef = useRef(null);
  // Platforms
  const platforms = [
    { id: "dataquest", name: "DataQuest" },
    { id: "wlp", name: "WLP - zds.zalopay.vn" },
    { id: "ads", name: "ADS - ads.zalopay.vn" },
    { id: "sbv", name: "SBV - walletreport.zalopay.vn" },
    { id: "ecommerce", name: "Ecommerce - baocao.zalopay.vn" },
    { id: "mysql-997", name: "MySQL - 997" },
    { id: "mysql-998", name: "MySQL - 998" },
  ];

  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(() => {
    const savedItemsPerPage = localStorage.getItem("itemsPerPage");
    return savedItemsPerPage ? parseInt(savedItemsPerPage, 10) : 10;
  });
  const [sortConfig, setSortConfig] = useState({
    key: "updated_at",
    direction: "descending",
  });
  const [filters, setFilters] = useState(() => {
    const savedPlatform = localStorage.getItem("currentPlatform");
    // Validate if savedPlatform is a valid platform ID
    const isValidPlatform = platforms.some((p) => p.id === savedPlatform);
    return {
      platform: isValidPlatform ? savedPlatform : "dataquest",
      status: "all",
      search: "",
    };
  });
  const [selectedUser, setSelectedUser] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState("view"); // 'view', 'edit', 'add', 'delete'
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [modalError, setModalError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [columns, setColumns] = useState([]);
  const [visibleColumns, setVisibleColumns] = useState(() => {
    const savedVisibleColumns = localStorage.getItem("visibleColumns");
    return savedVisibleColumns ? JSON.parse(savedVisibleColumns) : [];
  });
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const initialLoadDone = useRef(false);
  const columnSelectorRef = useRef(null);

  // Status options
  const statusOptions = [
    { id: "all", name: "All" },
    { id: "active", name: "Active" },
    { id: "inactive", name: "Inactive" },
    { id: "remove", name: "Removed" },
  ];

  // Role options for different platforms
  const adsRoleOptions = [
    { value: "all", label: "All" },
    { value: "tnex-all", label: "TNEX ALL" },
    { value: "tnex-view", label: "TNEX View" },
    { value: "sunlife-all", label: "Sunlife ALL" },
    { value: "sunlife-view", label: "Sunlife View" },
    { value: "shinhan-all", label: "Shinhan ALL" },
    { value: "shinhan-view", label: "Shinhan View" },
    { value: "affiliate_accesstrade", label: "Affiliate AccessTrade" },
    { value: "affiliate_adflex", label: "Affiliate Adflex" },
    { value: "affiliate_dinos", label: "Affiliate Dinos" },
    { value: "affiliate_omega", label: "Affiliate Omega" },
    { value: "affiliate_omega-v2", label: "Affiliate Omega-v2" },
    { value: "affiliate_tecdo", label: "Affiliate Tecdo" },
    { value: "affiliate_netmarvel", label: "Affiliate Netmarvel" },
    { value: "affiliate_mobivision", label: "Affiliate Mobivision" },
    { value: "affiliate_others", label: "Affiliate Others" },
    { value: "samsung", label: "Samsung" },
    { value: "samsung_display", label: "Samsung Display" },
    { value: "accounting", label: "Accounting" },
    { value: "ila_edu", label: "ILA Edu" },
    { value: "test_security", label: "Test Security" },
  ];

  // WLP specific role options
  const wlpRoleOptions = [
    { value: "all", label: "All" },
    { value: "whitelist", label: "Whitelist" },
    { value: "zns", label: "ZNS" },
    { value: "funnel", label: "Funnel" },
    { value: "config_tool", label: "Config Tool" },
    // { value: "sbv", label: "SBV" },
  ];

  // Role level 1 options for different platforms
  const roleLvl1Options = [
    { value: "funnel_event", label: "Funnel Event" },
    { value: "funnel_report", label: "Funnel Report" },
    { value: "config_tool_admin", label: "Config Tool Admin" },
    { value: "config_tool_approve", label: "Config Tool Approver User" },
    { value: "config_tool_edit", label: "Config Tool Edit User" },
    { value: "config_tool_view", label: "Config Tool View User" },
  ];

  // Role level 2 options for different platforms
  const roleLvl2Options = [];

  // Role level 3 options for different platforms
  const roleLvl3Options = [];

  // Fetch users from API
  const fetchUsers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiService.get(
        ENDPOINTS.USERS.GET_ALL(filters.platform)
      );
      setUsers(response);

      // Extract columns from the first user object
      if (response && response.length > 0) {
        const firstUser = response[0];
        const extractedColumns = Object.keys(firstUser).map((key) => ({
          key,
          label: formatColumnLabel(key),
          visible:
            visibleColumns.length === 0 ? true : visibleColumns.includes(key),
        }));
        setColumns(extractedColumns);

        // Initialize visible columns if not already set
        if (visibleColumns.length === 0) {
          // Default visible columns - show a reasonable subset by default
          const defaultVisibleColumns = [
            "id",
            "username",
            "display_name",
            "role",
            "status",
            "updated_at",
          ].filter((col) => Object.keys(firstUser).includes(col));

          setVisibleColumns(defaultVisibleColumns);
          localStorage.setItem(
            "visibleColumns",
            JSON.stringify(defaultVisibleColumns)
          );
        }
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      setError("Unable to load user data. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  // Format column label from key
  const formatColumnLabel = (key) => {
    // Convert snake_case or camelCase to Title Case
    return key
      .replace(/_/g, " ")
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  // Fetch users when platform filter changes
  useEffect(() => {
    // Always fetch on initial load
    if (!initialLoadDone.current) {
      fetchUsers();
      localStorage.setItem("currentPlatform", filters.platform);
      initialLoadDone.current = true;
    }
    // For subsequent platform changes, fetch data
    else if (filters.platform) {
      fetchUsers();
      localStorage.setItem("currentPlatform", filters.platform);
    }
  }, [filters.platform]);

  // Handle filtering and sorting
  useEffect(() => {
    let result = [...users];

    // Apply status filter
    if (filters.status !== "all") {
      result = result.filter((user) => user.status === filters.status);
    }

    // Apply search
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      result = result.filter((user) =>
        Object.values(user).some(
          (value) =>
            value &&
            typeof value === "string" &&
            value.toLowerCase().includes(searchLower)
        )
      );
    }

    // Apply sorting
    if (sortConfig.key) {
      result.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    }

    setFilteredUsers(result);
  }, [users, filters, sortConfig]);

  // Handle sorting when clicking on table header
  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  // Display sort icon
  const getSortIcon = (columnName) => {
    if (sortConfig.key !== columnName) {
      return <span className="sort-icon sort-inactive">⇅</span>;
    }

    return sortConfig.direction === "ascending" ? (
      <FiChevronUp className="sort-icon" />
    ) : (
      <FiChevronDown className="sort-icon" />
    );
  };

  // Handle pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredUsers.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

  // Handle page navigation
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const nextPage = () =>
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage((prev) => Math.max(prev - 1, 1));
  const firstPage = () => setCurrentPage(1);
  const lastPage = () => setCurrentPage(totalPages);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;

    // Handle special sort logic
    if (name === "sort") {
      if (value === "newest") {
        setSortConfig({ key: "updated_at", direction: "descending" });
      } else if (value === "oldest") {
        setSortConfig({ key: "updated_at", direction: "ascending" });
      }
    }

    setFilters((prev) => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Reset to page 1 when filter changes

    // Update localStorage when platform changes
    if (name === "platform") {
      localStorage.setItem("currentPlatform", value);
    }
  };

  // Toggle column selector dropdown
  const toggleColumnSelector = () => {
    setShowColumnSelector((prev) => !prev);
  };

  // Handle column visibility toggle
  const toggleColumnVisibility = (columnKey) => {
    setVisibleColumns((prev) => {
      let newVisibleColumns;
      if (prev.includes(columnKey)) {
        // Don't allow removing all columns - keep at least one
        if (prev.length <= 1) return prev;
        newVisibleColumns = prev.filter((key) => key !== columnKey);
      } else {
        newVisibleColumns = [...prev, columnKey];
      }

      // Save to localStorage
      localStorage.setItem("visibleColumns", JSON.stringify(newVisibleColumns));
      return newVisibleColumns;
    });
  };

  // Close column selector when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        columnSelectorRef.current &&
        !columnSelectorRef.current.contains(event.target)
      ) {
        setShowColumnSelector(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Xử lý thay đổi số lượng items mỗi trang
  const handleItemsPerPageChange = (e) => {
    const value = parseInt(e.target.value, 10);
    setItemsPerPage(value);
    localStorage.setItem("itemsPerPage", value.toString());
    // Reset về trang 1 khi thay đổi số lượng items
    setCurrentPage(1);
    localStorage.setItem("currentPage", "1");
  };

  // Handle search
  const handleSearch = (e) => {
    const searchValue = e.target.value.toLowerCase();
    setFilters((prev) => ({ ...prev, search: searchValue }));
    setCurrentPage(1);

    // Filter users based on search value across all columns
    const filtered = users.filter((user) => {
      return Object.values(user).some((value) => {
        // Convert value to string and check if it includes search value
        const stringValue = String(value).toLowerCase();
        return stringValue.includes(searchValue);
      });
    });

    setFilteredUsers(filtered);
  };

  // Handle opening modal
  const openModal = (user, mode) => {
    if (mode === "view") {
      navigate(`/userdetail?username=${user.username}`);
      return;
    }
    if (mode === "add") {
      setSelectedUser({
        id: users.length > 0 ? Math.max(...users.map((u) => u.id)) + 1 : 1000,
        username: "",
        is_admin: false,
        display_name: "",
        status: "active",
        platform: filters.platform,
      });
    } else {
      // For DataQuest platform, ensure we have the required fields
      if (filters.platform === "dataquest" && mode === "edit") {
        setSelectedUser({
          ...user,
          // Ensure these fields exist for DataQuest update
          username: user.username || "",
          display_name: user.display_name || "",
          role: user.role || "user",
          status: user.status || "active",
        });
      } else {
        setSelectedUser(user);
      }
    }
    setModalMode(mode);
    setIsModalOpen(true);
  };

  // Focus username input when modal opens in add mode
  useEffect(() => {
    if (isModalOpen && modalMode === "add" && usernameInputRef.current) {
      usernameInputRef.current.focus();
    }
  }, [isModalOpen, modalMode]);

  // Handle closing modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
    setModalError(null);
  };

  // Handle deleting user
  const handleDeleteUser = async () => {
    if (selectedUser) {
      try {
        await apiService.delete(ENDPOINTS.USERS.DELETE(selectedUser.id));
        // Update state directly instead of fetching
        setUsers((prevUsers) =>
          prevUsers.filter((user) => user.id !== selectedUser.id)
        );
        closeModal();
      } catch (error) {
        console.error("Error deleting user:", error);
        setError("Unable to delete user. Please try again later.");
      }
    }
  };

  // Handle user information changes
  const handleUserChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Handle checkbox fields (for arrays)
    if (type === "checkbox" && !name.startsWith("is_")) {
      setSelectedUser((prev) => {
        const currentValue = prev[name] || [];
        if (checked) {
          // Add value to array if checked
          return { ...prev, [name]: [...currentValue, value] };
        } else {
          // Remove value from array if unchecked
          return {
            ...prev,
            [name]: currentValue.filter((item) => item !== value),
          };
        }
      });
    }
    // Handle radio buttons for is_admin
    else if (type === "radio" && name === "is_admin") {
      const boolValue = value === "true";
      setSelectedUser((prev) => ({ ...prev, [name]: boolValue }));
    }
    // Handle toggle switch fields (for booleans)
    else if (type === "checkbox" && name.startsWith("is_")) {
      setSelectedUser((prev) => ({ ...prev, [name]: checked }));
    }
    // Handle multi-select fields
    else if (type === "select-multiple") {
      const selectedOptions = Array.from(e.target.selectedOptions).map(
        (option) => option.value
      );
      setSelectedUser((prev) => ({ ...prev, [name]: selectedOptions }));
    }
    // Handle username (convert to lowercase)
    else if (name === "username") {
      setSelectedUser((prev) => ({ ...prev, [name]: value.toLowerCase() }));
    }
    // Handle regular input fields
    else {
      setSelectedUser((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Check if username already exists
  const checkUsernameExists = (username) => {
    const lowercaseUsername = username.toLowerCase();
    return users.some(
      (user) =>
        user.username.toLowerCase() === lowercaseUsername &&
        user.platform === filters.platform &&
        (modalMode === "add" || user.id !== selectedUser.id)
    );
  };

  // Validate username format
  const isValidUsername = (username) => {
    // Email pattern
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    // Username pattern: must start with a letter, followed by letters or numbers only
    const usernamePattern = /^[a-zA-Z][a-zA-Z0-9]*$/;

    return emailPattern.test(username) || usernamePattern.test(username);
  };

  // Handle adding/updating user
  const handleSaveUser = async () => {
    try {
      // Validate username
      if (selectedUser.username) {
        // Convert username to lowercase
        const lowercaseUsername = selectedUser.username.toLowerCase();

        // Check if username format is valid
        if (!isValidUsername(lowercaseUsername)) {
          setModalError(
            "Username must be a valid email or start with a letter and contain only letters and numbers."
          );
          return;
        }

        // Update the username to lowercase
        selectedUser.username = lowercaseUsername;

        // Check if username already exists
        if (checkUsernameExists(lowercaseUsername)) {
          setModalError(
            `Username "${lowercaseUsername}" already exists for this platform.`
          );
          return;
        }
      } else {
        setModalError("Username is required.");
        return;
      }

      if (modalMode === "add") {
        // Filter out empty fields and fields not relevant to the platform
        const filteredUser = {};
        const formFields = document.querySelectorAll(
          ".user-form input, .user-form select"
        );

        // Add only fields that are in the form
        formFields.forEach((field) => {
          if (
            field.name &&
            (field.value || field.type === "checkbox") &&
            field.name !== "platform" &&
            field.name !== "id"
          ) {
            // Handle different input types
            if (field.type === "checkbox") {
              // For checkboxes, we need to collect all checked values for the same name
              if (field.checked) {
                if (!filteredUser[field.name]) {
                  filteredUser[field.name] = [];
                }
                filteredUser[field.name].push(field.value);
              }
            } else if (field.type === "select-multiple") {
              // For multi-select, collect all selected options
              filteredUser[field.name] = Array.from(field.selectedOptions).map(
                (option) => option.value
              );
            } else if (field.value) {
              // For regular inputs, just use the value
              filteredUser[field.name] = field.value;
            }
          }
        });

        // Handle boolean fields specially
        if (selectedUser.is_admin !== undefined) {
          filteredUser.is_admin = selectedUser.is_admin;
        }

        // Get platform-specific create endpoint from config
        const createEndpoint =
          ENDPOINTS.USERS[`${filters.platform.toUpperCase()}_CREATE`];
        if (!createEndpoint) {
          throw new Error(
            `No create endpoint found for platform: ${filters.platform}`
          );
        }

        const response = await apiService.post(createEndpoint, filteredUser);

        // Check if response has successful message
        if (
          response &&
          response.message &&
          response.message.includes("success")
        ) {
          // Add new user data from response to state
          if (response.data) {
            setUsers((prevUsers) => [...prevUsers, response.data]);
          }

          // Show success message
          setModalError(null);
          // Replace alert with toast message
          setSuccessMessage(response.message || "User created successfully");

          // Clear success message after 5 seconds
          setTimeout(() => {
            setSuccessMessage(null);
          }, 5000);

          closeModal();
        } else if (response && response.error) {
          // Show error message from API
          setModalError(
            response.detail || response.error || "Error creating user"
          );
        } else {
          // Add new user to state (fallback for old API responses)
          setUsers((prevUsers) => [...prevUsers, response]);

          // Show success message
          setSuccessMessage("User created successfully");

          // Clear success message after 5 seconds
          setTimeout(() => {
            setSuccessMessage(null);
          }, 5000);

          closeModal();
        }
      } else if (modalMode === "edit") {
        let response;
        // For DataQuest platform, use the specific update endpoint
        if (filters.platform === "dataquest") {
          response = await apiService.post(
            ENDPOINTS.USERS.UPDATE,
            selectedUser
          );
        } else if (filters.platform === "wlp") {
          response = await apiService.post(
            ENDPOINTS.USERS.WLP_UPDATE,
            selectedUser
          );
        } else if (filters.platform === "ads") {
          response = await apiService.post(
            ENDPOINTS.USERS.ADS_UPDATE,
            selectedUser
          );
        } else if (filters.platform === "sbv") {
          response = await apiService.post(
            ENDPOINTS.USERS.SBV_UPDATE,
            selectedUser
          );
        } else if (filters.platform === "ecommerce") {
          response = await apiService.post(
            ENDPOINTS.USERS.ECOMMERCE_UPDATE,
            selectedUser
          );
        } else if (filters.platform === "mysql") {
          response = await apiService.post(
            ENDPOINTS.USERS.MYSQL_UPDATE,
            selectedUser
          );
        } else {
          // For other platforms, use the standard update endpoint
          response = await apiService.post(
            ENDPOINTS.USERS.UPDATE(selectedUser.id),
            selectedUser
          );
        }

        // Update user in state using response data
        if (response && response.data) {
          setUsers((prevUsers) =>
            prevUsers.map((user) =>
              user.id === response.data.id ? response.data : user
            )
          );
        }

        // Show success message
        setSuccessMessage("User updated successfully");

        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);

        closeModal();
      }
    } catch (error) {
      console.error("Error saving user:", error);
      // Try to extract error message from the error object
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        setModalError(
          errorData.detail ||
            errorData.error ||
            "Unable to save user information. Please try again later."
        );
      } else {
        setModalError(
          error.message ||
            "Unable to save user information. Please try again later."
        );
      }
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);

    // Format as YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  // Format cell value based on column type
  const formatCellValue = (key, value) => {
    if (value === null || value === undefined) return "";

    // Format status
    if (key === "status") {
      return (
        <span className={`status-badge ${value}`}>
          {value === "active"
            ? "Active"
            : value === "inactive"
            ? "Inactive"
            : value === "remove"
            ? "Removed"
            : value}
        </span>
      );
    }

    // Format dates
    if (
      key.includes("date") ||
      key.includes("created") ||
      key.includes("updated")
    ) {
      return formatDate(value);
    }

    // Format boolean values
    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    // Format arrays and objects - only split for WLP platform role field
    if (Array.isArray(value) || typeof value === "object") {
      // If it's a string with commas, split it into an array
      const arrayValue = Array.isArray(value)
        ? value
        : typeof value === "string" && value.includes(",")
        ? value.split(",").map((item) => item.trim())
        : [value];

      return (
        <div className="array-values-container">
          {arrayValue.map((item, index) => {
            // Determine color class based on value
            let colorClass = "array-value-default";

            // Role-based colors
            if (key === "role" || key.includes("role")) {
              if (item == "all") {
                colorClass = "array-value-all";
              } else if (item == "admin") {
                colorClass = "array-value-admin";
              } else if (item == "approver") {
                colorClass = "array-value-approver";
              } else if (item == "user") {
                colorClass = "array-value-user";
              }
            }

            // Status-based colors
            if (key === "status" || key.includes("status")) {
              if (item.includes("active")) {
                colorClass = "array-value-active";
              } else if (item.includes("inactive")) {
                colorClass = "array-value-inactive";
              } else if (item.includes("remove")) {
                colorClass = "array-value-removed";
              }
            }

            return (
              <span
                key={`${key}-${index}`}
                className={`array-value-item ${colorClass}`}
              >
                {item}
              </span>
            );
          })}
        </div>
      );
    }

    // Format objects that are not arrays
    if (typeof value === "object" && !Array.isArray(value)) {
      return JSON.stringify(value);
    }

    // Hide password fields
    if (key === "hashed_password") {
      return "********";
    }

    // Default: return as is
    return value;
  };

  // Generate random password
  const generateRandomPassword = () => {
    const charset =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+";
    let password = "";
    for (let i = 0; i < 12; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }
    return password;
  };

  // Apply random password to input
  const applyRandomPassword = () => {
    const password = generateRandomPassword();
    setSelectedUser((prev) => ({
      ...prev,
      hashed_password: password,
    }));
  };

  return (
    <div className="users-container">
      <div className="users-header">
        <div className="users-filters">
          <div className="filter-group">
            <label>Platform:</label>
            <select
              name="platform"
              value={filters.platform}
              onChange={handleFilterChange}
            >
              {platforms.map((platform) => (
                <option key={platform.id} value={platform.id}>
                  {platform.name}
                </option>
              ))}
            </select>
          </div>
          <div className="filter-group">
            <label>Status:</label>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
            >
              {statusOptions.map((status) => (
                <option key={status.id} value={status.id}>
                  {status.name}
                </option>
              ))}
            </select>
          </div>
          {/* <div className="filter-group">
            <label>Sort:</label>
            <select
              name="sort"
              value={filters.sort}
              onChange={handleFilterChange}
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
            </select>
          </div> */}
          <div className="filter-group">
            <label>Show:</label>
            <select value={itemsPerPage} onChange={handleItemsPerPageChange}>
              <option value="5">5 items</option>
              <option value="10">10 items</option>
              <option value="20">20 items</option>
              <option value="50">50 items</option>
            </select>
          </div>
          <div className="filter-group">
            <div className="column-selector-container" ref={columnSelectorRef}>
              <button
                className={`column-selector-btn ${showColumnSelector ? 'active' : ''}`}
                onClick={toggleColumnSelector}
                title="Select columns to display"
              >
                <FiColumns className="btn-icon-left" />
                {/* Columns */}
              </button>
              {showColumnSelector && (
                <div className="column-selector-dropdown">
                  <div className="column-selector-header">
                    <h4>Display Columns</h4>
                  </div>
                  <div className="column-selector-list">
                    {columns.map((column) => (
                      <div
                        key={column.key}
                        className="column-selector-item"
                        onClick={() => toggleColumnVisibility(column.key)}
                      >
                        <div className="column-checkbox">
                          {visibleColumns.includes(column.key) && (
                            <FiCheck className="column-check-icon" />
                          )}
                        </div>
                        <span>{column.label}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="users-actions">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search users..."
              value={filters.search}
              onChange={handleSearch}
            />
            <FiSearch className="search-icon" />
          </div>
          <div className="users-actions-buttons">
            <button
              className="btn-primary"
              onClick={() => openModal(null, "add")}
            >
              <FiUserPlus className="btn-icon-left" />
              Add User
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="success-message">
          <FiUserCheck />
          <span>{successMessage}</span>
        </div>
      )}

      {isLoading ? (
        <div className="loading-spinner"></div>
      ) : (
        <div className="users-table-container">
          <table className="users-table">
            <thead>
              <tr>
                {columns
                  .filter((column) => visibleColumns.includes(column.key))
                  .map((column) => (
                    <th
                      key={column.key}
                      onClick={() => requestSort(column.key)}
                      className={sortConfig.key === column.key ? "sorted" : ""}
                    >
                      {column.label} {getSortIcon(column.key)}
                    </th>
                  ))}
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentItems.map((user) => (
                <tr key={user.id}>
                  {columns
                    .filter((column) => visibleColumns.includes(column.key))
                    .map((column) => (
                      <td key={`${user.id}-${column.key}`}>
                        {formatCellValue(column.key, user[column.key])}
                      </td>
                    ))}
                  <td>
                    <div className="action-buttons">
                      <button
                        className="btn-icon view"
                        onClick={() => openModal(user, "view")}
                        title="View"
                      >
                        <FiEye />
                      </button>
                      <button
                        className="btn-icon edit"
                        onClick={() => openModal(user, "edit")}
                        title="Edit"
                      >
                        <FiEdit />
                      </button>
                      <button
                        className="btn-icon delete"
                        onClick={() => openModal(user, "delete")}
                        disabled={true} // tạm thời ko cho xoá user
                        title="Delete"
                      >
                        <FiTrash2 />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="pagination-container">
        <div className="pagination-info">
          Showing {indexOfFirstItem + 1} -{" "}
          {Math.min(indexOfLastItem, filteredUsers.length)} of{" "}
          {filteredUsers.length} users
        </div>
        <div className="pagination">
          <button
            className="pagination-btn"
            onClick={firstPage}
            disabled={currentPage === 1}
          >
            «
          </button>
          <button
            className="pagination-btn"
            onClick={prevPage}
            disabled={currentPage === 1}
          >
            ‹
          </button>
          {[...Array(Math.min(5, totalPages))].map((_, index) => {
            let pageNumber;
            if (totalPages <= 5) {
              pageNumber = index + 1;
            } else if (currentPage <= 3) {
              pageNumber = index + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNumber = totalPages - 4 + index;
            } else {
              pageNumber = currentPage - 2 + index;
            }

            return (
              <button
                key={pageNumber}
                className={`pagination-btn ${
                  currentPage === pageNumber ? "active" : ""
                }`}
                onClick={() => paginate(pageNumber)}
              >
                {pageNumber}
              </button>
            );
          })}
          <button
            className="pagination-btn"
            onClick={nextPage}
            disabled={currentPage === totalPages}
          >
            ›
          </button>
          <button
            className="pagination-btn"
            onClick={lastPage}
            disabled={currentPage === totalPages}
          >
            »
          </button>
        </div>
      </div>

      {isModalOpen && selectedUser && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-container" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                {modalMode === "add" && "Add New User"}
                {modalMode === "edit" &&
                  `Edit User: ${
                    selectedUser.display_name || selectedUser.username
                  }`}
                {modalMode === "delete" &&
                  `Delete User: ${
                    selectedUser.display_name || selectedUser.username
                  }`}
              </h3>
              <button className="modal-close" onClick={closeModal}>
                ×
              </button>
            </div>
            <div className="modal-body">
              {modalError && (
                <div className="modal-error-message">
                  <FiAlertCircle className="error-icon" />
                  <span>{modalError}</span>
                </div>
              )}
              {modalMode === "delete" ? (
                <div className="delete-confirmation">
                  <p>Are you sure you want to delete this user?</p>
                  <div className="modal-actions">
                    <button className="btn-secondary" onClick={closeModal}>
                      Cancel
                    </button>
                    <button className="btn-danger" onClick={handleDeleteUser}>
                      Delete
                    </button>
                  </div>
                </div>
              ) : (
                <div className="user-form">
                  {columns.map((column) => {
                    // Skip certain fields that shouldn't be editable
                    if (
                      column.key === "id" ||
                      column.key === "created_at" ||
                      column.key === "updated_at"
                    ) {
                      return null;
                    }

                    // Determine input type based on column key
                    let inputType = "text";
                    if (column.key === "email") inputType = "email";
                    if (column.key === "phone") inputType = "tel";
                    if (column.key === "password") inputType = "password";

                    // Special handling for status field
                    if (column.key === "status") {
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <select
                            name={column.key}
                            value={selectedUser[column.key] || ""}
                            onChange={handleUserChange}
                          >
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="remove">Removed</option>
                          </select>
                        </div>
                      );
                    }

                    // Special handling for role field
                    if (column.key === "role") {
                      // For DataQuest platform, use a select box with default 'user'
                      if (filters.platform === "dataquest") {
                        return (
                          <div className="users-form-group" key={column.key}>
                            <label>{column.label}:</label>
                            <select
                              name={column.key}
                              value={selectedUser[column.key] || "user"}
                              onChange={handleUserChange}
                            >
                              <option value="admin">Admin</option>
                              <option value="approver">Approver</option>
                              <option value="user">User</option>
                            </select>
                          </div>
                        );
                      }

                      // For SBV and Ecommerce platforms, use a select box with default 'all'
                      if (
                        filters.platform === "sbv" ||
                        filters.platform === "ecommerce"
                      ) {
                        return (
                          <div className="users-form-group" key={column.key}>
                            <label>{column.label}:</label>
                            <select
                              name={column.key}
                              value={selectedUser[column.key] || "all"}
                              onChange={handleUserChange}
                            >
                              <option value="all">All</option>
                              <option value="null">Null</option>
                            </select>
                          </div>
                        );
                      }

                      // For WLP platform, use checkboxes with specific options
                      if (filters.platform === "wlp") {
                        return (
                          <div className="users-form-group" key={column.key}>
                            <label>{column.label}:</label>
                            <div className="checkbox-group">
                              {wlpRoleOptions.map((option) => (
                                <label
                                  key={option.value}
                                  className="checkbox-label"
                                >
                                  <input
                                    type="checkbox"
                                    name={column.key}
                                    value={option.value}
                                    checked={(
                                      selectedUser[column.key] || []
                                    ).includes(option.value)}
                                    onChange={handleUserChange}
                                  />
                                  <span className="checkbox-text">
                                    {option.label}
                                  </span>
                                </label>
                              ))}
                            </div>
                          </div>
                        );
                      }

                      // For other platforms, use the existing checkbox group
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <div className="checkbox-group">
                            {adsRoleOptions.map((option) => (
                              <label
                                key={option.value}
                                className="checkbox-label"
                              >
                                <input
                                  type="checkbox"
                                  name={column.key}
                                  value={option.value}
                                  checked={(
                                    selectedUser[column.key] || []
                                  ).includes(option.value)}
                                  onChange={handleUserChange}
                                />
                                <span className="checkbox-text">
                                  {option.label}
                                </span>
                              </label>
                            ))}
                          </div>
                        </div>
                      );
                    }

                    // Special handling for role_lvl1 field
                    if (column.key === "role_lvl1") {
                      // For WLP platform, show conditional options based on selected roles
                      if (filters.platform === "wlp") {
                        const selectedRoles = selectedUser.role || [];
                        const hasFunnel = selectedRoles.includes("funnel");
                        const hasEcommerce =
                          selectedRoles.includes("config_tool");

                        // If no relevant roles are selected, don't show role_lvl1
                        if (!hasFunnel && !hasEcommerce) {
                          return null;
                        }

                        return (
                          <div className="users-form-group" key={column.key}>
                            <label>{column.label}:</label>
                            <div className="checkbox-group">
                              {hasFunnel && (
                                <>
                                  <label className="checkbox-label">
                                    <input
                                      type="checkbox"
                                      name={column.key}
                                      value="funnel_event"
                                      checked={(
                                        selectedUser[column.key] || []
                                      ).includes("funnel_event")}
                                      onChange={handleUserChange}
                                    />
                                    <span className="checkbox-text">
                                      Funnel Event
                                    </span>
                                  </label>
                                  <label className="checkbox-label">
                                    <input
                                      type="checkbox"
                                      name={column.key}
                                      value="funnel_report"
                                      checked={(
                                        selectedUser[column.key] || []
                                      ).includes("funnel_report")}
                                      onChange={handleUserChange}
                                    />
                                    <span className="checkbox-text">
                                      Funnel Report
                                    </span>
                                  </label>
                                </>
                              )}
                              {hasEcommerce && (
                                <>
                                  <label className="checkbox-label">
                                    <input
                                      type="checkbox"
                                      name={column.key}
                                      value="config_tool_view"
                                      checked={(
                                        selectedUser[column.key] || []
                                      ).includes("config_tool_view")}
                                      onChange={handleUserChange}
                                    />
                                    <span className="checkbox-text">
                                      Config Tool View
                                    </span>
                                  </label>
                                  <label className="checkbox-label">
                                    <input
                                      type="checkbox"
                                      name={column.key}
                                      value="config_tool_edit"
                                      checked={(
                                        selectedUser[column.key] || []
                                      ).includes("config_tool_edit")}
                                      onChange={handleUserChange}
                                    />
                                    <span className="checkbox-text">
                                      Config Tool Edit
                                    </span>
                                  </label>
                                  <label className="checkbox-label">
                                    <input
                                      type="checkbox"
                                      name={column.key}
                                      value="config_tool_approve"
                                      checked={(
                                        selectedUser[column.key] || []
                                      ).includes("config_tool_approve")}
                                      onChange={handleUserChange}
                                    />
                                    <span className="checkbox-text">
                                      Config Tool Approve
                                    </span>
                                  </label>
                                  <label className="checkbox-label">
                                    <input
                                      type="checkbox"
                                      name={column.key}
                                      value="config_tool_admin"
                                      checked={(
                                        selectedUser[column.key] || []
                                      ).includes("config_tool_admin")}
                                      onChange={handleUserChange}
                                    />
                                    <span className="checkbox-text">
                                      Config Tool Admin
                                    </span>
                                  </label>
                                </>
                              )}
                            </div>
                          </div>
                        );
                      }

                      // For DataQuest platform, use the existing options
                      if (filters.platform === "dataquest") {
                        return (
                          <div className="users-form-group" key={column.key}>
                            <label>{column.label}:</label>
                            <div className="checkbox-group">
                              {roleLvl1Options.map((option) => (
                                <label
                                  key={option.value}
                                  className="checkbox-label"
                                >
                                  <input
                                    type="checkbox"
                                    name={column.key}
                                    value={option.value}
                                    checked={(
                                      selectedUser[column.key] || []
                                    ).includes(option.value)}
                                    onChange={handleUserChange}
                                  />
                                  <span className="checkbox-text">
                                    {option.label}
                                  </span>
                                </label>
                              ))}
                            </div>
                          </div>
                        );
                      }

                      // For other platforms, use the existing checkbox group
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <div className="checkbox-group">
                            {roleLvl1Options.map((option) => (
                              <label
                                key={option.value}
                                className="checkbox-label"
                              >
                                <input
                                  type="checkbox"
                                  name={column.key}
                                  value={option.value}
                                  checked={(
                                    selectedUser[column.key] || []
                                  ).includes(option.value)}
                                  onChange={handleUserChange}
                                />
                                <span className="checkbox-text">
                                  {option.label}
                                </span>
                              </label>
                            ))}
                          </div>
                        </div>
                      );
                    }

                    // Special handling for role_lvl2 field
                    if (column.key === "role_lvl2") {
                      // For WLP platform, don't show role_lvl2 if no relevant roles are selected
                      if (filters.platform === "wlp") {
                        const selectedRoles = selectedUser.role || [];
                        const hasFunnel = selectedRoles.includes("funnel");
                        const hasEcommerce =
                          selectedRoles.includes("config_tool");

                        if (!hasFunnel && !hasEcommerce) {
                          return null;
                        }
                      }

                      // For DataQuest platform, use the existing options
                      if (filters.platform === "dataquest") {
                        return (
                          <div className="users-form-group" key={column.key}>
                            <label>{column.label}:</label>
                            <div className="checkbox-group">
                              {roleLvl2Options.map((option) => (
                                <label
                                  key={option.value}
                                  className="checkbox-label"
                                >
                                  <input
                                    type="checkbox"
                                    name={column.key}
                                    value={option.value}
                                    checked={(
                                      selectedUser[column.key] || []
                                    ).includes(option.value)}
                                    onChange={handleUserChange}
                                  />
                                  <span className="checkbox-text">
                                    {option.label}
                                  </span>
                                </label>
                              ))}
                            </div>
                          </div>
                        );
                      }

                      // For other platforms, use the existing checkbox group
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <div className="checkbox-group">
                            {roleLvl2Options.map((option) => (
                              <label
                                key={option.value}
                                className="checkbox-label"
                              >
                                <input
                                  type="checkbox"
                                  name={column.key}
                                  value={option.value}
                                  checked={(
                                    selectedUser[column.key] || []
                                  ).includes(option.value)}
                                  onChange={handleUserChange}
                                />
                                <span className="checkbox-text">
                                  {option.label}
                                </span>
                              </label>
                            ))}
                          </div>
                        </div>
                      );
                    }

                    // Special handling for role_lvl3 field
                    if (column.key === "role_lvl3") {
                      // For WLP platform, don't show role_lvl3 if no relevant roles are selected
                      if (filters.platform === "wlp") {
                        const selectedRoles = selectedUser.role || [];
                        const hasFunnel = selectedRoles.includes("funnel");
                        const hasEcommerce =
                          selectedRoles.includes("config_tool");

                        if (!hasFunnel && !hasEcommerce) {
                          return null;
                        }
                      }

                      // For other platforms, use the existing checkbox group
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <div className="checkbox-group">
                            {roleLvl3Options.map((option) => (
                              <label
                                key={option.value}
                                className="checkbox-label"
                              >
                                <input
                                  type="checkbox"
                                  name={column.key}
                                  value={option.value}
                                  checked={(
                                    selectedUser[column.key] || []
                                  ).includes(option.value)}
                                  onChange={handleUserChange}
                                />
                                <span className="checkbox-text">
                                  {option.label}
                                </span>
                              </label>
                            ))}
                          </div>
                        </div>
                      );
                    }

                    // Special handling for permission field
                    if (column.key === "permission") {
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <div className="checkbox-group">
                            <label className="checkbox-label">
                              <input
                                type="checkbox"
                                name={column.key}
                                value="read"
                                checked={(
                                  selectedUser[column.key] || []
                                ).includes("read")}
                                onChange={handleUserChange}
                              />
                              <span className="checkbox-text">Read</span>
                            </label>
                            <label className="checkbox-label">
                              <input
                                type="checkbox"
                                name={column.key}
                                value="write"
                                checked={(
                                  selectedUser[column.key] || []
                                ).includes("write")}
                                onChange={handleUserChange}
                              />
                              <span className="checkbox-text">Write</span>
                            </label>
                            <label className="checkbox-label">
                              <input
                                type="checkbox"
                                name={column.key}
                                value="admin"
                                checked={(
                                  selectedUser[column.key] || []
                                ).includes("admin")}
                                onChange={handleUserChange}
                              />
                              <span className="checkbox-text">Admin</span>
                            </label>
                          </div>
                        </div>
                      );
                    }

                    // Special handling for boolean fields (is_admin, etc.)
                    if (
                      typeof selectedUser[column.key] === "boolean" ||
                      column.key.startsWith("is_")
                    ) {
                      // For is_admin, use radio buttons instead of toggle switch
                      if (column.key === "is_admin") {
                        return (
                          <div className="users-form-group" key={column.key}>
                            <label>{column.label}:</label>
                            <div className="radio-group">
                              <label className="radio-label">
                                <input
                                  type="radio"
                                  name={column.key}
                                  value="true"
                                  checked={selectedUser[column.key] === true}
                                  onChange={handleUserChange}
                                />
                                <span className="radio-text">Yes</span>
                              </label>
                              <label className="radio-label">
                                <input
                                  type="radio"
                                  name={column.key}
                                  value="false"
                                  checked={
                                    selectedUser[column.key] === false ||
                                    selectedUser[column.key] === undefined
                                  }
                                  onChange={handleUserChange}
                                />
                                <span className="radio-text">No</span>
                              </label>
                            </div>
                          </div>
                        );
                      }

                      // For other boolean fields, use toggle switch
                      return (
                        <div
                          className="users-form-group toggle-group"
                          key={column.key}
                        >
                          <label>{column.label}:</label>
                          <label className="toggle-switch">
                            <input
                              type="checkbox"
                              name={column.key}
                              checked={selectedUser[column.key] || false}
                              onChange={handleUserChange}
                            />
                            <span className="toggle-slider"></span>
                          </label>
                        </div>
                      );
                    }

                    // Default input field
                    return (
                      <div className="users-form-group" key={column.key}>
                        <label>
                          {column.key === "hashed_password"
                            ? "Password"
                            : column.label}
                          :
                        </label>
                        {column.key === "hashed_password" ? (
                          <div className="password-input-container">
                            <input
                              type="text"
                              name={column.key}
                              value={selectedUser[column.key] || ""}
                              onChange={handleUserChange}
                              required={["username", "email"].includes(
                                column.key
                              )}
                            />
                            <button
                              type="button"
                              className="password-suggestion-btn"
                              onClick={applyRandomPassword}
                              title="Generate random 12-character password"
                              disabled={
                                selectedUser[column.key] &&
                                selectedUser[column.key] !== ""
                              }
                            >
                              Suggest
                            </button>
                            {/* <button
                              type="button"
                              className="password-toggle-btn"
                              onClick={() => setShowPassword(!showPassword)}
                              title={showPassword ? "Hide password" : "Show password"}
                            >
                              {showPassword ? "Hide" : "Show"}
                            </button> */}
                          </div>
                        ) : (
                          <input
                            type={inputType}
                            name={column.key}
                            value={selectedUser[column.key] || ""}
                            onChange={handleUserChange}
                            required={["username", "email"].includes(
                              column.key
                            )}
                            disabled={
                              column.key === "username" && modalMode === "edit"
                            }
                            className={
                              column.key === "username" && modalMode === "edit"
                                ? "disabled-field"
                                : ""
                            }
                            ref={
                              column.key === "username"
                                ? usernameInputRef
                                : null
                            }
                          />
                        )}
                      </div>
                    );
                  })}
                  <div className="modal-actions">
                    <button className="btn-secondary" onClick={closeModal}>
                      Cancel
                    </button>
                    <button className="btn-primary" onClick={handleSaveUser}>
                      {modalMode === "add" ? "Add" : "Save Changes"}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Users;
