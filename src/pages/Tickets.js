import React, { useState, useEffect } from 'react';
import { <PERSON>Eye, FiEdit, FiTrash2, FiChevronUp, FiChevronDown } from 'react-icons/fi';
import '../styles/Tickets.css';

const Tickets = () => {
  // Dữ liệu mẫu
  const generateTickets = () => {
    return Array(50).fill().map((_, index) => ({
      id: 1234 + index,
      title: `Ticket mẫu #${index + 1}`,
      creator: `Người dùng ${index % 10 + 1}`,
      assignee: `Nhân viên ${index % 3 + 1}`,
      status: index % 4 === 0 ? 'new' : 
              index % 4 === 1 ? 'pending' : 
              index % 4 === 2 ? 'completed' : 'closed',
      priority: index % 3 === 0 ? 'high' : 
                index % 3 === 1 ? 'medium' : 'low',
      createdAt: new Date(2023, 7, 15 - (index % 30)),
    }));
  };

  const [tickets, setTickets] = useState(generateTickets());
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(() => {
    const savedItemsPerPage = localStorage.getItem('itemsPerPage');
    return savedItemsPerPage ? parseInt(savedItemsPerPage, 10) : 10;
  });
  
  const [sortConfig, setSortConfig] = useState({ key: 'id', direction: 'ascending' });
  const [filters, setFilters] = useState(() => {
    // Đọc trạng thái bộ lọc từ localStorage nếu có
    const savedFilterStatus = localStorage.getItem('ticketFilterStatus');
    return {
      status: savedFilterStatus || 'all',
      category: 'all',
      sort: 'newest'
    };
  });
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('view'); // 'view', 'edit', 'delete'

  // Thêm useEffect để xóa bộ lọc khỏi localStorage sau khi đã sử dụng
  useEffect(() => {
    // Xóa bộ lọc khỏi localStorage sau khi đã áp dụng
    localStorage.removeItem('ticketFilterStatus');
  }, []);

  // Xử lý lọc và sắp xếp
  useEffect(() => {
    let result = [...tickets];
    
    // Áp dụng bộ lọc
    if (filters.status !== 'all') {
      result = result.filter(ticket => ticket.status === filters.status);
    }
    
    // Áp dụng sắp xếp
    if (sortConfig.key) {
      result.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }
    
    setFilteredTickets(result);
  }, [tickets, filters, sortConfig]);

  // Xử lý sắp xếp khi click vào header của bảng
  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Hiển thị icon sắp xếp
  const getSortIcon = (columnName) => {
    if (sortConfig.key !== columnName) {
      return <span className="sort-icon sort-inactive">⇅</span>;
    }
    
    return sortConfig.direction === 'ascending' 
      ? <FiChevronUp className="sort-icon" /> 
      : <FiChevronDown className="sort-icon" />;
  };

  // Xử lý phân trang
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredTickets.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredTickets.length / itemsPerPage);

  // Xử lý chuyển trang và lưu vào localStorage
  const paginate = (pageNumber) => {
    setCurrentPage(pageNumber);
  };
  
  const nextPage = () => {
    const newPage = Math.min(currentPage + 1, totalPages);
    setCurrentPage(newPage);
  };
  
  const prevPage = () => {
    const newPage = Math.max(currentPage - 1, 1);
    setCurrentPage(newPage);
  };
  
  const firstPage = () => {
    setCurrentPage(1);
  };
  
  const lastPage = () => {
    setCurrentPage(totalPages);
  };
  
  // Xử lý thay đổi số lượng items mỗi trang
  const handleItemsPerPageChange = (e) => {
    const value = parseInt(e.target.value, 10);
    setItemsPerPage(value);
    localStorage.setItem('itemsPerPage', value.toString());
    // Reset về trang 1 khi thay đổi số lượng items
    setCurrentPage(1);
  };

  // Xử lý mở modal
  const openModal = (ticket, mode) => {
    setSelectedTicket(ticket);
    setModalMode(mode);
    setIsModalOpen(true);
  };

  // Xử lý đóng modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedTicket(null);
  };

  // Xử lý xóa ticket
  const handleDeleteTicket = () => {
    if (selectedTicket) {
      setTickets(tickets.filter(ticket => ticket.id !== selectedTicket.id));
      closeModal();
    }
  };

  // Xử lý cập nhật ticket
  const handleUpdateTicket = (updatedTicket) => {
    setTickets(tickets.map(ticket => 
      ticket.id === updatedTicket.id ? updatedTicket : ticket
    ));
    closeModal();
  };

  // Format date
  const formatDate = (date) => {
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="tickets-container">
      <div className="tickets-filters">
        <div className="filter-group">
          <label>Trạng thái:</label>
          <select 
            value={filters.status}
            onChange={(e) => setFilters({...filters, status: e.target.value})}
          >
            <option value="all">Tất cả</option>
            <option value="new">Mới</option>
            <option value="pending">Đang xử lý</option>
            <option value="completed">Hoàn thành</option>
            <option value="closed">Đã đóng</option>
          </select>
        </div>
        <div className="filter-group">
          <label>Phân loại:</label>
          <select 
            value={filters.category}
            onChange={(e) => setFilters({...filters, category: e.target.value})}
          >
            <option value="all">Tất cả</option>
            <option value="bug">Lỗi</option>
            <option value="feature">Tính năng mới</option>
            <option value="support">Hỗ trợ</option>
          </select>
        </div>
        <div className="filter-group">
          <label>Sắp xếp:</label>
          <select 
            value={filters.sort}
            onChange={(e) => setFilters({...filters, sort: e.target.value})}
          >
            <option value="newest">Mới nhất</option>
            <option value="oldest">Cũ nhất</option>
            <option value="priority">Ưu tiên</option>
          </select>
        </div>
        <div className="filter-group">
          <label>Hiển thị:</label>
          <select 
            value={itemsPerPage}
            onChange={handleItemsPerPageChange}
          >
            <option value="5">5 mục</option>
            <option value="10">10 mục</option>
            <option value="20">20 mục</option>
            <option value="50">50 mục</option>
          </select>
        </div>
      </div>
      
      <div className="tickets-list">
        <table className="tickets-table">
          <thead>
            <tr>
              <th onClick={() => requestSort('id')}>
                ID {getSortIcon('id')}
              </th>
              <th onClick={() => requestSort('title')}>
                Tiêu đề {getSortIcon('title')}
              </th>
              <th onClick={() => requestSort('creator')}>
                Người tạo {getSortIcon('creator')}
              </th>
              <th onClick={() => requestSort('assignee')}>
                Người xử lý {getSortIcon('assignee')}
              </th>
              <th onClick={() => requestSort('status')}>
                Trạng thái {getSortIcon('status')}
              </th>
              <th onClick={() => requestSort('priority')}>
                Ưu tiên {getSortIcon('priority')}
              </th>
              <th onClick={() => requestSort('createdAt')}>
                Ngày tạo {getSortIcon('createdAt')}
              </th>
              <th>Hành động</th>
            </tr>
          </thead>
          <tbody>
            {currentItems.map((ticket) => (
              <tr key={ticket.id}>
                <td>#{ticket.id}</td>
                <td>{ticket.title}</td>
                <td>{ticket.creator}</td>
                <td>{ticket.assignee}</td>
                <td>
                  <span className={`status-badge ${ticket.status}`}>
                    {ticket.status === 'new' ? 'Mới' : 
                     ticket.status === 'pending' ? 'Đang xử lý' : 
                     ticket.status === 'completed' ? 'Hoàn thành' : 'Đã đóng'}
                  </span>
                </td>
                <td>
                  <span className={`priority-badge ${ticket.priority}`}>
                    {ticket.priority === 'high' ? 'Cao' : 
                     ticket.priority === 'medium' ? 'Trung bình' : 'Thấp'}
                  </span>
                </td>
                <td>{formatDate(ticket.createdAt)}</td>
                <td>
                  <div className="action-buttons">
                    <button 
                      className="btn-icon view"
                      onClick={() => openModal(ticket, 'view')}
                    >
                      <FiEye />
                    </button>
                    <button 
                      className="btn-icon edit"
                      onClick={() => openModal(ticket, 'edit')}
                    >
                      <FiEdit />
                    </button>
                    <button 
                      className="btn-icon delete"
                      onClick={() => openModal(ticket, 'delete')}
                    >
                      <FiTrash2 />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="pagination-container">
        <div className="pagination-info">
          Hiển thị {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredTickets.length)} của {filteredTickets.length} kết quả
        </div>
        <div className="pagination">
          <button 
            className="pagination-btn" 
            onClick={firstPage}
            disabled={currentPage === 1}
          >
            «
          </button>
          <button 
            className="pagination-btn" 
            onClick={prevPage}
            disabled={currentPage === 1}
          >
            ‹
          </button>
          
          {/* Hiển thị các nút trang */}
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Tính toán số trang để hiển thị xung quanh trang hiện tại
            let pageNum;
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }
            
            return (
              <button
                key={pageNum}
                className={`pagination-btn ${currentPage === pageNum ? 'active' : ''}`}
                onClick={() => paginate(pageNum)}
              >
                {pageNum}
              </button>
            );
          })}
          
          <button 
            className="pagination-btn" 
            onClick={nextPage}
            disabled={currentPage === totalPages}
          >
            ›
          </button>
          <button 
            className="pagination-btn" 
            onClick={lastPage}
            disabled={currentPage === totalPages}
          >
            »
          </button>
        </div>
      </div>
      
      {isModalOpen && selectedTicket && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-container" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                {modalMode === 'view' && `Chi tiết Ticket #${selectedTicket.id}`}
                {modalMode === 'edit' && `Chỉnh sửa Ticket #${selectedTicket.id}`}
                {modalMode === 'delete' && `Xóa Ticket #${selectedTicket.id}`}
              </h3>
              <button className="modal-close" onClick={closeModal}>×</button>
            </div>
            <div className="modal-body">
              {modalMode === 'delete' ? (
                <div className="delete-confirmation">
                  <p>Bạn có chắc chắn muốn xóa ticket này?</p>
                  <div className="modal-actions">
                    <button 
                      className="btn-secondary" 
                      onClick={closeModal}
                    >
                      Hủy
                    </button>
                    <button 
                      className="btn-danger" 
                      onClick={handleDeleteTicket}
                    >
                      Xóa
                    </button>
                  </div>
                </div>
              ) : (
                <div className="ticket-details">
                  <div className="ticket-detail-row">
                    <div className="ticket-detail-label">ID:</div>
                    <div className="ticket-detail-value">#{selectedTicket.id}</div>
                  </div>
                  
                  <div className="ticket-detail-row">
                    <div className="ticket-detail-label">Tiêu đề:</div>
                    {modalMode === 'edit' ? (
                      <input 
                        type="text" 
                        className="detail-input" 
                        value={selectedTicket.title}
                        onChange={(e) => setSelectedTicket({
                          ...selectedTicket,
                          title: e.target.value
                        })}
                      />
                    ) : (
                      <div className="ticket-detail-value">{selectedTicket.title}</div>
                    )}
                  </div>
                  
                  <div className="ticket-detail-row">
                    <div className="ticket-detail-label">Người tạo:</div>
                    <div className="ticket-detail-value">{selectedTicket.creator}</div>
                  </div>
                  
                  <div className="ticket-detail-row">
                    <div className="ticket-detail-label">Người xử lý:</div>
                    {modalMode === 'edit' ? (
                      <select 
                        className="detail-select"
                        value={selectedTicket.assignee}
                        onChange={(e) => setSelectedTicket({
                          ...selectedTicket,
                          assignee: e.target.value
                        })}
                      >
                        <option value="Nhân viên 1">Nhân viên 1</option>
                        <option value="Nhân viên 2">Nhân viên 2</option>
                        <option value="Nhân viên 3">Nhân viên 3</option>
                      </select>
                    ) : (
                      <div className="ticket-detail-value">{selectedTicket.assignee}</div>
                    )}
                  </div>
                  
                  <div className="ticket-detail-row">
                    <div className="ticket-detail-label">Trạng thái:</div>
                    {modalMode === 'edit' ? (
                      <select 
                        className="detail-select"
                        value={selectedTicket.status}
                        onChange={(e) => setSelectedTicket({
                          ...selectedTicket,
                          status: e.target.value
                        })}
                      >
                        <option value="new">Mới</option>
                        <option value="pending">Đang xử lý</option>
                        <option value="completed">Hoàn thành</option>
                        <option value="closed">Đã đóng</option>
                      </select>
                    ) : (
                      <span className={`status-badge ${selectedTicket.status}`}>
                        {selectedTicket.status === 'new' ? 'Mới' : 
                         selectedTicket.status === 'pending' ? 'Đang xử lý' : 
                         selectedTicket.status === 'completed' ? 'Hoàn thành' : 'Đã đóng'}
                      </span>
                    )}
                  </div>
                  
                  <div className="ticket-detail-row">
                    <div className="ticket-detail-label">Ưu tiên:</div>
                    {modalMode === 'edit' ? (
                      <select 
                        className="detail-select"
                        value={selectedTicket.priority}
                        onChange={(e) => setSelectedTicket({
                          ...selectedTicket,
                          priority: e.target.value
                        })}
                      >
                        <option value="high">Cao</option>
                        <option value="medium">Trung bình</option>
                        <option value="low">Thấp</option>
                      </select>
                    ) : (
                      <span className={`priority-badge ${selectedTicket.priority}`}>
                        {selectedTicket.priority === 'high' ? 'Cao' : 
                         selectedTicket.priority === 'medium' ? 'Trung bình' : 'Thấp'}
                      </span>
                    )}
                  </div>
                  
                  <div className="ticket-detail-row">
                    <div className="ticket-detail-label">Ngày tạo:</div>
                    <div className="ticket-detail-value">{formatDate(selectedTicket.createdAt)}</div>
                  </div>
                  
                  {modalMode === 'edit' && (
                    <div className="ticket-detail-row">
                      <div className="ticket-detail-label">Mô tả:</div>
                      <textarea 
                        className="detail-textarea"
                        value={selectedTicket.description || ''}
                        onChange={(e) => setSelectedTicket({
                          ...selectedTicket,
                          description: e.target.value
                        })}
                        placeholder="Nhập mô tả chi tiết..."
                      />
                    </div>
                  )}
                  
                  {modalMode === 'edit' && (
                    <div className="modal-actions">
                      <button 
                        className="btn-secondary" 
                        onClick={closeModal}
                      >
                        Hủy
                      </button>
                      <button 
                        className="btn-primary" 
                        onClick={() => handleUpdateTicket(selectedTicket)}
                      >
                        Lưu thay đổi
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Tickets; 