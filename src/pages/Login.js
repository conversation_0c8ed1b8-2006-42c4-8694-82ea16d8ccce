import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ock, FiAlertCircle, FiEye, FiEyeOff, FiMoon, FiSun } from 'react-icons/fi';
import '../styles/Login.css';
import apiService from '../services/api.service';
import { ENDPOINTS } from '../config/api.config';
import { useTheme } from '../context/ThemeContext';

const Login = ({ onLogin }) => {
  const { theme, toggleTheme } = useTheme();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // Call login API
      const loginResponse = await apiService.post(ENDPOINTS.AUTH.LOGIN, {
        username,
        password
      });

      if (loginResponse.status === 1 && loginResponse.access_token) {
        // Calculate expiration time
        const expiresAt = new Date(Date.now() + (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000));

        // Create temporary user data with token
        const tempUserData = {
          username: username,
          name: '',
          role: '',
          isLoggedIn: true,
          token: loginResponse.access_token,
          expiresAt: expiresAt.toISOString()
        };

        // Store temporary user data in localStorage
        localStorage.setItem('user', JSON.stringify(tempUserData));

        // Call profile API with the token
        const profileResponse = await apiService.get(ENDPOINTS.AUTH.PROFILE);

        // Update user data with profile information
        const userData = {
          ...tempUserData,
          username: profileResponse.username,
          name: profileResponse.display_name,
          given_name: profileResponse.given_name,
          role: profileResponse.role,
          manager: profileResponse.manager,
          id: profileResponse.id,
          // Include the type from login response for welcome flow
          loginType: loginResponse.type || 'no-change'
        };

        // Update user data in localStorage
        localStorage.setItem('user', JSON.stringify(userData));

        // Call the onLogin callback
        onLogin(userData);
      }
      else if (loginResponse.status === 0)
      {
        setError(loginResponse.message);
      }
      else {
        setError('Error logging in. Please try again or contact support.');
      }
    } catch (error) {
      if (error.response?.data?.detail === 'Inactive user') {
        setError('Your account is inactive. Please contact your administrator.');
      } else {
        setError('Error logging in. Please try again or contact support.');
      }
      // Clear any temporary data if there's an error
      localStorage.removeItem('user');
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="login-container">
      <div className="login-theme-toggle">
        <button
          className="theme-toggle-btn"
          onClick={toggleTheme}
          title={theme === "light" ? "Switch to dark mode" : "Switch to light mode"}
        >
          {theme === "light" ? <FiMoon /> : <FiSun />}
        </button>
      </div>

      <div className="login-card-container">
        <div className="login-card">
          <div className="login-header">
            <h1>ZDS DataQuest</h1>
            {/* <p>Login to continue</p> */}
          </div>

          <div className="login-feature-icons">
            <div className="login-feature-icon bg-primary-green">
              <FiUser />
            </div>
            <div className="login-feature-icon bg-secondary-light-blue">
              <FiLock />
            </div>
            <div className="login-feature-icon bg-secondary-purple">
              <FiAlertCircle />
            </div>
          </div>

          {error && (
            <div className="login-error">
              <FiAlertCircle />
              <span>{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="login-form">
            <div className="login-form-group">
              <label htmlFor="username">
                <FiUser /> Username
              </label>
              <input
                type="text"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter username"
                required
              />
            </div>

            <div className="login-form-group">
              <label htmlFor="password">
                <FiLock /> Password
              </label>
              <div className="login-password-input-container">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter PIN + OTP"
                  required
                />
                <button
                  type="button"
                  className="login-password-toggle"
                  onClick={togglePasswordVisibility}
                  tabIndex="-1"
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              </div>
            </div>

            <div className="login-form-footer">
              <div className="login-remember-me">
                <input
                  type="checkbox"
                  id="remember"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <label htmlFor="remember">Remember me</label>
              </div>
              {/* <a href="/forgot-password" className="forgot-password">
                Forgot password?
              </a> */}
            </div>

            <button
              type="submit"
              className="login-button"
              disabled={isLoading}
            >
              {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;