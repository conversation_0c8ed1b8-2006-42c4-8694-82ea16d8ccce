import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiArrowLeft,
  FiBarChart2,
  FiUser,
  FiCheck,
  FiRefreshCw,
  FiAlertCircle,
  FiGrid,
  FiLayers,
  FiSearch,
  FiX,
  FiArrowUp,
  FiCalendar,
  FiFileText,
  FiCheckCircle,
} from "react-icons/fi";
import apiService from "../../services/api.service";
import { ENDPOINTS } from "../../config/api.config";
import "../../styles/RequestReport.css";

const RequestReport = () => {
  const navigate = useNavigate();
  const [views, setViews] = useState([]);
  const [workbooks, setWorkbooks] = useState([]);
  const [selectedViews, setSelectedViews] = useState([]);
  const [selectedWorkbooks, setSelectedWorkbooks] = useState([]);
  const [isLoadingViews, setIsLoadingViews] = useState(false);
  const [isLoadingWorkbooks, setIsLoadingWorkbooks] = useState(false);
  const [viewsError, setViewsError] = useState(null);
  const [workbooksError, setWorkbooksError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [createdRequest, setCreatedRequest] = useState(null);
  const [approverOptions, setApproverOptions] = useState([]);
  const [reviewerOptions, setReviewerOptions] = useState([]);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    due_date: "",
    reviewer_id: "",
    approver_id: "",
  });

  // Tab state
  const [activeTab, setActiveTab] = useState("views"); // 'views' or 'workbooks'

  // Pagination state
  const [viewsPage, setViewsPage] = useState(1);
  const [workbooksPage, setWorkbooksPage] = useState(1);
  const [hasMoreViews, setHasMoreViews] = useState(true);
  const [hasMoreWorkbooks, setHasMoreWorkbooks] = useState(true);
  const [loadingMoreViews, setLoadingMoreViews] = useState(false);
  const [loadingMoreWorkbooks, setLoadingMoreWorkbooks] = useState(false);
  const PAGE_SIZE = 15;

  // Filter state
  const [viewsOwnerFilter, setViewsOwnerFilter] = useState("");
  const [workbooksOwnerFilter, setWorkbooksOwnerFilter] = useState("");
  const [viewsSearchQuery, setViewsSearchQuery] = useState("");
  const [workbooksSearchQuery, setWorkbooksSearchQuery] = useState("");

  // Refs for infinite scroll
  const viewsContainerRef = useRef(null);
  const workbooksContainerRef = useRef(null);

  // Owners data from API
  const [reportOwners, setReportOwners] = useState([]);
  const [isLoadingOwners, setIsLoadingOwners] = useState(false);

  // Back to top button state
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Fetch initial data on component mount
  useEffect(() => {
    document.title = "Request Report Access";
    fetchViews(1, true);
    fetchWorkbooks(1, true);

    // Check if we need to load more data after initial load
    const checkInitialDataLoad = () => {
      if (
        activeTab === "views" &&
        views.length > 0 &&
        views.length < PAGE_SIZE &&
        hasMoreViews
      ) {
        loadMoreViews();
      }

      if (
        activeTab === "workbooks" &&
        workbooks.length > 0 &&
        workbooks.length < PAGE_SIZE &&
        hasMoreWorkbooks
      ) {
        loadMoreWorkbooks();
      }
    };

    // Wait for initial data to load
    const timer = setTimeout(checkInitialDataLoad, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Handle scroll for back to top button
  useEffect(() => {
    const handleScroll = () => {
      // Show back to top button when scrolled past 3 pages worth of content
      // Assuming each page is roughly the viewport height
      const scrollThreshold = window.innerHeight * 3;
      setShowBackToTop(window.scrollY > scrollThreshold);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Fetch owners from API
  const fetchOwners = async () => {
    setIsLoadingOwners(true);
    try {
      const response = await apiService.get(ENDPOINTS.ATLAS.GET_REPORT_OWNERS);
      let data = [];

      // Process API response
      if (Array.isArray(response)) {
        data = response;
      } else if (response && Array.isArray(response.data)) {
        data = response.data;
      } else {
        throw new Error("Unexpected response format");
      }

      // Sort owners by name
      data.sort((a, b) => a.owner_name.localeCompare(b.owner_name));

      // Update state with owners data
      setReportOwners(data);
    } catch (error) {
      console.error("Error fetching owners:", error);
    } finally {
      setIsLoadingOwners(false);
    }
  };

  // Fetch owners on component mount
  useEffect(() => {
    fetchOwners();
  }, []);

  // Fetch approvers & executors for dropdowns based on request type
  useEffect(() => {
    const fetchApproversExecutors = async () => {
      try {
        const response = await apiService.get(ENDPOINTS.CONFIG.GET);
        // Approvers
        const approvers = (response || [])
          .filter(
            (item) =>
              item.request_type === "access-report" &&
              item.role === "approver"
          )
          .map((item) => ({
            user_id: item.user_id,
            display_name: item.display_name,
          }));
        setApproverOptions(approvers);
        console.log("Response:", approvers);
        // Set default approver if not set
        if (approvers.length > 0 && !formData.approver_id) {
          setFormData((prev) => ({
            ...prev,
            approver_id: approvers[0].user_id,
          }));
        }
        // Reviewer
        const reviewers = (response || [])
          .filter(
            (item) =>
              item.request_type === "access-report" &&
              item.role === "reviewer"
          )
          .map((item) => ({
            user_id: item.user_id,
            display_name: item.display_name,
          }));
        setReviewerOptions(reviewers);
        // Executor is optional, so we don't set a default value
        // This allows the "Select an Executor" option to be the default
      } catch (err) {
        console.error("Failed to fetch approvers and executors:", err);
        setApproverOptions([]);
        setReviewerOptions([]);
        // setErrorWithTimeout("Failed to load approvers and executors");
      }
    };
    fetchApproversExecutors();
    // eslint-disable-next-line
  }, []);

  // Setup intersection observer for infinite scrolling
  useEffect(() => {
    const viewsObserver = new IntersectionObserver(
      (entries) => {
        if (
          entries[0].isIntersecting &&
          hasMoreViews &&
          !loadingMoreViews &&
          activeTab === "views"
        ) {
          loadMoreViews();
        }
      },
      { threshold: 0.1, rootMargin: "100px" }
    );

    const workbooksObserver = new IntersectionObserver(
      (entries) => {
        if (
          entries[0].isIntersecting &&
          hasMoreWorkbooks &&
          !loadingMoreWorkbooks &&
          activeTab === "workbooks"
        ) {
          loadMoreWorkbooks();
        }
      },
      { threshold: 0.1, rootMargin: "100px" }
    );

    // Small delay to ensure DOM is ready
    const timer = setTimeout(() => {
      if (viewsContainerRef.current) {
        viewsObserver.observe(viewsContainerRef.current);
      }

      if (workbooksContainerRef.current) {
        workbooksObserver.observe(workbooksContainerRef.current);
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      viewsObserver.disconnect();
      workbooksObserver.disconnect();
    };
  }, [
    hasMoreViews,
    loadingMoreViews,
    hasMoreWorkbooks,
    loadingMoreWorkbooks,
    activeTab,
    views.length,
    workbooks.length,
  ]);

  // Fetch views from API with pagination, filtering, and search
  const fetchViews = async (
    page = 1,
    reset = false,
    ownerFilter = viewsOwnerFilter,
    searchQuery = viewsSearchQuery
  ) => {
    if (page === 1) {
      setIsLoadingViews(true);
    } else {
      setLoadingMoreViews(true);
    }

    if (reset) {
      setViewsError(null);
    }

    try {
      // Construct API URL with pagination, filter, and search params
      const apiUrl =
        `${ENDPOINTS.ATLAS.GET_VIEWS}?page=${page}&limit=${PAGE_SIZE}` +
        (ownerFilter && ownerFilter !== ""
          ? `&owner=${encodeURIComponent(ownerFilter)}`
          : "") +
        (searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : "");

      const response = await apiService.get(apiUrl);
      let data = [];

      // Process API response
      if (Array.isArray(response)) {
        data = response;
      } else if (response && Array.isArray(response.data)) {
        data = response.data;
      } else {
        throw new Error("Unexpected response format");
      }

      // Check if there are more items (assuming API returns total count or has_more flag)
      // For now, we'll assume there are no more items if we received fewer than PAGE_SIZE
      setHasMoreViews(data.length >= PAGE_SIZE);

      // Update state
      if (reset || page === 1) {
        setViews(data);
        setViewsPage(1);
      } else {
        setViews((prevViews) => [...prevViews, ...data]);
        setViewsPage(page);
      }
    } catch (error) {
      console.error("Error fetching views:", error);
      if (reset || page === 1) {
        setViewsError("Failed to load views. Please try again.");
        setViews([]);
        setHasMoreViews(false);
      }
    } finally {
      if (page === 1) {
        setIsLoadingViews(false);
      } else {
        setLoadingMoreViews(false);
      }
    }
  };

  // Load more views for infinite scrolling
  const loadMoreViews = () => {
    if (!loadingMoreViews && hasMoreViews && views.length > 0) {
      fetchViews(viewsPage + 1);
    }
  };

  // Fetch workbooks from API with pagination, filtering, and search
  const fetchWorkbooks = async (
    page = 1,
    reset = false,
    ownerFilter = workbooksOwnerFilter,
    searchQuery = workbooksSearchQuery
  ) => {
    if (page === 1) {
      setIsLoadingWorkbooks(true);
    } else {
      setLoadingMoreWorkbooks(true);
    }

    if (reset) {
      setWorkbooksError(null);
    }

    try {
      // Construct API URL with pagination, filter, and search params
      const apiUrl =
        `${ENDPOINTS.ATLAS.GET_WORKBOOKS}?page=${page}&limit=${PAGE_SIZE}` +
        (ownerFilter && ownerFilter !== ""
          ? `&owner=${encodeURIComponent(ownerFilter)}`
          : "") +
        (searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : "");

      const response = await apiService.get(apiUrl);
      let data = [];

      // Process API response
      if (Array.isArray(response)) {
        data = response;
      } else if (response && Array.isArray(response.data)) {
        data = response.data;
      } else {
        throw new Error("Unexpected response format");
      }

      // Check if there are more items (assuming API returns total count or has_more flag)
      // For now, we'll assume there are no more items if we received fewer than PAGE_SIZE
      setHasMoreWorkbooks(data.length >= PAGE_SIZE);

      // Update state
      if (reset || page === 1) {
        setWorkbooks(data);
        setWorkbooksPage(1);
      } else {
        setWorkbooks((prevWorkbooks) => [...prevWorkbooks, ...data]);
        setWorkbooksPage(page);
      }
    } catch (error) {
      console.error("Error fetching workbooks:", error);
      if (reset || page === 1) {
        setWorkbooksError("Failed to load workbooks. Please try again.");
        setWorkbooks([]);
        setHasMoreWorkbooks(false);
      }
    } finally {
      if (page === 1) {
        setIsLoadingWorkbooks(false);
      } else {
        setLoadingMoreWorkbooks(false);
      }
    }
  };

  // Load more workbooks for infinite scrolling
  const loadMoreWorkbooks = () => {
    if (!loadingMoreWorkbooks && hasMoreWorkbooks && workbooks.length > 0) {
      fetchWorkbooks(workbooksPage + 1);
    }
  };

  // Toggle view selection
  const toggleViewSelection = (viewId) => {
    setSelectedViews((prevSelected) => {
      const newSelection = prevSelected.includes(viewId)
        ? prevSelected.filter((id) => id !== viewId)
        : [...prevSelected, viewId];

      // Show success message when items are selected
      updateSuccessMessage(newSelection, selectedWorkbooks);
      return newSelection;
    });
  };

  // Toggle workbook selection
  const toggleWorkbookSelection = (workbookId) => {
    setSelectedWorkbooks((prevSelected) => {
      const newSelection = prevSelected.includes(workbookId)
        ? prevSelected.filter((id) => id !== workbookId)
        : [...prevSelected, workbookId];

      // Show success message when items are selected
      updateSuccessMessage(selectedViews, newSelection);
      return newSelection;
    });
  };

  // Update success message based on selections
  const updateSuccessMessage = (views, workbooks) => {
    const totalSelected = views.length + workbooks.length;

    if (totalSelected > 0) {
      setSuccessMessage(
        `${totalSelected} item${totalSelected > 1 ? "s" : ""} selected`
      );

      // Auto-hide success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } else {
      setSuccessMessage(null);
    }
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Handle views owner filter change
  const handleViewsOwnerFilterChange = (ownerId) => {
    setViewsOwnerFilter(ownerId);
    setViewsPage(1);
    setViews([]);
    setHasMoreViews(true);
    setIsLoadingViews(true); // Show loading indicator
    // Pass the selected owner ID directly to fetchViews
    fetchViews(1, true, ownerId, viewsSearchQuery);
  };

  // Handle workbooks owner filter change
  const handleWorkbooksOwnerFilterChange = (ownerId) => {
    setWorkbooksOwnerFilter(ownerId);
    setWorkbooksPage(1);
    setWorkbooks([]);
    setHasMoreWorkbooks(true);
    setIsLoadingWorkbooks(true); // Show loading indicator
    // Pass the selected owner ID directly to fetchWorkbooks
    fetchWorkbooks(1, true, ownerId, workbooksSearchQuery);
  };

  // Debounce search function
  const useDebounce = (callback, delay) => {
    const timeoutRef = useRef(null);

    return (searchQuery) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(searchQuery);
      }, delay);
    };
  };

  // Handle views search
  const handleViewsSearch = (e) => {
    const query = e.target.value;
    setViewsSearchQuery(query);

    // Use debounced search with current query
    debouncedViewsSearch(query);
  };

  // Debounced search for views
  const debouncedViewsSearch = useDebounce((searchQuery) => {
    // Reset pagination and clear existing data
    setViewsPage(1);
    setViews([]);
    setHasMoreViews(true);
    setIsLoadingViews(true); // Show loading indicator
    fetchViews(1, true, viewsOwnerFilter, searchQuery);
  }, 500);

  // Handle workbooks search
  const handleWorkbooksSearch = (e) => {
    const query = e.target.value;
    setWorkbooksSearchQuery(query);

    // Use debounced search with current query
    debouncedWorkbooksSearch(query);
  };

  // Debounced search for workbooks
  const debouncedWorkbooksSearch = useDebounce((searchQuery) => {
    // Reset pagination and clear existing data
    setWorkbooksPage(1);
    setWorkbooks([]);
    setHasMoreWorkbooks(true);
    setIsLoadingWorkbooks(true); // Show loading indicator
    fetchWorkbooks(1, true, workbooksOwnerFilter, searchQuery);
  }, 500);

  // Clear views filters
  const clearViewsFilters = () => {
    const emptyOwner = "";
    const emptySearch = "";
    setViewsOwnerFilter(emptyOwner); // Reset to empty string (All)
    setViewsSearchQuery(emptySearch);
    setViewsPage(1);
    setViews([]);
    setHasMoreViews(true);
    setIsLoadingViews(true); // Show loading indicator
    fetchViews(1, true, emptyOwner, emptySearch);
  };

  // Clear workbooks filters
  const clearWorkbooksFilters = () => {
    const emptyOwner = "";
    const emptySearch = "";
    setWorkbooksOwnerFilter(emptyOwner); // Reset to empty string (All)
    setWorkbooksSearchQuery(emptySearch);
    setWorkbooksPage(1);
    setWorkbooks([]);
    setHasMoreWorkbooks(true);
    setIsLoadingWorkbooks(true); // Show loading indicator
    fetchWorkbooks(1, true, emptyOwner, emptySearch);
  };

  // Open confirmation modal
  const handleSubmit = () => {
    if (selectedViews.length === 0 && selectedWorkbooks.length === 0) {
      alert("Please select at least one view or workbook");
      return;
    }

    // Show confirmation modal
    setShowConfirmModal(true);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Close confirmation modal
  const closeConfirmModal = () => {
    setShowConfirmModal(false);
  };

  // Close success modal
  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    // Reset the submitting state when closing the modal
    setIsSubmitting(false);
  };

  // Submit request after confirmation
  const submitRequest = async () => {
    if (!formData.title.trim()) {
      alert("Title is required");
      return;
    }

    setIsSubmitting(true);

    try {
      // Get the list of owners (not owner_name) of selected items for executors
      const selectedViewsData = views.filter((view) =>
        selectedViews.includes(view.id)
      );
      const selectedWorkbooksData = workbooks.filter((workbook) =>
        selectedWorkbooks.includes(workbook.id)
      );

      // Extract unique owner IDs from selected items
      const executors = [
        ...new Set([
          ...selectedViewsData.map((view) => view.owner || view.owner_id),
          ...selectedWorkbooksData.map(
            (workbook) => workbook.owner || workbook.owner_id
          ),
        ]),
      ].filter(Boolean); // Filter out any undefined or null values

      // Create JSON structure with selected items information
      // Group items by owner
      const itemsByOwner = {};

      // Process views
      selectedViewsData.forEach((view) => {
        const ownerName = view.owner_name || "Unknown Owner";
        const ownerId = view.owner || view.owner_id;
        if (!itemsByOwner[ownerName]) {
          itemsByOwner[ownerName] = {
            owner_id: ownerId,
            owner_name: ownerName,
            views: [],
            workbooks: [],
          };
        }
        // Create link to view
        // const viewLink = `https://atlas.vng.com.vn/#/site/ZLPDataServices/workbooks/${view.workbook_id}/views`;
        itemsByOwner[ownerName].views.push({
          id: view.id,
          name: view.name,
          workbook_id: view.workbook_id,
          completed: false,
          // thumbnail_id: view.thumbnail_id,
          // updated_at: view.updated_at,
          // link: viewLink
        });
      });

      // Process workbooks
      selectedWorkbooksData.forEach((workbook) => {
        const ownerName = workbook.owner_name || "Unknown Owner";
        const ownerId = workbook.owner || workbook.owner_id;
        if (!itemsByOwner[ownerName]) {
          itemsByOwner[ownerName] = {
            owner_id: ownerId,
            owner_name: ownerName,
            views: [],
            workbooks: [],
          };
        }
        // Create link to workbook
        // const workbookLink = `https://atlas.vng.com.vn/#/site/ZLPDataServices/workbooks/${workbook.id}/views`;
        itemsByOwner[ownerName].workbooks.push({
          id: workbook.id,
          name: workbook.name,
          completed: false,
          // thumbnail_id: workbook.thumbnail_id,
          // updated_at: workbook.updated_at,
          // link: workbookLink
        });
      });

      // Create JSON structure for requested items
      const requestedItems = {
        summary: {
          total_items: selectedViews.length + selectedWorkbooks.length,
          total_views: selectedViews.length,
          total_workbooks: selectedWorkbooks.length,
          total_owners: Object.keys(itemsByOwner).length,
          total_completed: 0,
        },
        items_by_owner: itemsByOwner,
        selected_views: selectedViews,
        selected_workbooks: selectedWorkbooks,
      };

      // Convert to JSON string
      const itemsDescription = JSON.stringify(requestedItems, null, 2);

      // Prepare request data
      const requestData = {
        request_type: "access-report",
        title: formData.title,
        description: formData.description,
        report_needed: itemsDescription,
        executors: executors,
      };

      // Add optional fields if provided
      if (formData.due_date) {
        requestData.due_date = formData.due_date;
      }

      if (formData.reviewer_id) {
        requestData.reviewer_id = formData.reviewer_id;
      }

      if (formData.approver_id) {
        requestData.approver_id = formData.approver_id;
      }

      // Submit request
      const response = await apiService.post(
        ENDPOINTS.REQUEST.CREATE,
        requestData
      );

      if (response) {
        // Close confirmation modal
        setShowConfirmModal(false);

        // Store the created request object
        setCreatedRequest(response);

        // Show success modal
        setShowSuccessModal(true);

        // Reset submitting state after successful submission
        setIsSubmitting(false);
      } else {
        throw new Error("Failed to create request");
      }
    } catch (error) {
      console.error("Error submitting request:", error);
      alert("Failed to submit request. Please try again.");
      setIsSubmitting(false);
    }
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // Clear all selections function
  const clearAllSelections = () => {
    setSelectedViews([]);
    setSelectedWorkbooks([]);
    setSuccessMessage("All selections cleared");

    // Auto-hide success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  // Refresh page function
  const refreshPage = () => {
    // Reset data and refetch
    setViewsPage(1);
    setWorkbooksPage(1);
    setViews([]);
    setWorkbooks([]);
    setHasMoreViews(true);
    setHasMoreWorkbooks(true);

    // Show loading indicators
    setIsLoadingViews(true);
    setIsLoadingWorkbooks(true);

    // Fetch fresh data
    fetchViews(1, true);
    fetchWorkbooks(1, true);

    // Show success message
    setSuccessMessage("Page refreshed");

    // Auto-hide success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  // Render thumbnail from local static files
  const renderThumbnail = (thumbnailId, type) => {
    if (thumbnailId) {
      // Xác định loại thumbnail (view hoặc workbook)
      const folderPath = type === "view" ? "views" : "workbooks";

      // Đường dẫn đến file thumbnail sử dụng thumbnail_id
      // Lưu ý: Chúng ta sử dụng thumbnail_id trực tiếp làm tên file

      // Đường dẫn đến file thumbnail
      const thumbnailUrl = `/atlas/thumbnails/${folderPath}/${thumbnailId}.webp`;

      // Đường dẫn fallback nếu file WebP không tồn tại
      const fallbackUrl = `/atlas/thumbnails/${folderPath}/${thumbnailId}.jpg`;

      return (
        <picture>
          {/* Sử dụng WebP cho trình duyệt hỗ trợ */}
          <source srcSet={thumbnailUrl} type="image/webp" />

          {/* Fallback cho trình duyệt không hỗ trợ WebP */}
          <img
            src={fallbackUrl}
            alt="Report thumbnail"
            loading="lazy"
            className="thumbnail-image"
            onError={(e) => {
              // Nếu cả WebP và JPG đều không tồn tại, hiển thị placeholder
              if (e.target.src === fallbackUrl) {
                e.target.onerror = null;
                e.target.style.display = "none";
                const placeholder = document.createElement("div");
                placeholder.className = "placeholder-icon";
                placeholder.innerHTML =
                  '<svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M20 20V4a2 2 0 0 0-2-2h-8a2 2 0 0 0-2 2v16"></path><path d="M2 6h18"></path><path d="M2 12h18"></path><path d="M2 18h18"></path></svg>';
                e.target.parentNode.appendChild(placeholder);
              } else {
                // Nếu WebP không tồn tại, thử JPG
                e.target.src = fallbackUrl;
              }
            }}
          />
        </picture>
      );
    }

    // Nếu không có thumbnailId, hiển thị placeholder
    return (
      <div className="placeholder-icon">
        <FiBarChart2 />
      </div>
    );
  };

  return (
    <div className="request-report-container">
      <div className="request-report-header">
        <button
          type="button"
          className="back-button"
          onClick={() => navigate("/requests/types")}
        >
          <FiArrowLeft /> Back
        </button>
        <h2 className="request-report-title">Request Report Access</h2>
      </div>

      {successMessage && (
        <div className="success-message">
          <FiCheck />
          <span>{successMessage}</span>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="request-report-modal-overlay">
          <div className="request-report-modal-container">
            <div className="request-report-modal-header">
              <h3>Confirm Request Submission</h3>
              <button
                className="request-report-modal-close"
                onClick={closeConfirmModal}
              >
                <FiX />
              </button>
            </div>
            <div className="request-report-modal-body">
              <div className="form-group">
                <label htmlFor="title">
                  <FiFileText className="form-icon" /> Title*
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="E.g., Access request for Atlas DGS reports"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="description">
                  <FiFileText className="form-icon" /> Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  style={{ maxWidth: "100%", minWidth: "100%" }}
                  placeholder="Please explain why you need access to these reports and how you plan to use them. This helps the approver understand your request."
                />
              </div>

              <div className="form-group">
                <label>
                  <FiGrid className="form-icon" /> Selected Items
                </label>
                <div className="selected-items-summary">
                  <div className="selected-items-header">
                    <strong>Views ({selectedViews.length})</strong>
                  </div>
                  {selectedViews.length > 0 ? (
                    <div className="selected-items-list">
                      {views
                        .filter((view) => selectedViews.includes(view.id))
                        .map((view) => (
                          <div key={view.id} className="selected-item">
                            <span className="selected-item-name">
                              {view.name}
                            </span>
                            <span className="selected-item-owner">
                              {view.owner_name}
                            </span>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="no-items-selected">No views selected</div>
                  )}

                  <div className="selected-items-header">
                    <strong>Workbooks ({selectedWorkbooks.length})</strong>
                  </div>
                  {selectedWorkbooks.length > 0 ? (
                    <div className="selected-items-list">
                      {workbooks
                        .filter((workbook) =>
                          selectedWorkbooks.includes(workbook.id)
                        )
                        .map((workbook) => (
                          <div key={workbook.id} className="selected-item">
                            <span className="selected-item-name">
                              {workbook.name}
                            </span>
                            <span className="selected-item-owner">
                              {workbook.owner_name}
                            </span>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="no-items-selected">
                      No workbooks selected
                    </div>
                  )}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="reviewer_id">
                  <FiUser className="form-icon" /> Reviewer (optional)
                </label>
                <select
                  id="reviewer_id"
                  name="reviewer_id"
                  value={formData.reviewer_id}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="">Select a Reviewer</option>
                  {reviewerOptions.map((user, idx) => (
                    <option
                      key={`approver-${user.user_id}-${idx}`}
                      value={user.user_id}
                    >
                      {user.display_name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="approver_id">
                  <FiUser className="form-icon" /> Approver (optional)
                </label>
                <select
                  id="approver_id"
                  name="approver_id"
                  value={formData.approver_id}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="">Select an Approver</option>
                  {approverOptions.map((user, idx) => (
                    <option
                      key={`approver-${user.user_id}-${idx}`}
                      value={user.user_id}
                    >
                      {user.display_name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="due_date">
                  <FiCalendar className="form-icon" /> Due Date (optional)
                </label>
                <input
                  type="date"
                  id="due_date"
                  name="due_date"
                  value={formData.due_date}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="request-report-modal-actions">
              <div className="request-report-modal-actions-left">
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={closeConfirmModal}
                >
                  Back
                </button>
              </div>
              <div className="request-report-modal-actions-right">
                <button
                  type="button"
                  className="btn-primary"
                  onClick={submitRequest}
                  disabled={isSubmitting || !formData.title.trim()}
                >
                  {isSubmitting ? "Submitting..." : "Submit Request"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="request-report-modal-overlay">
          <div className="request-report-modal-container success-modal">
            <div className="request-report-modal-header">
              <h3>Request Submitted Successfully</h3>
            </div>
            <div className="request-report-modal-body success-modal-body">
              <div className="success-modal-icon">
                <FiCheckCircle />
              </div>
              <p className="success-modal-message">
                Your request has been submitted successfully.
              </p>
              {createdRequest && (
                <div className="request-details">
                  <p>
                    <strong>Request ID:</strong>{" "}
                    {createdRequest.id ||
                      (createdRequest.data && createdRequest.data.id)}
                  </p>
                  <p>
                    <strong>Title:</strong> {formData.title}
                  </p>
                  <p>
                    <strong>Status:</strong>{" "}
                    {createdRequest.status || "Pending"}
                  </p>
                </div>
              )}
              <p className="success-modal-description">
                You can track the status of your request in the Request Tracking
                page.
              </p>
            </div>
            <div className="request-report-modal-actions">
              <div className="request-report-modal-actions-left">
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={() => {
                    closeSuccessModal();
                    clearAllSelections();
                    navigate("/requests/report");
                  }}
                >
                  OK
                </button>
              </div>
              <div className="request-report-modal-actions-right">
                <button
                  type="button"
                  className="btn-primary"
                  onClick={() => {
                    closeSuccessModal();
                    // Get the request ID from the response
                    let requestId = null;
                    if (createdRequest) {
                      if (createdRequest.id) {
                        requestId = createdRequest.id;
                      } else if (
                        createdRequest.data &&
                        createdRequest.data.id
                      ) {
                        requestId = createdRequest.data.id;
                      } else if (createdRequest.request_id) {
                        requestId = createdRequest.request_id;
                      } else if (
                        createdRequest.data &&
                        createdRequest.data.request_id
                      ) {
                        requestId = createdRequest.data.request_id;
                      }
                    }
                    navigate(
                      requestId
                        ? `/requests/detail?id=${requestId}`
                        : "/requests"
                    );
                  }}
                >
                  <span>Request Tracking</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          className="back-to-top-button"
          onClick={scrollToTop}
          aria-label="Back to top"
        >
          <FiArrowUp />
        </button>
      )}

      {/* Tabs */}
      <div className="request-report-tabs">
        <div className="tab-buttons">
          <button
            className={`tab-button ${activeTab === "views" ? "active" : ""}`}
            onClick={() => handleTabChange("views")}
          >
            <FiGrid /> Views ({selectedViews.length} selected)
          </button>
          <button
            className={`tab-button ${
              activeTab === "workbooks" ? "active" : ""
            }`}
            onClick={() => handleTabChange("workbooks")}
          >
            <FiLayers /> Workbooks ({selectedWorkbooks.length} selected)
          </button>
        </div>
        <div className="tab-actions">
          <button
            type="button"
            className="btn-secondary"
            onClick={refreshPage}
            title="Refresh Page"
          >
            Refresh Page
          </button>
          <button
            type="button"
            className="btn-secondary"
            onClick={clearAllSelections}
            disabled={
              isSubmitting ||
              (selectedViews.length === 0 && selectedWorkbooks.length === 0)
            }
            title="Clear All Selections"
          >
            Clear Selected
          </button>
          <button
            type="button"
            className="btn-primary submit-button"
            onClick={handleSubmit}
            disabled={
              isSubmitting ||
              (selectedViews.length === 0 && selectedWorkbooks.length === 0)
            }
          >
            {isSubmitting ? "Submitting..." : "Create Request"}
          </button>
        </div>
      </div>

      {/* Views Tab Content */}
      {activeTab === "views" && (
        <div className="request-report-section">
          {/* Search and Filter Bar */}
          <div className="filter-search-bar">
            <div className="filter-container">
              <label htmlFor="views-owner-filter">Owner:</label>
              <select
                id="views-owner-filter"
                value={viewsOwnerFilter}
                onChange={(e) => handleViewsOwnerFilterChange(e.target.value)}
                className="filter-select"
                disabled={isLoadingOwners}
              >
                <option value="">All</option>
                {reportOwners.map((owner) => (
                  <option key={owner.owner} value={owner.owner}>
                    {owner.owner_name}
                  </option>
                ))}
              </select>
              {isLoadingOwners && <div className="loading-spinner-small"></div>}
            </div>
            <div className="search-container">
              {isLoadingViews && viewsSearchQuery ? (
                <FiRefreshCw className="search-icon spinning" />
              ) : (
                <FiSearch className="search-icon" />
              )}
              <input
                type="text"
                placeholder="Search views..."
                value={viewsSearchQuery}
                onChange={handleViewsSearch}
                className="search-input"
              />
              {viewsSearchQuery && (
                <FiX
                  className="clear-search-icon"
                  onClick={() => {
                    const emptySearch = "";
                    setViewsSearchQuery(emptySearch);
                    setViewsPage(1);
                    setViews([]);
                    setHasMoreViews(true);
                    setIsLoadingViews(true);
                    fetchViews(1, true, viewsOwnerFilter, emptySearch);
                  }}
                />
              )}
            </div>

            {(viewsOwnerFilter || viewsSearchQuery) && (
              <button className="clear-filters-btn" onClick={clearViewsFilters}>
                Clear Filters
              </button>
            )}
          </div>

          {viewsError && (
            <div className="error-message">
              <FiAlertCircle />
              <span>{viewsError}</span>
            </div>
          )}

          {isLoadingViews && views.length === 0 ? (
            <div className="loading-spinner"></div>
          ) : views.length === 0 ? (
            <div className="empty-state">
              <p>No views available</p>
            </div>
          ) : (
            <>
              <div className="request-report-grid">
                {views.map((view) => (
                  <div
                    key={view.id}
                    className={`report-item ${
                      selectedViews.includes(view.id) ? "selected" : ""
                    }`}
                    onClick={() => toggleViewSelection(view.id)}
                  >
                    <div className="report-item-thumbnail">
                      {renderThumbnail(view.thumbnail_id, "view")}
                    </div>
                    <div className="report-item-content">
                      <h4 className="report-item-title">{view.name}</h4>
                      <div className="report-item-owner">
                        <FiUser />
                        <span>{view.owner_name}</span>
                      </div>
                      {view.updated_at && (
                        <div className="report-item-updated">
                          <FiCalendar />
                          <span>
                            Updated:{" "}
                            {new Date(view.updated_at).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div
                      className={`report-item-selection ${
                        selectedViews.includes(view.id) ? "selected" : ""
                      }`}
                    >
                      {selectedViews.includes(view.id) && <FiCheck />}
                    </div>
                  </div>
                ))}
              </div>

              {/* Infinite scroll loading indicator */}
              {loadingMoreViews && (
                <div className="loading-more">
                  <div className="loading-spinner small"></div>
                  <span>Loading more...</span>
                </div>
              )}

              {/* Sentinel element for infinite scroll */}
              <div ref={viewsContainerRef} className="scroll-sentinel"></div>
            </>
          )}
        </div>
      )}

      {/* Workbooks Tab Content */}
      {activeTab === "workbooks" && (
        <div className="request-report-section">
          {/* Search and Filter Bar */}
          <div className="filter-search-bar">
            <div className="filter-container">
              <label htmlFor="workbooks-owner-filter">Owner:</label>
              <select
                id="workbooks-owner-filter"
                value={workbooksOwnerFilter}
                onChange={(e) =>
                  handleWorkbooksOwnerFilterChange(e.target.value)
                }
                className="filter-select"
                disabled={isLoadingOwners}
              >
                <option value="">All</option>
                {reportOwners.map((owner) => (
                  <option key={owner.owner} value={owner.owner}>
                    {owner.owner_name}
                  </option>
                ))}
              </select>
              {isLoadingOwners && <div className="loading-spinner-small"></div>}
            </div>

            <div className="search-container">
              {isLoadingWorkbooks && workbooksSearchQuery ? (
                <FiRefreshCw className="search-icon spinning" />
              ) : (
                <FiSearch className="search-icon" />
              )}
              <input
                type="text"
                placeholder="Search workbooks..."
                value={workbooksSearchQuery}
                onChange={handleWorkbooksSearch}
                className="search-input"
              />
              {workbooksSearchQuery && (
                <FiX
                  className="clear-search-icon"
                  onClick={() => {
                    const emptySearch = "";
                    setWorkbooksSearchQuery(emptySearch);
                    setWorkbooksPage(1);
                    setWorkbooks([]);
                    setHasMoreWorkbooks(true);
                    setIsLoadingWorkbooks(true);
                    fetchWorkbooks(1, true, workbooksOwnerFilter, emptySearch);
                  }}
                />
              )}
            </div>

            {(workbooksOwnerFilter || workbooksSearchQuery) && (
              <button
                className="clear-filters-btn"
                onClick={clearWorkbooksFilters}
              >
                Clear Filters
              </button>
            )}
          </div>

          {workbooksError && (
            <div className="error-message">
              <FiAlertCircle />
              <span>{workbooksError}</span>
            </div>
          )}

          {isLoadingWorkbooks && workbooks.length === 0 ? (
            <div className="loading-spinner"></div>
          ) : workbooks.length === 0 ? (
            <div className="empty-state">
              <p>No workbooks available</p>
            </div>
          ) : (
            <>
              <div className="request-report-grid">
                {workbooks.map((workbook) => (
                  <div
                    key={workbook.id}
                    className={`report-item ${
                      selectedWorkbooks.includes(workbook.id) ? "selected" : ""
                    }`}
                    onClick={() => toggleWorkbookSelection(workbook.id)}
                  >
                    <div className="report-item-thumbnail">
                      {renderThumbnail(workbook.thumbnail_id, "workbook")}
                    </div>
                    <div className="report-item-content">
                      <h4 className="report-item-title">{workbook.name}</h4>
                      <div className="report-item-owner">
                        <FiUser />
                        <span>{workbook.owner_name}</span>
                      </div>
                      {workbook.updated_at && (
                        <div className="report-item-updated">
                          <FiCalendar />
                          <span>
                            Updated:{" "}
                            {new Date(workbook.updated_at).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div
                      className={`report-item-selection ${
                        selectedWorkbooks.includes(workbook.id)
                          ? "selected"
                          : ""
                      }`}
                    >
                      {selectedWorkbooks.includes(workbook.id) && <FiCheck />}
                    </div>
                  </div>
                ))}
              </div>

              {/* Infinite scroll loading indicator */}
              {loadingMoreWorkbooks && (
                <div className="loading-more">
                  <div className="loading-spinner small"></div>
                  <span>Loading more...</span>
                </div>
              )}

              {/* Sentinel element for infinite scroll */}
              <div
                ref={workbooksContainerRef}
                className="scroll-sentinel"
              ></div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default RequestReport;
