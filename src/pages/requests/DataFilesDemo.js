import React, { useState } from 'react';
import {
  FiDownload,
  FiFolder,
  FiFile,
  FiLoader,
  FiXCircle,
  FiCheckCircle
} from 'react-icons/fi';
import '../../styles/RequestDetail.css';

// Demo component to showcase the Data Files section
const DataFilesDemo = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const [empty, setEmpty] = useState(false);

  // Mock data files
  const mockDataFiles = [
    {
      id: 176,
      filename: '20250529_130611_59342788-28b6-4a14-87c9-94b457c4f169.json',
      original_filename: 'openapi (11).json',
      file_size: 47783,
      mime_type: 'application/json',
      description: 'Adhoc data file: openapi (11).json',
      created_at: '2025-05-29T13:06:11.900144'
    },
    {
      id: 177,
      filename: 'test_data.csv',
      original_filename: 'customer_data.csv',
      file_size: 1024000,
      mime_type: 'text/csv',
      description: 'Customer data export for analysis',
      created_at: '2025-05-29T14:06:11.900144'
    },
    {
      id: 178,
      filename: 'report.pdf',
      original_filename: 'quarterly_report.pdf',
      file_size: 2048000,
      mime_type: 'application/pdf',
      description: 'Q4 2024 quarterly business report',
      created_at: '2025-05-29T15:06:11.900144'
    }
  ];

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Get file icon based on mime type
  const getFileIcon = (mimeType) => {
    if (mimeType.startsWith("image/")) return <FiFile style={{ color: "#4CAF50" }} />;
    if (mimeType.includes("json")) return <FiFile style={{ color: "#FF9800" }} />;
    if (mimeType.includes("csv")) return <FiFile style={{ color: "#2196F3" }} />;
    if (mimeType.includes("excel") || mimeType.includes("spreadsheet")) 
      return <FiFile style={{ color: "#4CAF50" }} />;
    if (mimeType.includes("pdf")) return <FiFile style={{ color: "#F44336" }} />;
    return <FiFile style={{ color: "#757575" }} />;
  };

  // Mock download function
  const downloadFile = (fileId, filename) => {
    alert(`Downloading: ${filename}`);
  };

  // Mock download all function
  const downloadAllFiles = () => {
    alert(`Downloading all ${mockDataFiles.length} files...`);
  };

  // Demo controls
  const toggleLoading = () => {
    setLoading(!loading);
    setError(false);
    setEmpty(false);
  };

  const toggleError = () => {
    setError(!error);
    setLoading(false);
    setEmpty(false);
  };

  const toggleEmpty = () => {
    setEmpty(!empty);
    setLoading(false);
    setError(false);
  };

  const resetToNormal = () => {
    setLoading(false);
    setError(false);
    setEmpty(false);
  };

  if (loading) {
    return (
      <div className="request-detail-section request-data-file-section">
        <h2>
          <FiFolder /> Data Files
        </h2>
        <div className="loading-state">
          <FiLoader className="spin" />
          <span>Loading data files...</span>
        </div>
        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <button onClick={resetToNormal} style={{ padding: '8px 16px', marginRight: '8px' }}>
            Show Normal State
          </button>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="request-detail-section request-data-file-section">
        <h2>
          <FiFolder /> Data Files
        </h2>
        <div className="error-state">
          <FiXCircle />
          <span>Failed to load data files</span>
        </div>
        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <button onClick={resetToNormal} style={{ padding: '8px 16px', marginRight: '8px' }}>
            Show Normal State
          </button>
        </div>
      </div>
    );
  }

  if (empty) {
    return (
      <div className="request-detail-section request-data-file-section">
        <h2>
          <FiFolder /> Data Files
        </h2>
        <div className="empty-state">
          <FiFile />
          <span>No data files uploaded for this request</span>
        </div>
        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <button onClick={resetToNormal} style={{ padding: '8px 16px', marginRight: '8px' }}>
            Show Normal State
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <h1 style={{ marginBottom: '20px', textAlign: 'center' }}>
        Adhoc Data Files Feature Demo
      </h1>
      
      {/* Demo Controls */}
      <div style={{ 
        marginBottom: '30px', 
        textAlign: 'center',
        padding: '20px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ marginBottom: '15px' }}>Demo Controls</h3>
        <button onClick={toggleLoading} style={{ padding: '8px 16px', marginRight: '8px' }}>
          Show Loading State
        </button>
        <button onClick={toggleError} style={{ padding: '8px 16px', marginRight: '8px' }}>
          Show Error State
        </button>
        <button onClick={toggleEmpty} style={{ padding: '8px 16px', marginRight: '8px' }}>
          Show Empty State
        </button>
        <button onClick={resetToNormal} style={{ padding: '8px 16px' }}>
          Show Normal State
        </button>
      </div>

      {/* Data Files Section */}
      <div className="request-detail-section request-data-file-section">
        <div className="section-header">
          <h2>
            <FiFolder /> Data Files ({mockDataFiles.length})
          </h2>
          <button 
            className="btn-secondary btn-download-all"
            onClick={downloadAllFiles}
            title="Download all files"
          >
            <FiDownload />
            Download All
          </button>
        </div>
        
        <div className="data-files-list">
          {mockDataFiles.map((file) => (
            <div key={file.id} className="data-file-item">
              <div className="file-icon">
                {getFileIcon(file.mime_type)}
              </div>
              
              <div className="file-info">
                <div className="file-name" title={file.original_filename}>
                  {file.original_filename}
                </div>
                <div className="file-meta">
                  <span className="file-size">{formatFileSize(file.file_size)}</span>
                  <span className="file-type">{file.mime_type}</span>
                  <span className="file-date">
                    {new Date(file.created_at).toLocaleDateString()}
                  </span>
                </div>
                {file.description && (
                  <div className="file-description">{file.description}</div>
                )}
              </div>
              
              <div className="file-actions">
                <button
                  className="btn-primary btn-download"
                  onClick={() => downloadFile(file.id, file.original_filename)}
                  title="Download file"
                >
                  <FiDownload />
                  Download
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Feature Description */}
      <div style={{ 
        marginTop: '30px',
        padding: '20px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h3>Feature Description</h3>
        <ul style={{ lineHeight: '1.6' }}>
          <li>This section only appears for requests with <code>request_type = 'adhoc-data'</code></li>
          <li>Files are fetched using the <code>FILES.GET_BY_REQUEST</code> API endpoint</li>
          <li>Each file shows its original filename, size, type, and upload date</li>
          <li>Individual download buttons for each file</li>
          <li>Bulk "Download All" functionality</li>
          <li>Different file type icons based on MIME type</li>
          <li>Responsive design with hover effects</li>
          <li>Loading, error, and empty states handled gracefully</li>
        </ul>
      </div>
    </div>
  );
};

export default DataFilesDemo;
