import React from "react";
import { useNavigate } from "react-router-dom";
import "../../styles/CreateRequest.css";
import { FiDatabase, FiLayers, FiFileText, FiBarChart2 } from "react-icons/fi";

const CreateRequest = () => {
  const navigate = useNavigate();

  const disabledTypes = ["access-database"];

  // Define request types
  const requestTypes = [
    {
      id: "access-platform",
      name: "ZDS Platform",
      description:
        "Request access to the ZDS platform with specific permissions",
      icon: <FiLayers size={48} />,
      color: "#4285F4",
      route: "/requests/platform-select",
    },
    {
      id: "access-report",
      name: "Atlas Report",
      description: "Request access to view or edit Atlas reports",
      icon: <FiBarChart2 size={48} />,
      color: "#EA4335",
      route: "/requests/report",
    },
    {
      id: "adhoc-data",
      name: "Adhoc Data",
      description: "Request custom data extraction or analysis",
      icon: <FiFileText size={48} />,
      color: "#34A853",
      route: "/requests/adhoc",
    },
    {
      id: "access-database",
      name: "Database",
      description: "Request access to database resources",
      icon: <FiDatabase size={48} />,
      color: "#FBBC05",
      route: "/requests/database",
    },
  ];

  // Navigate to request form with selected type
  const handleSelectType = (typeId, routePath) => {
    // Check if platform is temporarily disabled
    if (disabledTypes.includes(typeId)) {
      return; // Do nothing for disabled platforms
    }

    navigate(routePath);
  };

  return (
    <div className="create-request-container">
      <div className="create-request-header">
        <h2>Select Request Type</h2>
        <p>Choose the type of request you want to create</p>
      </div>

      <div className="request-type-grid">
        {requestTypes.map((type) => {
          const isDisabled = disabledTypes.includes(type.id);
          return (
          <div
            key={type.id}
            className={`request-type-card ${isDisabled ? 'request-type-card-disabled' : ''}`}
            onClick={() => handleSelectType(type.id, type.route)}
            style={{
              cursor: isDisabled ? 'not-allowed' : 'pointer',
              opacity: isDisabled ? 0.5 : 1
            }}
          >
            <div
              className="request-type-icon"
              style={{ backgroundColor: `${type.color}20` }}
            >
              <div className="icon-container" style={{ color: type.color }}>
                {type.icon}
              </div>
            </div>
            <div className="request-type-content">
              <h3>{type.name}</h3>
              <p>{type.description}</p>
              {isDisabled && (
                <div className="request-type-disabled-notice">
                  <small style={{ color: '#999', fontStyle: 'italic' }}>
                    Temporarily unavailable
                  </small>
                </div>
              )}
            </div>
          </div>
          );
        })}
      </div>
      <div className="platform-actions">
        <button className="btn-secondary" onClick={() => navigate("/")}>
          Back
        </button>
      </div>
    </div>
  );
};

export default CreateRequest;
