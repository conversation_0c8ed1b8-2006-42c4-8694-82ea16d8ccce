import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import apiService from "../../services/api.service";
import { ENDPOINTS } from "../../config/api.config";
import { FiAlertCircle } from "react-icons/fi";
import "../../styles/CreateRequest.css";
import "../../styles/RequestModal.css";

const RequestPlatform = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [users, setUsers] = useState([]);
  const [approverOptions, setApproverOptions] = useState([]);
  const [executorOptions, setExecutorOptions] = useState([]);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [createdRequest, setCreatedRequest] = useState(null);

  // Get platform info from location state
  const platformId = location.state?.platformId || "";
  const platformName = location.state?.platformName || "";

  // Platform options
  const platformOptions = [
    { value: "whitelist", label: "Whitelist Tool" },
    { value: "funnel", label: "Funnel Tool" },
    { value: "ads", label: "Ads Report" },
    { value: "zdslab", label: "ZDSLab" },
    { value: "sbv", label: "SBV Report" },
    { value: "ecommerce", label: "Ecommerce" },
    { value: "configtool", label: "Config Tool" },
  ];

  const [formData, setFormData] = useState({
    title: platformName ? `Request access to ${platformName}` : "",
    request_type: "access-platform",
    description: null,
    platform_name: platformId || null,
    due_date: null,
    approver_id: null,
    executor_id: null,
  });

  // Fetch users for dropdown selections (executor only)
  // useEffect(() => {
  //   const fetchUsers = async () => {
  //     try {
  //       const response = await apiService.get(
  //         ENDPOINTS.USERS.GET_ALL("dataquest")
  //       );
  //       if (response && Array.isArray(response.users)) {
  //         setUsers(response.users);
  //       }
  //     } catch (err) {
  //       console.error("Failed to fetch users:", err);
  //       setError("Failed to load user data");
  //     }
  //   };

  //   fetchUsers();
  // }, []);

  // Fetch approvers & executors for dropdowns based on platform
  useEffect(() => {
    const fetchApproversExecutors = async () => {
      try {
        const response = await apiService.get(ENDPOINTS.CONFIG.GET);
        const platformValue = formData.platform_name || platformId;
        // Approvers
        const approvers = (response || [])
          .filter(
            (item) =>
              item.request_type === "access-platform" &&
              item.platform === platformValue &&
              item.role === "approver"
          )
          .map((item) => ({
            user_id: item.user_id,
            display_name: item.display_name,
          }));
        setApproverOptions(approvers);
        // Set default approver if not set
        if (approvers.length > 0 && !formData.approver_id) {
          setFormData((prev) => ({
            ...prev,
            approver_id: approvers[0].user_id,
          }));
        }
        // Executors
        const executors = (response || [])
          .filter(
            (item) =>
              item.request_type === "access-platform" &&
              item.platform === platformValue &&
              item.role === "executor"
          )
          .map((item) => ({
            user_id: item.user_id,
            display_name: item.display_name,
          }));
        setExecutorOptions(executors);
        // Set default executor if not set
        if (executors.length > 0 && !formData.executor_id) {
          setFormData((prev) => ({
            ...prev,
            executor_id: executors[0].user_id,
          }));
        }
      } catch (err) {
        setApproverOptions([]);
        setExecutorOptions([]);
      }
    };
    fetchApproversExecutors();
    // eslint-disable-next-line
  }, [formData.platform_name, platformId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Format the data before submission
      const requestData = {
        ...formData,
      };

      // Add approver_id if provided
      if (formData.approver_id) {
        requestData.approver_id = parseInt(formData.approver_id, 10);
      }

      // Add executor_id if provided
      if (formData.executor_id) {
        requestData.executor_id = parseInt(formData.executor_id, 10);
      }

      // If due_date is empty, don't send it to avoid validation errors
      if (!requestData.due_date) {
        delete requestData.due_date;
      }

      const response = await apiService.post(
        ENDPOINTS.REQUEST.CREATE,
        requestData
      );
      setCreatedRequest(response);
      setShowSuccessModal(true);
    } catch (err) {
      console.error("Error creating request:", err);
      if (err.response && err.response.data) {
        // Format and display validation errors
        const errorDetails = err.response.data.detail;
        if (Array.isArray(errorDetails)) {
          setError(
            errorDetails
              .map((detail) => `${detail.msg} for ${detail.loc[1]}`)
              .join(", ")
          );
        } else {
          setError(err.response.data.message || "Failed to create request");
        }
      } else {
        setError("An unexpected error occurred");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="request-page-container">
      <div className="request-form-container">
        <h2>Request Access to {platformName}</h2>

        {error && (
          <div className="error-message">
            <FiAlertCircle />
            <span>{error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="title">Title*</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
            />
          </div>

          {/* Hidden platform field */}
          <input
            type="hidden"
            name="platform_name"
            value={formData.platform_name}
          />

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={6}
              style={{ maxWidth: "100%", minWidth: "100%" }}
              placeholder="Additional context or information about your request"
            />
          </div>

          <div className="form-group">
            <label htmlFor="due_date">Due Date (optional)</label>
            <input
              type="date"
              id="due_date"
              name="due_date"
              value={formData.due_date || ""}
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label htmlFor="approver_id">Approver (optional)</label>
            <select
              id="approver_id"
              name="approver_id"
              value={formData.approver_id}
              onChange={handleChange}
            >
              <option value="">Select an Approver</option>
              {approverOptions.map((user, idx) => (
                <option
                  key={`approver-${user.user_id}-${idx}`}
                  value={user.user_id}
                >
                  {user.display_name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="executor_id">Executor (optional)</label>
            <select
              id="executor_id"
              name="executor_id"
              value={formData.executor_id}
              onChange={handleChange}
            >
              <option value="">Select an Executor</option>
              {executorOptions.map((user, idx) => (
                <option
                  key={`executor-${user.user_id}-${idx}`}
                  value={user.user_id}
                >
                  {user.display_name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="btn-secondary"
              onClick={() => navigate("/requests/platform-select")}
            >
              Back
            </button>
            <button type="submit" className="btn-primary" disabled={loading}>
              {loading ? "Creating..." : "Create Request"}
            </button>
          </div>
        </form>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="request-success-modal">
          <div className="modal-overlay">
            <div className="modal-container">
              <div className="modal-header">
                <h3>Request Created Successfully</h3>
              </div>
              <div className="modal-body">
                <div className="success-message">
                  <p>
                    Your request has been created successfully. Here are the
                    details:
                  </p>
                </div>

                <div className="request-details">
                  <div className="detail-row">
                    <span className="detail-label">Request ID:</span>
                    <span className="detail-value">{createdRequest?.id}</span>
                  </div>

                  <div className="detail-row">
                    <span className="detail-label">Title:</span>
                    <span className="detail-value">
                      {createdRequest?.title}
                    </span>
                  </div>

                  <div className="detail-row">
                    <span className="detail-label">Platform:</span>
                    <span className="detail-value">
                      {createdRequest?.platform_name}
                    </span>
                  </div>

                  <div className="detail-row">
                    <span className="detail-label">Request Type:</span>
                    <span className="detail-value">
                      {createdRequest?.request_type}
                    </span>
                  </div>

                  <div className="detail-row">
                    <span className="detail-label">Description:</span>
                    <span className="detail-value">
                      {createdRequest?.description || "No description provided"}
                    </span>
                  </div>

                  <div className="detail-row">
                    <span className="detail-label">Due Date:</span>
                    <span className="detail-value">
                      {createdRequest?.due_date || "Not specified"}
                    </span>
                  </div>

                  <div className="detail-row">
                    <span className="detail-label">Status:</span>
                    <span className="detail-value">
                      <span
                        className={`status-badge ${
                          createdRequest?.status?.toLowerCase() || "pending"
                        }`}
                      >
                        {createdRequest?.status || "Pending"}
                      </span>
                    </span>
                  </div>

                  <div className="detail-row">
                    <span className="detail-label">Created At:</span>
                    <span className="detail-value">
                      {new Date(createdRequest?.created_at).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
              <div className="modal-actions request-modal-actions">
                <button
                  className="btn-primary"
                  onClick={() => {
                    setShowSuccessModal(false);
                    navigate(`/requests/detail?id=${createdRequest?.id}`);
                  }}
                >
                  Request Tracking
                </button>
                <button
                  className="btn-success"
                  onClick={() => {
                    setShowSuccessModal(false);
                    navigate("/requests/types");
                  }}
                >
                  OK
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RequestPlatform;
