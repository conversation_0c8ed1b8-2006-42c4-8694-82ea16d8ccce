import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  FiArrowLeft,
  FiClock,
  FiUser,
  FiCalendar,
  FiCheckCircle,
  FiXCircle,
  FiLoader,
  FiInfo,
  FiUsers,
  FiActivity,
  FiBarChart2,
  FiMessageSquare,
  FiSend,
  FiChevronRight,
  FiEdit2,
  FiTrash2,
  FiExternalLink,
  FiGrid,
  FiLayers,
  FiBook,
  FiUpload,
  FiFile,
  FiX,
  FiRefreshCw,
  FiDownload,
  FiFolder,
} from "react-icons/fi";
import apiService from "../../services/api.service";
import { ENDPOINTS } from "../../config/api.config";
import "../../styles/RequestDetail.css";

// Component for rendering report needed JSON data as table
const ReportNeededTable = ({ reportNeeded }) => {
  // Try to parse JSON data
  let parsedData = null;
  try {
    parsedData = JSON.parse(reportNeeded);
  } catch (error) {
    // If parsing fails, display as HTML (fallback for old data)
    return (
      <div
        className="info-value"
        dangerouslySetInnerHTML={{
          __html: reportNeeded,
        }}
      />
    );
  }

  // If parsing succeeds, render as table
  if (parsedData && parsedData.items_by_owner) {
    // Flatten all views and workbooks from all owners
    const allViews = [];
    const allWorkbooks = [];

    Object.entries(parsedData.items_by_owner).forEach(
      ([ownerName, ownerData]) => {
        if (ownerData.views) {
          ownerData.views.forEach((view) => {
            allViews.push({ ...view, owner: ownerName });
          });
        }
        if (ownerData.workbooks) {
          ownerData.workbooks.forEach((workbook) => {
            allWorkbooks.push({ ...workbook, owner: ownerName });
          });
        }
      }
    );

    return (
      <div className="report-needed-table">
        {/* Summary section */}
        {parsedData.summary && (
          <div className="report-summary">
            <div className="summary-stats">
              <div className="summary-stat">
                <span className="stat-number">
                  {parsedData.summary.total_items}
                </span>
                <span className="stat-label">Total Items</span>
              </div>
              <div className="summary-stat">
                <span className="stat-number">
                  {parsedData.summary.total_views}
                </span>
                <span className="stat-label">Views</span>
              </div>
              <div className="summary-stat">
                <span className="stat-number">
                  {parsedData.summary.total_workbooks}
                </span>
                <span className="stat-label">Workbooks</span>
              </div>
              <div className="summary-stat">
                <span className="stat-number">
                  {parsedData.summary.total_owners}
                </span>
                <span className="stat-label">Owners</span>
              </div>
              <div className="summary-stat">
                <span className="stat-number">
                  {parsedData.summary.total_completed}
                </span>
                <span className="stat-label">Completed</span>
              </div>
            </div>
          </div>
        )}

        {/* Views table */}
        {allViews.length > 0 && (
          <div className="items-table-section">
            <div className="table-header">
              <div className="table-header-left">
                <FiGrid className="table-icon" />
                <span className="table-title">Views ({allViews.length})</span>
              </div>
            </div>
            <div className="items-table">
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Owner</th>
                    <th>Link</th>
                    <th>Completed</th>
                  </tr>
                </thead>
                <tbody>
                  {allViews.map((view) => (
                    <tr key={`${view.owner}-${view.id}`}>
                      <td className="item-name">{view.name}</td>
                      <td className="item-owner">{view.owner}</td>
                      <td className="item-link">
                        <a
                          href={`https://atlas.vng.com.vn/#/site/ZLPDataServices/workbooks/${view.workbook_id}/views`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="atlas-link"
                        >
                          <FiExternalLink />
                          View in Atlas
                        </a>
                      </td>
                      <td className="item-completed">
                        <input
                          type="checkbox"
                          className="completed-checkbox"
                          defaultChecked={view.completed}
                          style={{ pointerEvents: "none" }}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Workbooks table */}
        {allWorkbooks.length > 0 && (
          <div className="items-table-section">
            <div className="table-header">
              <div className="table-header-left">
                <FiLayers className="table-icon" />
                <span className="table-title">
                  Workbooks ({allWorkbooks.length})
                </span>
              </div>
            </div>
            <div className="items-table">
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Owner</th>
                    <th>Link</th>
                    <th>Completed</th>
                  </tr>
                </thead>
                <tbody>
                  {allWorkbooks.map((workbook) => (
                    <tr key={`${workbook.owner}-${workbook.id}`}>
                      <td className="item-name">{workbook.name}</td>
                      <td className="item-owner">{workbook.owner}</td>
                      <td className="item-link">
                        <a
                          href={`https://atlas.vng.com.vn/#/site/ZLPDataServices/workbooks/${workbook.id}/views`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="atlas-link"
                        >
                          <FiExternalLink />
                          View in Atlas
                        </a>
                      </td>
                      <td className="item-completed">
                        <input
                          type="checkbox"
                          className="completed-checkbox"
                          defaultChecked={workbook.completed}
                          style={{ pointerEvents: "none" }}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Fallback for unexpected data structure
  return (
    <div className="info-value">
      <pre>{JSON.stringify(parsedData, null, 2)}</pre>
    </div>
  );
};

// Component for rendering report needed JSON data as table in Process Modal (with enabled checkboxes)
const ProcessModalReportTable = ({
  reportNeeded,
  checkedItems,
  onCheckboxChange,
  onMasterCheckboxChange,
  getMasterCheckboxState,
}) => {
  // Try to parse JSON data
  let parsedData = null;
  try {
    parsedData = JSON.parse(reportNeeded);
  } catch (error) {
    // If parsing fails, return null (don't show anything)
    return null;
  }

  // If parsing succeeds, render as table
  if (parsedData && parsedData.items_by_owner) {
    // Flatten all views and workbooks from all owners
    const allViews = [];
    const allWorkbooks = [];

    Object.entries(parsedData.items_by_owner).forEach(
      ([ownerName, ownerData]) => {
        if (ownerData.views) {
          ownerData.views.forEach((view) => {
            // Only add views that are not completed
            if (!view.completed) {
              allViews.push({ ...view, owner: ownerName });
            }
          });
        }
        if (ownerData.workbooks) {
          ownerData.workbooks.forEach((workbook) => {
            // Only add workbooks that are not completed
            if (!workbook.completed) {
              allWorkbooks.push({ ...workbook, owner: ownerName });
            }
          });
        }
      }
    );

    // If no pending items, show completion message
    if (allViews.length === 0 && allWorkbooks.length === 0) {
      return (
        <div className="process-modal-report-table">
          <div className="no-pending-items">
            <FiCheckCircle className="completion-icon" />
            <h3>All Reports Completed</h3>
            <p>
              All requested reports have been marked as completed. No further
              action required.
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="process-modal-report-table">
        {/* Summary section - showing only incomplete items */}
        {/* <div className="report-summary">
          <div className="summary-stats">
            <div className="summary-stat">
              <span className="stat-number">{allViews.length + allWorkbooks.length}</span>
              <span className="stat-label">Pending Items</span>
            </div>
            <div className="summary-stat">
              <span className="stat-number">{allViews.length}</span>
              <span className="stat-label">Pending Views</span>
            </div>
            <div className="summary-stat">
              <span className="stat-number">{allWorkbooks.length}</span>
              <span className="stat-label">Pending Workbooks</span>
            </div>
            <div className="summary-stat">
              <span className="stat-number">
                {new Set([...allViews.map(v => v.owner), ...allWorkbooks.map(w => w.owner)]).size}
              </span>
              <span className="stat-label">Owners</span>
            </div>
          </div>
        </div> */}

        {/* Views table */}
        {allViews.length > 0 && (
          <div className="items-table-section">
            <div className="table-header">
              <div className="table-header-left">
                <FiGrid className="table-icon" />
                <span className="table-title">Views ({allViews.length})</span>
              </div>
            </div>
            <div className="items-table">
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Owner</th>
                    <th>Link</th>
                    <th>
                      <label className="table-header-checkbox">
                        <input
                          type="checkbox"
                          checked={
                            getMasterCheckboxState(allViews, "view").checked
                          }
                          ref={(el) => {
                            if (el) {
                              el.indeterminate = getMasterCheckboxState(
                                allViews,
                                "view"
                              ).indeterminate;
                            }
                          }}
                          onChange={(e) =>
                            onMasterCheckboxChange(
                              allViews,
                              "view",
                              e.target.checked
                            )
                          }
                        />
                        Completed
                      </label>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {allViews.map((view) => (
                    <tr key={`${view.owner}-${view.id}`}>
                      <td className="item-name">{view.name}</td>
                      <td className="item-owner">{view.owner}</td>
                      <td className="item-link">
                        <a
                          href={`https://atlas.vng.com.vn/#/site/ZLPDataServices/workbooks/${view.workbook_id}/views`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="atlas-link"
                        >
                          <FiExternalLink />
                          View in Atlas
                        </a>
                      </td>
                      <td className="item-completed">
                        <input
                          type="checkbox"
                          className="completed-checkbox"
                          checked={checkedItems[`view-${view.id}`] || false}
                          onChange={(e) =>
                            onCheckboxChange(view.id, "view", e.target.checked)
                          }
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Workbooks table */}
        {allWorkbooks.length > 0 && (
          <div className="items-table-section">
            <div className="table-header">
              <div className="table-header-left">
                <FiLayers className="table-icon" />
                <span className="table-title">
                  Workbooks ({allWorkbooks.length})
                </span>
              </div>
            </div>
            <div className="items-table">
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Owner</th>
                    <th>Link</th>
                    <th>
                      <label className="table-header-checkbox">
                        <input
                          type="checkbox"
                          checked={
                            getMasterCheckboxState(allWorkbooks, "workbook")
                              .checked
                          }
                          ref={(el) => {
                            if (el) {
                              el.indeterminate = getMasterCheckboxState(
                                allWorkbooks,
                                "workbook"
                              ).indeterminate;
                            }
                          }}
                          onChange={(e) =>
                            onMasterCheckboxChange(
                              allWorkbooks,
                              "workbook",
                              e.target.checked
                            )
                          }
                        />
                        Completed
                      </label>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {allWorkbooks.map((workbook) => (
                    <tr key={`${workbook.owner}-${workbook.id}`}>
                      <td className="item-name">{workbook.name}</td>
                      <td className="item-owner">{workbook.owner}</td>
                      <td className="item-link">
                        <a
                          href={`https://atlas.vng.com.vn/#/site/ZLPDataServices/workbooks/${workbook.id}/views`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="atlas-link"
                        >
                          <FiExternalLink />
                          View in Atlas
                        </a>
                      </td>
                      <td className="item-completed">
                        <input
                          type="checkbox"
                          className="completed-checkbox"
                          checked={
                            checkedItems[`workbook-${workbook.id}`] || false
                          }
                          onChange={(e) =>
                            onCheckboxChange(
                              workbook.id,
                              "workbook",
                              e.target.checked
                            )
                          }
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Fallback for unexpected data structure
  return null;
};

// Component for handling file uploads in adhoc-data request processing
const AdhocDataUploadForm = ({
  uploadedFiles,
  setUploadedFiles,
  uploadProgress,
  setUploadProgress,
  uploadErrors,
  setUploadErrors,
  requestId
}) => {
  const fileInputRef = useRef(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isDataRestored, setIsDataRestored] = useState(false);

  // Generate unique storage key for this request
  const storageKey = `adhoc_upload_${requestId}`;

  // Clean up old localStorage data (older than 24 hours)
  const cleanupOldStorageData = () => {
    const now = Date.now();
    const oneDayInMs = 24 * 60 * 60 * 1000; // 24 hours

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('adhoc_upload_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key));
          if (data.timestamp && (now - data.timestamp) > oneDayInMs) {
            localStorage.removeItem(key);
          }
        } catch (error) {
          // Remove corrupted data
          localStorage.removeItem(key);
        }
      }
    }
  };

  // Load data from localStorage on component mount
  useEffect(() => {
    // Clean up old data first
    cleanupOldStorageData();

    const savedData = localStorage.getItem(storageKey);
    if (savedData) {
      try {
        const { files, errors } = JSON.parse(savedData);

        // Only restore uploaded files (not uploading ones)
        const uploadedFilesOnly = files.filter(file => file.status === 'uploaded');
        if (uploadedFilesOnly.length > 0) {
          setUploadedFiles(uploadedFilesOnly);
          setIsDataRestored(true);

          // Auto-hide the restored message after 5 seconds
          setTimeout(() => {
            setIsDataRestored(false);
          }, 5000);
        }

        // Restore errors
        if (errors && Object.keys(errors).length > 0) {
          setUploadErrors(errors);
        }
      } catch (error) {
        console.error('Error loading saved upload data:', error);
        // Clear corrupted data
        localStorage.removeItem(storageKey);
      }
    }
  }, [requestId, storageKey, setUploadedFiles, setUploadErrors]);

  // Save data to localStorage whenever uploadedFiles or uploadErrors change
  useEffect(() => {
    if (requestId) {
      const dataToSave = {
        files: uploadedFiles,
        errors: uploadErrors,
        timestamp: Date.now()
      };
      localStorage.setItem(storageKey, JSON.stringify(dataToSave));
    }
  }, [uploadedFiles, uploadErrors, requestId, storageKey]);

  // Clear localStorage data when component unmounts or request is completed
  const clearStoredData = () => {
    localStorage.removeItem(storageKey);
  };

  // File validation function
  const validateFile = (file) => {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/json',
      'text/plain',
      'application/zip',
      'application/x-zip-compressed'
    ];

    if (file.size > maxSize) {
      return 'File size must be less than 50MB';
    }

    if (!allowedTypes.includes(file.type)) {
      return 'File type not supported. Please upload CSV, Excel, JSON, TXT, or ZIP files.';
    }

    return null;
  };

  // Check if file already exists (by name and size)
  const isDuplicateFile = (file) => {
    return uploadedFiles.some(existingFile =>
      existingFile.name === file.name &&
      existingFile.size === file.size &&
      existingFile.status !== 'error'
    );
  };

  // Handle file selection (both click and drag & drop)
  const handleFiles = async (files) => {
    const fileArray = Array.from(files);
    const validFiles = [];
    const errors = {};

    // Validate each file and check for duplicates
    for (const file of fileArray) {
      const validationError = validateFile(file);
      if (validationError) {
        errors[file.name] = validationError;
        continue;
      }

      if (isDuplicateFile(file)) {
        errors[file.name] = 'This file has already been uploaded';
        continue;
      }

      validFiles.push(file);
    }

    // Update errors
    if (Object.keys(errors).length > 0) {
      setUploadErrors(prev => ({ ...prev, ...errors }));
    }

    // If no valid files, return early
    if (validFiles.length === 0) {
      return;
    }

    // Clear errors for files that are being uploaded successfully
    const successfulFileNames = validFiles.map(f => f.name);
    if (successfulFileNames.length > 0) {
      setUploadErrors(prev => {
        const newErrors = { ...prev };
        successfulFileNames.forEach(fileName => {
          delete newErrors[fileName];
        });
        return newErrors;
      });
    }

    // Create file objects and start uploading
    const newFiles = validFiles.map((file) => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
      status: 'uploading',
      uploadedFileId: null,
      errorMessage: null,
    }));

    setUploadedFiles((prev) => [...prev, ...newFiles]);

    // Upload each file immediately
    newFiles.forEach((fileObj) => {
      uploadSingleFile(fileObj);
    });
  };

  // Handle file selection from input
  const handleFileSelect = async (e) => {
    await handleFiles(e.target.files);
    // Clear the input
    e.target.value = '';
  };

  // Drag and drop handlers
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    await handleFiles(files);
  };

  // Upload single file
  const uploadSingleFile = async (fileObj) => {
    try {
      const formData = new FormData();
      formData.append('file', fileObj.file);
      formData.append('description', `Adhoc data file: ${fileObj.name}`);
      if (requestId) {
        formData.append('request_id', requestId);
      }

      const response = await apiService.postFormData(
        ENDPOINTS.FILES.UPLOAD,
        formData,
        (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(prev => ({
            ...prev,
            [fileObj.id]: percentCompleted
          }));
        }
      );

      // Update status to uploaded
      setUploadedFiles(prev => prev.map(file =>
        file.id === fileObj.id
          ? {
              ...file,
              status: 'uploaded',
              uploadedFileId: response.file_id || response.id,
              errorMessage: null
            }
          : file
      ));

      // Clear progress
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileObj.id];
        return newProgress;
      });

    } catch (error) {
      console.error(`Error uploading file ${fileObj.name}:`, error);

      const errorMessage = error.response?.data?.message || error.message || 'Upload failed';
      setUploadedFiles(prev => prev.map(file =>
        file.id === fileObj.id
          ? {
              ...file,
              status: 'error',
              errorMessage: errorMessage
            }
          : file
      ));

      setUploadErrors(prev => ({
        ...prev,
        [fileObj.name]: errorMessage
      }));
    }
  };

  // Remove file
  const removeFile = async (fileId, fileName) => {
    const fileToRemove = uploadedFiles.find(f => f.id === fileId);

    // If file was uploaded, delete it from server
    if (fileToRemove && fileToRemove.uploadedFileId) {
      try {
        await apiService.delete(ENDPOINTS.FILES.DELETE(fileToRemove.uploadedFileId));
      } catch (error) {
        console.error('Error deleting file from server:', error);
      }
    }

    // Remove from UI
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));

    // Clear any errors
    setUploadErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fileName];
      return newErrors;
    });
  };

  // Retry upload
  const retryUpload = (fileObj) => {
    setUploadedFiles(prev => prev.map(file =>
      file.id === fileObj.id
        ? { ...file, status: 'uploading', errorMessage: null }
        : file
    ));

    setUploadErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fileObj.name];
      return newErrors;
    });

    uploadSingleFile(fileObj);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Expose clearStoredData function to parent component
  useEffect(() => {
    // Store the clear function in a way that parent can access it
    window.clearAdhocUploadData = clearStoredData;

    // Cleanup on unmount
    return () => {
      delete window.clearAdhocUploadData;
    };
  }, [clearStoredData]);

  return (
    <div className="adhoc-upload-form">
      <div className="upload-header">
        <h3>Upload Data Files</h3>
        <p>Please upload the data files that need to be processed for this adhoc data request.</p>

        {/* Data restored notification */}
        {isDataRestored && (
          <div className="data-restored-notification">
            <FiCheckCircle />
            <span>Previously uploaded files have been restored from your last session.</span>
          </div>
        )}
      </div>

      {/* File Upload Area */}
      <div className="upload-area">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileSelect}
          style={{ display: 'none' }}
          accept=".csv,.xlsx,.xls,.json,.txt,.zip"
        />

        <div
          className={`upload-dropzone ${isDragOver ? 'drag-over' : ''}`}
          onClick={() => fileInputRef.current?.click()}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <FiUpload className="upload-icon" />
          <div className="upload-text">
            <p><strong>Click to upload</strong> or drag and drop</p>
            <p className="upload-hint">CSV, Excel, JSON, TXT, ZIP files (max 50MB each)</p>
          </div>
        </div>
      </div>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="uploaded-files-list">
          <h4>Uploaded Files ({uploadedFiles.length})</h4>
          {uploadedFiles.map((file) => (
            <div key={file.id} className={`file-item ${file.status}`}>
              <div className="file-info">
                <FiFile className="file-icon" />
                <div className="file-details">
                  <div className="file-name">{file.name}</div>
                  <div className="file-size">{formatFileSize(file.size)}</div>
                </div>
              </div>

              <div className="file-status">
                {file.status === 'uploading' && (
                  <div className="upload-progress">
                    <FiLoader className="spin" />
                    <span>{uploadProgress[file.id] || 0}%</span>
                  </div>
                )}

                {file.status === 'uploaded' && (
                  <div className="upload-success">
                    <FiCheckCircle />
                    <span>Uploaded</span>
                  </div>
                )}

                {file.status === 'error' && (
                  <div className="upload-error">
                    <FiXCircle />
                    <span>Failed</span>
                    <button
                      className="retry-btn"
                      onClick={() => retryUpload(file)}
                      title="Retry upload"
                    >
                      <FiRefreshCw />
                    </button>
                  </div>
                )}
              </div>

              <button
                className="remove-file-btn"
                onClick={() => removeFile(file.id, file.name)}
                disabled={file.status === 'uploading'}
                title="Remove file"
              >
                <FiX />
              </button>

              {file.errorMessage && (
                <div className="file-error-message">
                  {file.errorMessage}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Upload Errors */}
      {Object.keys(uploadErrors).length > 0 && (
        <div className="upload-errors">
          <h4>Upload Issues</h4>
          {Object.entries(uploadErrors).map(([fileName, error]) => (
            <div key={fileName} className="error-message">
              <FiXCircle />
              <span><strong>{fileName}:</strong> {error}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Recursive component for rendering nested replies
const RenderReplies = ({
  replies,
  editingCommentId,
  setEditingCommentId,
  replyingToId,
  setReplyingToId,
  handleEditComment,
  handleDeleteComment,
  handleSubmitReply,
  editTextareaRef,
  replyTextareaRef,
  autoResizeTextarea,
  formatDate,
}) => {
  return (
    <div className="comment-replies">
      {replies.map((reply) => (
        <div
          key={reply.id}
          className="reply-item"
          data-level={reply.level || 1}
        >
          <div className="reply-avatar">
            <FiUser />
          </div>
          <div className="reply-content">
            <div className="reply-header">
              <div className="reply-author">User {reply.userId}</div>
              <div className="reply-meta">
                <div className="reply-time">
                  {formatDate(reply.timestamp)}
                  {reply.edited && (
                    <span className="reply-edited"> (edited)</span>
                  )}
                </div>
                {reply.userId === "current" && (
                  <div className="reply-actions">
                    <button
                      className="comment-action-btn"
                      onClick={() => setEditingCommentId(reply.id)}
                      title="Edit reply"
                    >
                      <FiEdit2 />
                    </button>
                    <button
                      className="comment-action-btn delete"
                      onClick={() => handleDeleteComment(reply.id)}
                      title="Delete reply"
                    >
                      <FiTrash2 />
                    </button>
                  </div>
                )}
              </div>
            </div>

            {editingCommentId === reply.id ? (
              <div className="comment-edit-form">
                <textarea
                  ref={editTextareaRef}
                  className="comment-edit-input"
                  defaultValue={reply.text}
                  onChange={() => {
                    // Auto-resize the textarea after content change
                    setTimeout(
                      () => autoResizeTextarea(editTextareaRef.current),
                      0
                    );
                  }}
                  onKeyDown={(e) => {
                    if ((e.metaKey || e.ctrlKey) && e.key === "Enter") {
                      e.preventDefault();
                      handleEditComment(reply.id, e.target.value);
                    }
                  }}
                ></textarea>
                <div className="comment-edit-actions">
                  <button
                    className="btn-cancel"
                    onClick={() => setEditingCommentId(null)}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn-save"
                    onClick={() => {
                      if (editTextareaRef.current) {
                        handleEditComment(
                          reply.id,
                          editTextareaRef.current.value
                        );
                      }
                    }}
                  >
                    Save
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="reply-text">{reply.text}</div>
                {/* Show reply button only if we're not at max nesting level (3) */}
                {reply.level < 2 && (
                  <div className="comment-footer">
                    <button
                      className="comment-reply-btn"
                      onClick={() => setReplyingToId(reply.id)}
                    >
                      Reply
                    </button>
                  </div>
                )}
              </>
            )}

            {/* Reply form */}
            {replyingToId === reply.id && (
              <div className="comment-reply-form">
                <textarea
                  ref={replyTextareaRef}
                  className="comment-reply-input"
                  placeholder="Write a reply..."
                  onChange={() => {
                    // Auto-resize the textarea after content change
                    setTimeout(
                      () => autoResizeTextarea(replyTextareaRef.current),
                      0
                    );
                  }}
                  onKeyDown={(e) => {
                    if ((e.metaKey || e.ctrlKey) && e.key === "Enter") {
                      e.preventDefault();
                      handleSubmitReply(reply.id, e.target.value, reply.level);
                    }
                  }}
                ></textarea>
                <div className="comment-reply-actions">
                  <button
                    className="btn-cancel"
                    onClick={() => setReplyingToId(null)}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn-reply"
                    onClick={() => {
                      if (replyTextareaRef.current) {
                        handleSubmitReply(
                          reply.id,
                          replyTextareaRef.current.value,
                          reply.level
                        );
                      }
                    }}
                  >
                    Reply
                  </button>
                </div>
              </div>
            )}

            {/* Recursively render nested replies */}
            {reply.replies && reply.replies.length > 0 && (
              <RenderReplies
                replies={reply.replies}
                editingCommentId={editingCommentId}
                setEditingCommentId={setEditingCommentId}
                replyingToId={replyingToId}
                setReplyingToId={setReplyingToId}
                handleEditComment={handleEditComment}
                handleDeleteComment={handleDeleteComment}
                handleSubmitReply={handleSubmitReply}
                editTextareaRef={editTextareaRef}
                replyTextareaRef={replyTextareaRef}
                autoResizeTextarea={autoResizeTextarea}
                formatDate={formatDate}
              />
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

// Component for displaying data files for adhoc-data request type
const RequestDataFileSection = ({ requestId }) => {
  const [dataFiles, setDataFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch data files for the request
  useEffect(() => {
    const fetchDataFiles = async () => {
      try {
        setLoading(true);
        const response = await apiService.get(
          `${ENDPOINTS.FILES.GET_BY_REQUEST}?request_id=${requestId}`
        );
        setDataFiles(response || []);
      } catch (err) {
        console.error("Error fetching data files:", err);
        setError("Failed to load data files");
      } finally {
        setLoading(false);
      }
    };

    if (requestId) {
      fetchDataFiles();
    }
  }, [requestId]);

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Get file icon based on mime type
  const getFileIcon = (mimeType) => {
    if (mimeType.startsWith("image/")) return <FiFile style={{ color: "#4CAF50" }} />;
    if (mimeType.includes("json")) return <FiFile style={{ color: "#FF9800" }} />;
    if (mimeType.includes("csv")) return <FiFile style={{ color: "#2196F3" }} />;
    if (mimeType.includes("excel") || mimeType.includes("spreadsheet"))
      return <FiFile style={{ color: "#4CAF50" }} />;
    if (mimeType.includes("pdf")) return <FiFile style={{ color: "#F44336" }} />;
    return <FiFile style={{ color: "#757575" }} />;
  };

  // Download individual file
  const downloadFile = async (fileId, filename) => {
    try {
      const response = await apiService.get(ENDPOINTS.FILES.DOWNLOAD(fileId), {
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      alert('Failed to download file. Please try again.');
    }
  };

  // Download all files
  const downloadAllFiles = async () => {
    if (dataFiles.length === 0) return;

    try {
      // Download files one by one
      for (const file of dataFiles) {
        await downloadFile(file.id, file.original_filename);
        // Add small delay between downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Error downloading all files:', error);
      alert('Failed to download some files. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="request-detail-section request-data-file-section">
        <h2>
          <FiFolder /> Data Files
        </h2>
        <div className="loading-state">
          <FiLoader className="spin" />
          <span>Loading data files...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="request-detail-section request-data-file-section">
        <h2>
          <FiFolder /> Data Files
        </h2>
        <div className="error-state">
          <FiXCircle />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  if (dataFiles.length === 0) {
    return (
      <div className="request-detail-section request-data-file-section">
        <h2>
          <FiFolder /> Data Files
        </h2>
        <div className="empty-state">
          <FiFile />
          <span>No data files uploaded for this request</span>
        </div>
      </div>
    );
  }

  return (
    <div className="request-detail-section request-data-file-section">
      <div className="section-header">
        <h2>
          <FiFolder /> Data Files ({dataFiles.length})
        </h2>
        <button
          className="btn-download"
          onClick={downloadAllFiles}
          title="Download all files"
        >
          <FiDownload />
          Download All
        </button>
      </div>

      <div className="data-files-list">
        {dataFiles.map((file) => (
          <div key={file.id} className="data-file-item">
            <div className="file-icon">
              {getFileIcon(file.mime_type)}
            </div>

            <div className="file-info">
              <div className="file-name" title={file.original_filename}>
                {file.original_filename}
              </div>
              <div className="file-meta">
                <span className="file-size">{formatFileSize(file.file_size)}</span>
                <span className="file-type">{file.mime_type}</span>
                <span className="file-date">
                  {new Date(file.created_at).toLocaleDateString()}
                </span>
              </div>
              {/* {file.description && (
                <div className="file-description">{file.description}</div>
              )} */}
            </div>

            <div className="file-actions">
              <button
                className="btn-download"
                onClick={() => downloadFile(file.id, file.original_filename)}
                title="Download file"
              >
                <FiDownload />
                Download
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const RequestDetail = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [request, setRequest] = useState(null);
  const [comment, setComment] = useState("");
  const [comments, setComments] = useState([]);
  const [users, setUsers] = useState([]);
  const [editingCommentId, setEditingCommentId] = useState(null);
  const [replyingToId, setReplyingToId] = useState(null);
  const [isProcessModalOpen, setIsProcessModalOpen] = useState(false);
  const [processingStep, setProcessingStep] = useState(0);
  const [processingComplete, setProcessingComplete] = useState(false);
  const [processingError, setProcessingError] = useState(null);
  const [processedUsername, setProcessedUsername] = useState(null);
  const [checkedItems, setCheckedItems] = useState({});
  const [currentUser, setCurrentUser] = useState(null);
  const [isGuideModalOpen, setIsGuideModalOpen] = useState(false);
  // File upload states for adhoc-data requests
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadErrors, setUploadErrors] = useState({});
  const requestIdRef = useRef(null);
  const textareaRef = useRef(null);
  const editTextareaRef = useRef(null);
  const replyTextareaRef = useRef(null);

  // Get current user from localStorage
  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      const parsedUser = JSON.parse(userData);
      setCurrentUser(parsedUser);
    }
  }, []);

  // Function to check if current user can perform actions on the request
  const canPerformActions = () => {
    if (!currentUser || !request) return false;

    const status = request["request_base"].status;
    const reviewerId = request["request_base"].reviewer_id;
    const approverId = request["request_base"].approver_id;
    const executorId = request["request_base"].executor_id;
    const executors = request["request_base"].executors;
    const currentUserId = currentUser.id;
    const currentUserRole = currentUser.role;

    // if currentUserRole === 'admin', return true
    if (currentUserRole === 'admin') return true;

    switch (status) {
      case 'pending':
        // If reviewer_id exists, enable for reviewer, else enable for approver
        if (reviewerId) {
          return currentUserId === reviewerId;
        } else {
          return currentUserId === approverId;
        }

      case 'reviewed':
        // Enable for approver
        return currentUserId === approverId;

      case 'approved':
        // Enable for executor_id or executors
        if (executorId) {
          return currentUserId === executorId;
        }
        if (executors && Array.isArray(executors))  {
          // For access-report requests, executors might be usernames, so check both id and username
          return executors.includes(currentUserId) || executors.includes(currentUser.username);
        }
        return false;

      default:
        return false;
    }
  };

  // Function to generate a unique ID for comments
  const generateCommentId = () => {
    return `comment-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  };

  // Function to get user display name by ID
  const getUserDisplayName = (userId) => {
    if (!userId || !users || users.length === 0) {
      return "Unknown";
    }

    const user = users.find((u) => u.id === userId);
    return user ? user.display_name : "Unknown";
  };

  // Function to get user display name by username
  const getUserDisplayNameByUsername = (username) => {
    if (!username || !users || users.length === 0) {
      return "Unknown";
    }

    const user = users.find((u) => u.username === username);
    return user ? user.display_name : "Unknown";
  };

  // Function to handle checkbox changes in Process Modal
  const handleCheckboxChange = (itemId, itemType, checked) => {
    setCheckedItems((prev) => ({
      ...prev,
      [`${itemType}-${itemId}`]: checked,
    }));
  };

  // Function to handle master checkbox change
  const handleMasterCheckboxChange = (items, itemType, checked) => {
    setCheckedItems((prev) => {
      const newCheckedItems = { ...prev };
      items.forEach((item) => {
        newCheckedItems[`${itemType}-${item.id}`] = checked;
      });
      return newCheckedItems;
    });
  };

  // Function to get master checkbox state (checked, unchecked, or indeterminate)
  const getMasterCheckboxState = (items, itemType) => {
    const checkedCount = items.filter(
      (item) => checkedItems[`${itemType}-${item.id}`]
    ).length;

    if (checkedCount === 0) {
      return { checked: false, indeterminate: false };
    } else if (checkedCount === items.length) {
      return { checked: true, indeterminate: false };
    } else {
      return { checked: false, indeterminate: true };
    }
  };

  // Function to check if any items are checked (for enabling Process Request button)
  const hasCheckedItems = () => {
    return Object.values(checkedItems).some((checked) => checked === true);
  };

  // Function to check if files are uploaded (for adhoc-data requests)
  const hasUploadedFiles = () => {
    return uploadedFiles.some(file => file.status === 'uploaded');
  };

  // Function to check if any files are currently uploading
  const hasUploadingFiles = () => {
    return uploadedFiles.some(file => file.status === 'uploading');
  };

  // Function to check if all items are completed for access-report requests
  const areAllItemsCompleted = () => {
    if (
      request["request_base"].request_type !== "access-report" ||
      !request["request_base"].report_needed
    ) {
      return true; // For non-access-report requests, consider as completed
    }

    try {
      const parsedData = JSON.parse(request["request_base"].report_needed);
      if (parsedData && parsedData.summary) {
        return (
          parsedData.summary.total_items === parsedData.summary.total_completed
        );
      }
    } catch (error) {
      console.error("Error parsing report_needed data:", error);
    }

    return false; // Default to not completed if we can't determine
  };

  // Function to handle comment submission
  const handleSubmitComment = () => {
    if (comment.trim()) {
      const newComment = {
        id: generateCommentId(),
        userId: "current", // In a real app, this would be the current user's ID
        text: comment, // The raw text with formatting is preserved
        timestamp: new Date().toISOString(),
        replies: [],
        level: 0, // Top-level comment
      };
      setComments([...comments, newComment]);
      setComment("");

      // Reset textarea height after submission
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
      }
    }
  };

  // Function to handle comment editing
  const handleEditComment = (commentId, newText) => {
    if (newText.trim()) {
      setComments(
        comments.map((comment) => {
          if (comment.id === commentId) {
            return { ...comment, text: newText, edited: true };
          }

          // Check if it's a reply within any comment
          if (comment.replies && comment.replies.length > 0) {
            const updatedReplies = comment.replies.map((reply) => {
              if (reply.id === commentId) {
                return { ...reply, text: newText, edited: true };
              }
              return reply;
            });

            return { ...comment, replies: updatedReplies };
          }

          return comment;
        })
      );
      setEditingCommentId(null);
    }
  };

  // Function to handle comment deletion
  const handleDeleteComment = (commentId) => {
    // First check if it's a top-level comment
    const isTopLevel = comments.some((comment) => comment.id === commentId);

    if (isTopLevel) {
      setComments(comments.filter((comment) => comment.id !== commentId));
    } else {
      // It must be a reply, so find the parent comment and remove the reply
      setComments(
        comments.map((comment) => {
          if (
            comment.replies &&
            comment.replies.some((reply) => reply.id === commentId)
          ) {
            return {
              ...comment,
              replies: comment.replies.filter(
                (reply) => reply.id !== commentId
              ),
            };
          }
          return comment;
        })
      );
    }
  };

  // Function to handle reply submission
  const handleSubmitReply = (parentId, replyText, parentLevel = 0) => {
    if (replyText.trim()) {
      const newReply = {
        id: generateCommentId(),
        userId: "current", // In a real app, this would be the current user's ID
        text: replyText,
        timestamp: new Date().toISOString(),
        parentId,
        level: parentLevel + 1, // Increment the nesting level
        replies: [], // Allow for nested replies up to level 3
      };

      // Function to recursively find and update the parent comment or reply
      const updateReplies = (items) => {
        return items.map((item) => {
          if (item.id === parentId) {
            return {
              ...item,
              replies: [...(item.replies || []), newReply],
            };
          } else if (item.replies && item.replies.length > 0) {
            return {
              ...item,
              replies: updateReplies(item.replies),
            };
          }
          return item;
        });
      };

      setComments(updateReplies(comments));
      setReplyingToId(null);
    }
  };

  // Function to auto-resize textarea
  const autoResizeTextarea = (textareaElement) => {
    if (textareaElement) {
      // Reset height to auto to get the correct scrollHeight
      textareaElement.style.height = "auto";
      // Set the height to scrollHeight to fit all content
      textareaElement.style.height = textareaElement.scrollHeight + "px";
    }
  };

  // Wrapper function for the main comment textarea
  const autoResizeMainTextarea = () => {
    autoResizeTextarea(textareaRef.current);
  };

  // Initialize textarea height when comments are loaded or changed
  useEffect(() => {
    if (textareaRef.current) {
      autoResizeMainTextarea();
    }
  }, [comments]);

  // Auto-resize edit textarea when it becomes visible
  useEffect(() => {
    if (editingCommentId && editTextareaRef.current) {
      autoResizeTextarea(editTextareaRef.current);
    }
  }, [editingCommentId]);

  // Auto-resize reply textarea when it becomes visible
  useEffect(() => {
    if (replyingToId && replyTextareaRef.current) {
      autoResizeTextarea(replyTextareaRef.current);
    }
  }, [replyingToId]);

  // Fetch users for dropdown selections (approver and executor)
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await apiService.get(ENDPOINTS.USERS.GET_BASIC("all"));

        if (response && Array.isArray(response.users)) {
          setUsers(response.users);
        } else if (response && Array.isArray(response)) {
          setUsers(response);
        } else {
        }
      } catch (err) {
        console.error("Failed to fetch users:", err);
        // setErrorWithTimeout("Failed to load user data");
      }
    };

    fetchUsers();
  }, []);

  useEffect(() => {
    let isMounted = true;
    const requestId = new URLSearchParams(location.search).get("id");

    // Skip if the same request ID
    if (requestId === requestIdRef.current) {
      return;
    }
    requestIdRef.current = requestId;

    const fetchRequest = async () => {
      if (!requestId) {
        setError("Request ID is required");
        return;
      }

      try {
        const response = await apiService.get(ENDPOINTS.REQUEST.GET(requestId));
        console.log("Request API response:", response);
        console.log("Request base data:", response?.request_base);

        if (isMounted) {
          setRequest(response);
        }
      } catch (err) {
        console.error("Error fetching request:", err);
        if (isMounted) {
          setError("Failed to load request details");
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchRequest();

    return () => {
      isMounted = false;
    };
  }, [location.search]);

  const getStatusSteps = () => {
    // Define the possible status flows
    const steps = [
      { key: "pending", label: "Pending", icon: <FiClock /> },
      { key: "reviewed", label: "Reviewed", icon: <FiCheckCircle /> },
      { key: "approved", label: "Approved", icon: <FiCheckCircle /> },
      { key: "rejected", label: "Rejected", icon: <FiXCircle /> },
      { key: "in_progress", label: "In Progress", icon: <FiLoader /> },
      { key: "done", label: "Done", icon: <FiCheckCircle /> },
    ];

    // Determine the current flow and active step
    let currentFlow = [];
    let activeIndex = -1;

    if (request) {
      if (request["request_base"].status === "rejected") {
        // For rejected requests
        if (request["request_base"].request_type === "access-report") {
          // Check if the request was rejected after review or directly from pending
          if (
            request["request_logs"] &&
            request["request_logs"].some((log) => log.value === "reviewed")
          ) {
            // For access-report rejected after review: pending -> reviewed -> rejected
            currentFlow = [steps[0], steps[1], steps[3]];
            activeIndex = 2;
          } else {
            // For access-report rejected directly from pending: pending -> rejected
            currentFlow = [steps[0], steps[3]];
            activeIndex = 1;
          }
        } else {
          // For other types: pending -> rejected
          currentFlow = [steps[0], steps[3]];
          activeIndex = 1;
        }
      } else if (request["request_base"].request_type === "access-report") {
        // Special flow for access-report: pending -> reviewed -> approved -> in_progress -> done
        currentFlow = [steps[0], steps[1], steps[2], steps[4], steps[5]];

        switch (request["request_base"].status) {
          case "pending":
            activeIndex = 0;
            break;
          case "reviewed":
            activeIndex = 1;
            break;
          case "approved":
            activeIndex = 2;
            break;
          case "in_progress":
            activeIndex = 3;
            break;
          case "done":
            activeIndex = 4;
            break;
          default:
            activeIndex = 0;
        }
      } else {
        // Standard flow for other request types: pending -> approved -> in_progress -> done
        currentFlow = [steps[0], steps[2], steps[4], steps[5]];

        switch (request["request_base"].status) {
          case "pending":
            activeIndex = 0;
            break;
          case "approved":
            activeIndex = 1;
            break;
          case "in_progress":
            activeIndex = 2;
            break;
          case "done":
            activeIndex = 3;
            break;
          default:
            activeIndex = 0;
        }
      }
    }

    return { currentFlow, activeIndex };
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Not specified";
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Function to handle opening the process modal
  const openProcessModal = () => {
    setIsProcessModalOpen(true);
    setProcessingStep(0);
    setProcessingComplete(false);
    setProcessingError(null);
    setCheckedItems({}); // Reset checkbox states
    // Reset file upload states for adhoc-data requests
    setUploadedFiles([]);
    setUploadProgress({});
    setUploadErrors({});
  };

  // Function to close the process modal
  const closeProcessModal = () => {
    // Clean up uploaded files if user cancels
    if (!processingComplete && uploadedFiles.length > 0) {
      cleanupUploadedFiles();
    }

    setIsProcessModalOpen(false);

    // If processing was completed, refresh the page to update information
    if (processingComplete) {
      window.location.reload();
    }
  };

  // Function to clean up uploaded files
  const cleanupUploadedFiles = async () => {
    const filesToDelete = uploadedFiles.filter(file =>
      file.status === 'uploaded' && file.uploadedFileId
    );

    for (const file of filesToDelete) {
      try {
        await apiService.delete(ENDPOINTS.FILES.DELETE(file.uploadedFileId));
      } catch (error) {
        console.error('Error deleting file during cleanup:', error);
      }
    }

    setUploadedFiles([]);
    setUploadProgress({});
    setUploadErrors({});

    // Clear localStorage data when cleaning up
    if (window.clearAdhocUploadData) {
      window.clearAdhocUploadData();
    }
  };

  // Function to handle Ad-hoc Data request processing
  const handleProcessAdhocRequest = async () => {
    try {
      // Step 1: In Progress
      setProcessingStep(1);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Get uploaded file IDs
      const uploadedFileIds = uploadedFiles
        .filter(file => file.status === 'uploaded' && file.uploadedFileId)
        .map(file => file.uploadedFileId);

      // Call API to update status to in_progress with uploaded files
      const response = await apiService.post(
        ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
        {
          status: "in_progress",
          uploaded_files: uploadedFileIds
        }
      );

      if (!response) {
        throw new Error("Failed to update request status");
      }

      // Step 2: Data Extraction
      setProcessingStep(2);
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Step 3: Data Validation & Processing
      setProcessingStep(3);
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Step 4: Data Delivery
      setProcessingStep(4);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Refresh the request data after successful update
      const updatedRequest = await apiService.get(
        ENDPOINTS.REQUEST.GET(request["request_base"].id)
      );
      setRequest(updatedRequest);

      // Mark processing as complete
      setProcessingComplete(true);

      // Clear localStorage data after successful processing
      if (window.clearAdhocUploadData) {
        window.clearAdhocUploadData();
      }
    } catch (error) {
      console.error("Error processing adhoc request:", error);
      setProcessingError(
        error.message || "An error occurred during adhoc data processing"
      );
    }
  };

  // Function to extract checked item IDs for report_needed
  const getCheckedItemIds = () => {
    const checkedViewIds = [];
    const checkedWorkbookIds = [];

    Object.entries(checkedItems).forEach(([key, isChecked]) => {
      if (isChecked) {
        if (key.startsWith("view-")) {
          checkedViewIds.push(key.replace("view-", ""));
        } else if (key.startsWith("workbook-")) {
          checkedWorkbookIds.push(key.replace("workbook-", ""));
        }
      }
    });

    return {
      views: checkedViewIds,
      workbooks: checkedWorkbookIds,
    };
  };

  // Function to handle Access Report request processing
  const handleProcessReportRequest = async () => {
    try {
      // Step 1: In Progress
      setProcessingStep(1);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Get checked item IDs for report_needed
      const checkedIds = getCheckedItemIds();

      // Call API to update status to in_progress
      const response = await apiService.post(
        ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
        {
          status: "in_progress",
          report_needed: checkedIds,
        }
      );

      if (!response) {
        throw new Error("Failed to update request status");
      }

      // Step 2: Assign Access to Executors
      setProcessingStep(2);
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Step 3: Configure Report Permissions
      setProcessingStep(3);
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Step 4: Send Notifications
      setProcessingStep(4);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Refresh the request data after successful update
      const updatedRequest = await apiService.get(
        ENDPOINTS.REQUEST.GET(request["request_base"].id)
      );
      setRequest(updatedRequest);

      // Store the username from the API response if available
      if (response && response.data && response.data.username) {
        setProcessedUsername(response.data.username);
      }

      // Mark processing as complete
      setProcessingComplete(true);
    } catch (error) {
      console.error("Error processing report request:", error);
      setProcessingError(
        error.message || "An error occurred during report access processing"
      );
    }
  };

  // Function to handle Access Platform request processing
  const handleProcessPlatformRequest = async () => {
    try {
      // Step 1: In Progress
      setProcessingStep(1);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Call API to update status to in_progress
      const response = await apiService.post(
        ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
        { status: "in_progress" }
      );

      if (!response) {
        throw new Error("Failed to update request status");
      }

      // Step 2: Verify User Information
      setProcessingStep(2);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Step 3: Create/Update Platform Access
      setProcessingStep(3);
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Step 4: Setup User Credentials
      setProcessingStep(4);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Refresh the request data after successful update
      const updatedRequest = await apiService.get(
        ENDPOINTS.REQUEST.GET(request["request_base"].id)
      );
      setRequest(updatedRequest);

      // Store the username from the API response if available
      if (response && response.data && response.data.username) {
        setProcessedUsername(response.data.username);
      }

      // Mark processing as complete
      setProcessingComplete(true);
    } catch (error) {
      console.error("Error processing platform request:", error);
      setProcessingError(
        error.message || "An error occurred during platform access processing"
      );
    }
  };

  // Main function to handle request processing based on type
  const handleProcessRequest = async () => {
    const requestType = request["request_base"].request_type;

    switch (requestType) {
      case "adhoc-data":
        await handleProcessAdhocRequest();
        break;
      case "access-report":
        await handleProcessReportRequest();
        break;
      case "access-platform":
        await handleProcessPlatformRequest();
        break;
      case "access-database":
        // Use platform handler for database access (similar process)
        await handleProcessPlatformRequest();
        break;
      default:
        // Fallback to generic processing
        await handleProcessPlatformRequest();
        break;
    }
  };

  // Function to navigate to user detail page
  const goToUserDetail = () => {
    // First try to use the username from the API response
    if (processedUsername) {
      navigate(`/userdetail?username=${processedUsername}`);
    } else {
      // Fallback to extracting username from request data
      const username =
        request["request_base"].requester_username ||
        request["request_base"].username ||
        request["request_base"].user_id;

      if (username) {
        navigate(`/userdetail?username=${username}`);
      } else {
        console.error("Username not found in request data or API response");
      }
    }

    closeProcessModal();
  };

  // Function to close modal and refresh page
  const closeModalAndRefresh = () => {
    setIsProcessModalOpen(false);
    window.location.reload();
  };

  const getRequestTypeLabel = (type) => {
    switch (type) {
      case "adhoc-data":
        return "Ad-hoc Data";
      case "access-platform":
        return "Platform Access";
      case "access-database":
        return "Database Access";
      case "access-report":
        return "Report Access";
      default:
        return type;
    }
  };

  // Function to get processing steps based on request type
  const getProcessingSteps = (requestType) => {
    switch (requestType) {
      case "adhoc-data":
        return [
          { title: "In Progress", description: "Updating request status" },
          // {
          //   title: "Data Extraction",
          //   description: "Extracting requested data from sources",
          // },
          {
            title: "Data Validation & Processing",
            description: "Validating and processing extracted data",
          },
          {
            title: "Data Delivery",
            description: "Preparing and delivering final data",
          },
          {
            title: "Send Notifications",
            description: "Sending access notifications to users",
          },
        ];
      case "access-report":
        return [
          {
            title: "In Progress",
            description: "Updating request status to in_progress",
          },
          {
            title: "Check Report Permissions",
            description: "Checking report access permissions",
          },
          {
            title: "Send Notifications",
            description: "Sending access notifications to users",
          },
        ];
      case "access-platform":
      case "access-database":
        return [
          { title: "In Progress", description: "Updating request status" },
          {
            title: "Verify User Information",
            description: "Verifying user credentials and information",
          },
          {
            title: "Create/Update Platform Access",
            description: "Setting up platform access permissions",
          },
          {
            title: "Send Notifications",
            description: "Sending access notifications to users",
          },
        ];
      default:
        return [
          { title: "In Progress", description: "Updating request status" },
          { title: "Verified User", description: "Verifying user information" },
          {
            title: "Created/Updated Role",
            description: "Setting up user permissions",
          },
          {
            title: "Marked Done Request",
            description: "Finalizing request processing",
          },
        ];
    }
  };

  if (loading) {
    return (
      <div className="request-detail-container loading">
        <FiLoader className="loading-icon" />
        <div className="loading-text">Loading request details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="request-detail-container error">
        <FiXCircle className="error-icon" />
        <div className="error-message">{error}</div>
        <button className="btn-primary" onClick={() => navigate("/requests")}>
          <FiArrowLeft /> Back to All Requests
        </button>
      </div>
    );
  }

  const { currentFlow, activeIndex } = getStatusSteps();

  return (
    <div className="request-detail-container">
      {/* Processing Modal */}
      {isProcessModalOpen && (
        <div className="modal-overlay">
          <div
            className="modal-container process-modal"
            style={{
              maxWidth:
                request["request_base"].request_type === "access-report"
                  ? "750px"
                  : undefined,
              // textAlign: request["request_base"].request_type === "access-report" ? "left" : undefined
            }}
          >
            <div className="modal-header">
              <h3>Process Request</h3>
              <button className="modal-close" onClick={closeProcessModal}>
                ×
              </button>
            </div>
            <div className="modal-body">
              {processingError ? (
                <div
                  className="process-error"
                  style={{
                    textAlign: "center",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                  }}
                >
                  <FiXCircle className="error-icon" />
                  <p className="error-message">{processingError}</p>
                  <button className="btn-primary" onClick={closeProcessModal}>
                    Close
                  </button>
                </div>
              ) : processingStep === 0 ? (
                <div
                  className="process-confirmation"
                  style={{
                    // textAlign: "center",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                  }}
                >
                  {/* For access-report request type, show Views + Workbooks table */}
                  {request["request_base"].request_type === "access-report" &&
                  request["request_base"].report_needed ? (
                    <div className="process-modal-content">
                      <p style={{ marginBottom: "20px" }}>
                        Please review the requested reports and mark completed
                        items:
                      </p>
                      <ProcessModalReportTable
                        reportNeeded={request["request_base"].report_needed}
                        checkedItems={checkedItems}
                        onCheckboxChange={handleCheckboxChange}
                        onMasterCheckboxChange={handleMasterCheckboxChange}
                        getMasterCheckboxState={getMasterCheckboxState}
                      />
                      <div
                        className="modal-actions"
                        style={{ marginTop: "20px" }}
                      >
                        <button
                          className="btn-secondary"
                          onClick={closeProcessModal}
                        >
                          Cancel
                        </button>
                        <button
                          className="btn-primary"
                          onClick={handleProcessRequest}
                          disabled={!hasCheckedItems()}
                          title={
                            !hasCheckedItems()
                              ? "Please select at least one item to process"
                              : "Process selected items"
                          }
                        >
                          Process Request
                        </button>
                      </div>
                    </div>
                  ) : request["request_base"].request_type === "adhoc-data" ? (
                    /* File upload form for adhoc-data request type */
                    <div className="process-modal-content">
                      <AdhocDataUploadForm
                        uploadedFiles={uploadedFiles}
                        setUploadedFiles={setUploadedFiles}
                        uploadProgress={uploadProgress}
                        setUploadProgress={setUploadProgress}
                        uploadErrors={uploadErrors}
                        setUploadErrors={setUploadErrors}
                        requestId={request["request_base"].id}
                      />
                      <div
                        className="modal-actions"
                        style={{ marginTop: "20px" }}
                      >
                        <button
                          className="btn-secondary"
                          onClick={closeProcessModal}
                        >
                          Cancel
                        </button>
                        <button
                          className="btn-primary"
                          onClick={handleProcessRequest}
                          disabled={!hasUploadedFiles() || hasUploadingFiles()}
                          title={
                            hasUploadingFiles()
                              ? "Please wait for files to finish uploading"
                              : !hasUploadedFiles()
                              ? "Please upload at least one file to process"
                              : "Process request with uploaded files"
                          }
                        >
                          {hasUploadingFiles() ? (
                            <>
                              <FiLoader className="spin" />
                              <span>Uploading...</span>
                            </>
                          ) : (
                            <>
                              <span>Process Request</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  ) : (
                    /* Default UI for other request types */
                    <>
                      <p>
                        Are you sure you want to process this request?
                        <br />
                        This will update the request status and perform the
                        following steps:
                      </p>
                      <ol className="process-steps-preview">
                        {getProcessingSteps(
                          request["request_base"].request_type
                        ).map((step, index) => (
                          <li key={index}>{step.title}</li>
                        ))}
                      </ol>
                      <div className="modal-actions">
                        <button
                          className="btn-secondary"
                          onClick={closeProcessModal}
                        >
                          Cancel
                        </button>
                        <button
                          className="btn-primary"
                          onClick={handleProcessRequest}
                        >
                          Confirm
                        </button>
                      </div>
                    </>
                  )}
                </div>
              ) : (
                <div
                  className="process-progress"
                  style={{
                    textAlign: "center",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                  }}
                >
                  <div className="process-steps" style={{ width: "100%" }}>
                    {getProcessingSteps(
                      request["request_base"].request_type
                    ).map((step, index) => (
                      <div
                        key={index}
                        className={`process-step ${
                          processingStep >= index + 1 ? "active" : ""
                        } ${processingStep === index + 1 ? "current" : ""}`}
                      >
                        <div className="step-icon">
                          {processingStep > index + 1 ||
                          (processingComplete &&
                            processingStep >= index + 1) ? (
                            <FiCheckCircle />
                          ) : processingStep === index + 1 ? (
                            <FiLoader className="spin" />
                          ) : (
                            <FiClock />
                          )}
                        </div>
                        <div className="step-content">
                          <div className="step-title">{step.title}</div>
                          <div className="step-description">
                            {step.description}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {processingComplete && (
                    <div
                      className="process-complete"
                      style={{
                        textAlign: "center",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <FiCheckCircle className="success-icon" />
                      <p className="success-message">
                        {(() => {
                          switch (request["request_base"].request_type) {
                            case "adhoc-data":
                              return "Data extraction and delivery completed successfully!";
                            case "access-report":
                              return request["request_base"].executors &&
                                request["request_base"].executors.length > 0
                                ? "Report access granted successfully!"
                                : "Report access configured successfully!";
                            case "access-platform":
                              return "Platform access granted successfully!";
                            case "access-database":
                              return "Database access granted successfully!";
                            default:
                              return "Request processed successfully!";
                          }
                        })()}
                      </p>

                      {/* {request["request_base"].request_type ===
                        "access-report" &&
                        request["request_base"].executors &&
                        request["request_base"].executors.length > 0 && (
                          <div className="executor-result-list">
                            {request["request_base"].executors.map(
                              (executor, index) => (
                                <div
                                  key={`result-${index}`}
                                  className="executor-result-item"
                                >
                                  <div className="executor-result-icon">
                                    <FiUser />
                                  </div>
                                  <div className="executor-result-info">
                                    <div className="executor-result-name">
                                      {executor}
                                    </div>
                                    <div className="executor-result-details">
                                      <span className="executor-result-badge">
                                        Read Access
                                      </span>
                                      <span className="executor-result-duration">
                                        30 days
                                      </span>
                                    </div>
                                  </div>
                                  <div className="executor-result-status">
                                    <FiCheckCircle className="success" />
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        )} */}

                      <div className="modal-actions">
                        <button
                          className="btn-secondary"
                          onClick={closeProcessModal}
                        >
                          Close
                        </button>
                        {(() => {
                          switch (request["request_base"].request_type) {
                            case "adhoc-data":
                            case "access-report":
                              return (
                                <button
                                  className="btn-primary"
                                  onClick={() => {
                                    navigate(`/requests`);
                                  }}
                                >
                                  <FiExternalLink />
                                  <span>Go to All Requests</span>
                                </button>
                              );
                            case "access-platform":
                            case "access-database":
                              return (
                                <button
                                  className="btn-primary"
                                  onClick={() => {
                                    goToUserDetail();
                                  }}
                                >
                                  <FiExternalLink />
                                  <span>Go to User Detail</span>
                                </button>
                              );
                            default:
                              return (
                                <button
                                  className="btn-primary"
                                  onClick={closeModalAndRefresh}
                                >
                                  <FiCheckCircle />
                                  <span>Done</span>
                                </button>
                              );
                          }
                        })()}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Guide Modal for access-platform requests */}
      {isGuideModalOpen && (
        <div className="modal-overlay">
          <div className="modal-container guide-modal">
            <div className="modal-header">
              <h3>ZDS Platform Access Guide</h3>
              <button className="modal-close" onClick={() => setIsGuideModalOpen(false)}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="guide-content-simple">
                <ul className="guide-list">
                  <li>Login to OPVN</li>
                  <li>Add host: <code>*********** zds.zalopay.vn</code></li>
                  <li>Access <a href="https://zds.zalopay.vn/" target="_blank" rel="noopener noreferrer">https://zds.zalopay.vn/</a></li>
                  <li>Click "Continue with VNG CAS"</li>
                  <li>Or enter User Domain + Password (PIN + OTP) and click Login</li>
                </ul>
              </div>

              <div className="modal-actions">
                <button
                  className="btn-primary"
                  onClick={() => setIsGuideModalOpen(false)}
                >
                  Got it
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header with back button and status */}
      <div className="request-detail-header">
        <div className="header-left">
          <button className="back-button" onClick={() => navigate("/requests")}>
            <FiArrowLeft />
            <span>Back</span>
          </button>
          <div className="breadcrumb">
            <span>Requests</span>
            <FiChevronRight className="breadcrumb-separator" />
            <span className="current-page">Request Details</span>
          </div>
        </div>
        {/* <div className={`status-badge ${request["request_base"].status}`}>
          {request["request_base"].status === "pending" && <FiClock />}
          {request["request_base"].status === "approved" && <FiCheckCircle />}
          {request["request_base"].status === "rejected" && <FiXCircle />}
          {request["request_base"].status === "in_progress" && <FiLoader />}
          {request["request_base"].status === "done" && <FiCheckCircle />}
          <span>{request["request_base"].status.replace("_", " ")}</span>
        </div> */}
      </div>

      {/* Title and meta information */}
      <div className="request-title-section">
        <div className="request-title-left">
          <h1>{request["request_base"].title}</h1>
          <div className="request-meta">
            <div className="meta-item">
              <FiCalendar className="meta-icon" />
              <span>
                Created: {formatDate(request["request_base"].created_at)}
              </span>
            </div>
            {request["request_base"].due_date && (
              <div className="meta-item">
                <FiCalendar className="meta-icon" />
                <span>Due: {request["request_base"].due_date}</span>
              </div>
            )}
          </div>
        </div>
        <div className="request-title-right">
          <div className="request-actions">
            {/* Only show actions if user has permission */}
            {canPerformActions() && (
              <>
                {/* For access-report request type with pending status: Show Review & Reject buttons */}
                {request["request_base"].request_type === "access-report" &&
                  request["request_base"].status === "pending" && (
                    <>
                      <button
                        className="btn-action btn-reject"
                        title="Reject this request"
                        onClick={async () => {
                          try {
                            // Handle reject action
                            console.log(
                              "Rejecting request:",
                              request["request_base"].id
                            );
                            const response = await apiService.post(
                              ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
                              { status: "rejected" }
                            );

                            if (response) {
                              // Refresh the request data after successful update
                              const updatedRequest = await apiService.get(
                                ENDPOINTS.REQUEST.GET(request["request_base"].id)
                              );
                              setRequest(updatedRequest);

                              // Show success message (you can add a toast notification here)
                              console.log("Request rejected successfully");
                            }
                          } catch (error) {
                            console.error("Error rejecting request:", error);
                            // Show error message (you can add a toast notification here)
                          }
                        }}
                      >
                        <FiXCircle />
                        <span>Reject</span>
                      </button>
                      <button
                        className="btn-action btn-review"
                        title="Review this request"
                        onClick={async () => {
                          try {
                            // Handle review action
                            console.log(
                              "Reviewing request:",
                              request["request_base"].id
                            );
                            const response = await apiService.post(
                              ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
                              { status: "reviewed" }
                            );

                            if (response) {
                              // Refresh the request data after successful update
                              const updatedRequest = await apiService.get(
                                ENDPOINTS.REQUEST.GET(request["request_base"].id)
                              );
                              setRequest(updatedRequest);

                              // Show success message (you can add a toast notification here)
                              console.log("Request reviewed successfully");
                            }
                          } catch (error) {
                            console.error("Error reviewing request:", error);
                            // Show error message (you can add a toast notification here)
                          }
                        }}
                      >
                        <FiCheckCircle />
                        <span>Review</span>
                      </button>
                    </>
                  )}

                {/* For access-report request type with reviewed status: Show Approve & Reject buttons */}
                {request["request_base"].request_type === "access-report" &&
                  request["request_base"].status === "reviewed" && (
                    <>
                      <button
                        className="btn-action btn-reject"
                        title="Reject this request"
                        onClick={async () => {
                          try {
                            // Handle reject action
                            console.log(
                              "Rejecting request:",
                              request["request_base"].id
                            );
                            const response = await apiService.post(
                              ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
                              { status: "rejected" }
                            );

                            if (response) {
                              // Refresh the request data after successful update
                              const updatedRequest = await apiService.get(
                                ENDPOINTS.REQUEST.GET(request["request_base"].id)
                              );
                              setRequest(updatedRequest);

                              // Show success message (you can add a toast notification here)
                              console.log("Request rejected successfully");
                            }
                          } catch (error) {
                            console.error("Error rejecting request:", error);
                            // Show error message (you can add a toast notification here)
                          }
                        }}
                      >
                        <FiXCircle />
                        <span>Reject</span>
                      </button>
                      <button
                        className="btn-action btn-approve"
                        title="Approve this request"
                        onClick={async () => {
                          try {
                            // Handle approve action
                            console.log(
                              "Approving request:",
                              request["request_base"].id
                            );
                            const response = await apiService.post(
                              ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
                              { status: "approved" }
                            );

                            if (response) {
                              // Refresh the request data after successful update
                              const updatedRequest = await apiService.get(
                                ENDPOINTS.REQUEST.GET(request["request_base"].id)
                              );
                              setRequest(updatedRequest);

                              // Show success message (you can add a toast notification here)
                              console.log("Request approved successfully");
                            }
                          } catch (error) {
                            console.error("Error approving request:", error);
                            // Show error message (you can add a toast notification here)
                          }
                        }}
                      >
                        <FiCheckCircle />
                        <span>Approve</span>
                      </button>
                    </>
                  )}

                {/* For other request types with pending status: Show Approve & Reject buttons */}
                {request["request_base"].request_type !== "access-report" &&
                  request["request_base"].status === "pending" && (
                    <>
                      <button
                        className="btn-action btn-reject"
                        title="Reject this request"
                        onClick={async () => {
                          try {
                            // Handle reject action
                            console.log(
                              "Rejecting request:",
                              request["request_base"].id
                            );
                            const response = await apiService.post(
                              ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
                              { status: "rejected" }
                            );

                            if (response) {
                              // Refresh the request data after successful update
                              const updatedRequest = await apiService.get(
                                ENDPOINTS.REQUEST.GET(request["request_base"].id)
                              );
                              setRequest(updatedRequest);

                              // Show success message (you can add a toast notification here)
                              console.log("Request rejected successfully");
                            }
                          } catch (error) {
                            console.error("Error rejecting request:", error);
                            // Show error message (you can add a toast notification here)
                          }
                        }}
                      >
                        <FiXCircle />
                        <span>Reject</span>
                      </button>
                      <button
                        className="btn-action btn-approve"
                        title="Approve this request"
                        onClick={async () => {
                          try {
                            // Handle approve action
                            console.log(
                              "Approving request:",
                              request["request_base"].id
                            );
                            const response = await apiService.post(
                              ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
                              { status: "approved" }
                            );

                            if (response) {
                              // Refresh the request data after successful update
                              const updatedRequest = await apiService.get(
                                ENDPOINTS.REQUEST.GET(request["request_base"].id)
                              );
                              setRequest(updatedRequest);

                              // Show success message (you can add a toast notification here)
                              console.log("Request approved successfully");
                            }
                          } catch (error) {
                            console.error("Error approving request:", error);
                            // Show error message (you can add a toast notification here)
                          }
                        }}
                      >
                        <FiCheckCircle />
                        <span>Approve</span>
                      </button>
                    </>
                  )}

                {/* For all request types with approved status OR in-progress access-report with incomplete items: Show Process button */}
                {(request["request_base"].status === "approved" ||
                  (request["request_base"].status === "in_progress" &&
                    request["request_base"].request_type === "access-report" &&
                    !areAllItemsCompleted())) && (
                  <button
                    className="btn-action btn-process"
                    title="Process this request"
                    onClick={openProcessModal}
                  >
                    <FiActivity />
                    <span>Process</span>
                  </button>
                )}

                {/* For all request types with in_progress status: Show Complete button */}
                {request["request_base"].status === "in_progress" && (
                  <button
                    className="btn-action btn-complete"
                    title="Mark this request as complete"
                    onClick={async () => {
                      try {
                        // Handle complete action
                        console.log(
                          "Completing request:",
                          request["request_base"].id
                        );
                        const response = await apiService.post(
                          ENDPOINTS.REQUEST.UPDATE(request["request_base"].id),
                          { status: "done" }
                        );

                        if (response) {
                          // Refresh the request data after successful update
                          const updatedRequest = await apiService.get(
                            ENDPOINTS.REQUEST.GET(request["request_base"].id)
                          );
                          setRequest(updatedRequest);

                          // Show success message (you can add a toast notification here)
                          console.log("Request marked as complete successfully");
                        }
                      } catch (error) {
                        console.error("Error completing request:", error);
                        // Show error message (you can add a toast notification here)
                      }
                    }}
                  >
                    <FiCheckCircle />
                    <span>Complete</span>
                  </button>
                )}
              </>
            )}

            {/* For access-platform request type with done status: Show Guide button */}
            {request["request_base"].request_type === "access-platform" &&
              request["request_base"].status === "done" && (
                <button
                  className="btn-action btn-guide"
                  title="View access guide"
                  onClick={() => setIsGuideModalOpen(true)}
                >
                  <FiBook />
                  <span>Access Guide</span>
                </button>
              )}
          </div>
        </div>
      </div>

      {/* Status flow */}
      <div className="request-detail-status-flow">
        {currentFlow.map((step, index) => (
          <div
            key={step.key}
            data-step-key={step.key}
            className={`status-step ${index <= activeIndex ? "active" : ""} ${
              index === activeIndex ? "current" : ""
            }`}
          >
            <div className="step-icon">{step.icon}</div>
            <div className="step-label">{step.label}</div>
            {index < currentFlow.length - 1 && (
              <div
                className={`step-connector ${
                  index < activeIndex ? "active" : ""
                }`}
              ></div>
            )}
          </div>
        ))}
      </div>

      {/* Main content */}
      <div className="request-detail-body">
        {/* Left column */}
        <div className="request-detail-column">
          {/* Request Information */}
          <div className="request-detail-section request-info-section">
            <h2>
              <FiInfo /> Request Information
            </h2>
            <div className="request-detail-content">
              <div className="info-row">
                <div className="info-label">Type</div>
                <div className="info-value">
                  {getRequestTypeLabel(request["request_base"].request_type)}
                </div>
              </div>

              {request["request_base"].platform_name && (
                <div className="info-row">
                  <div className="info-label">Platform</div>
                  <div className="info-value">
                    {request["request_base"].platform_name}
                  </div>
                </div>
              )}

              {request["request_base"].db_name && (
                <div className="info-row">
                  <div className="info-label">Database</div>
                  <div className="info-value">
                    {request["request_base"].db_name}
                  </div>
                </div>
              )}

              {/* {request["request_base"].access_level && (
                <div className="info-row">
                  <div className="info-label">Access Level</div>
                  <div className="info-value">
                    {request["request_base"].access_level.replace("-", " ")}
                  </div>
                </div>
              )} */}

              {request["request_base"].description && (
                <div className="info-row description-row">
                  <div className="info-label">Description</div>
                  <div
                    className="info-value description"
                    dangerouslySetInnerHTML={{
                      __html: request["request_base"].description,
                    }}
                  />
                </div>
              )}

              {request["request_base"].data_needed && (
                <div className="info-row description-row">
                  <div className="info-label">Data Needed</div>
                  <div
                    className="info-value"
                    dangerouslySetInnerHTML={{
                      __html: request["request_base"].data_needed,
                    }}
                  />
                </div>
              )}

              {request["request_base"].report_needed && (
                <div className="info-row description-row">
                  <div className="info-label">Report Needed</div>
                  <ReportNeededTable
                    reportNeeded={request["request_base"].report_needed}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Data Files section - only show for adhoc-data request type */}
          {request["request_base"].request_type === "adhoc-data" && (
            <RequestDataFileSection
              requestId={new URLSearchParams(location.search).get("id")}
            />
          )}

          {/* Comments section */}
          <div className="request-detail-section comment-section">
            <h2>
              <FiMessageSquare /> Comments
            </h2>
            <div className="request-comments">
              {comments.length > 0 ? (
                <div className="comments-list">
                  {comments.map((comment) => (
                    <div key={comment.id} className="comment-item">
                      <div className="comment-avatar">
                        <FiUser />
                      </div>
                      <div className="comment-content">
                        <div className="comment-header">
                          <div className="comment-author">
                            User {comment.userId}
                          </div>
                          <div className="comment-meta">
                            <div className="comment-time">
                              {formatDate(comment.timestamp)}
                              {comment.edited && (
                                <span className="comment-edited">
                                  {" "}
                                  (edited)
                                </span>
                              )}
                            </div>
                            {comment.userId === "current" && (
                              <div className="comment-actions">
                                <button
                                  className="comment-action-btn"
                                  onClick={() =>
                                    setEditingCommentId(comment.id)
                                  }
                                  title="Edit comment"
                                >
                                  <FiEdit2 />
                                </button>
                                <button
                                  className="comment-action-btn delete"
                                  onClick={() =>
                                    handleDeleteComment(comment.id)
                                  }
                                  title="Delete comment"
                                >
                                  <FiTrash2 />
                                </button>
                              </div>
                            )}
                          </div>
                        </div>

                        {editingCommentId === comment.id ? (
                          <div className="comment-edit-form">
                            <textarea
                              ref={editTextareaRef}
                              className="comment-edit-input"
                              defaultValue={comment.text}
                              onChange={() => {
                                // Auto-resize the textarea after content change
                                setTimeout(
                                  () =>
                                    autoResizeTextarea(editTextareaRef.current),
                                  0
                                );
                              }}
                              onKeyDown={(e) => {
                                if (
                                  (e.metaKey || e.ctrlKey) &&
                                  e.key === "Enter"
                                ) {
                                  e.preventDefault();
                                  handleEditComment(comment.id, e.target.value);
                                }
                              }}
                            ></textarea>
                            <div className="comment-edit-actions">
                              <button
                                className="btn-cancel"
                                onClick={() => setEditingCommentId(null)}
                              >
                                Cancel
                              </button>
                              <button
                                className="btn-save"
                                onClick={() => {
                                  if (editTextareaRef.current) {
                                    handleEditComment(
                                      comment.id,
                                      editTextareaRef.current.value
                                    );
                                  }
                                }}
                              >
                                Save
                              </button>
                            </div>
                          </div>
                        ) : (
                          <>
                            <div className="comment-text">{comment.text}</div>
                            <div className="comment-footer">
                              <button
                                className="comment-reply-btn"
                                onClick={() => setReplyingToId(comment.id)}
                              >
                                Reply
                              </button>
                            </div>
                          </>
                        )}

                        {/* Reply form */}
                        {replyingToId === comment.id && (
                          <div className="comment-reply-form">
                            <textarea
                              ref={replyTextareaRef}
                              className="comment-reply-input"
                              placeholder="Write a reply..."
                              onChange={() => {
                                // Auto-resize the textarea after content change
                                setTimeout(
                                  () =>
                                    autoResizeTextarea(
                                      replyTextareaRef.current
                                    ),
                                  0
                                );
                              }}
                              onKeyDown={(e) => {
                                if (
                                  (e.metaKey || e.ctrlKey) &&
                                  e.key === "Enter"
                                ) {
                                  e.preventDefault();
                                  handleSubmitReply(
                                    comment.id,
                                    e.target.value,
                                    comment.level
                                  );
                                }
                              }}
                            ></textarea>
                            <div className="comment-reply-actions">
                              <button
                                className="btn-cancel"
                                onClick={() => setReplyingToId(null)}
                              >
                                Cancel
                              </button>
                              <button
                                className="btn-reply"
                                onClick={() => {
                                  if (replyTextareaRef.current) {
                                    handleSubmitReply(
                                      comment.id,
                                      replyTextareaRef.current.value,
                                      comment.level
                                    );
                                  }
                                }}
                              >
                                Reply
                              </button>
                            </div>
                          </div>
                        )}

                        {/* Render Replies - Recursive Component */}
                        {comment.replies && comment.replies.length > 0 && (
                          <RenderReplies
                            replies={comment.replies}
                            editingCommentId={editingCommentId}
                            setEditingCommentId={setEditingCommentId}
                            replyingToId={replyingToId}
                            setReplyingToId={setReplyingToId}
                            handleEditComment={handleEditComment}
                            handleDeleteComment={handleDeleteComment}
                            handleSubmitReply={handleSubmitReply}
                            editTextareaRef={editTextareaRef}
                            replyTextareaRef={replyTextareaRef}
                            autoResizeTextarea={autoResizeTextarea}
                            formatDate={formatDate}
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="no-comments">No comments yet</div>
              )}

              <div className="comment-form">
                <div className="comment-input-container">
                  <textarea
                    ref={textareaRef}
                    className="comment-input"
                    placeholder="Add a comment... (Press Cmd/Ctrl+Enter to submit)"
                    value={comment}
                    onChange={(e) => {
                      setComment(e.target.value);
                      // Auto-resize the textarea after content change
                      setTimeout(autoResizeMainTextarea, 0);
                    }}
                    onKeyDown={(e) => {
                      // Check for Command/Ctrl + Enter
                      if ((e.metaKey || e.ctrlKey) && e.key === "Enter") {
                        e.preventDefault();
                        handleSubmitComment();
                      }
                    }}
                  ></textarea>
                  <button
                    className="comment-submit"
                    disabled={!comment.trim()}
                    onClick={handleSubmitComment}
                    title="Submit comment (Cmd/Ctrl+Enter)"
                  >
                    <FiSend />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right column */}
        <div className="request-detail-column">
          {/* People section */}
          <div className="request-detail-section people-section">
            <h2>
              <FiUsers /> People
            </h2>
            <div className="request-people">
              <div className="people-item">
                <div className="people-avatar">
                  <FiUser />
                </div>
                <div className="people-info">
                  <div className="people-role">Requester</div>
                  <div className="people-id">
                    <span className="people-name">
                      {getUserDisplayName(request["request_base"].requester_id)}
                    </span>
                  </div>
                </div>
              </div>

              {request["request_base"].reviewer_id && (
                <div className="people-item">
                  <div className="people-avatar">
                    <FiUser />
                  </div>
                  <div className="people-info">
                    <div className="people-role">Reviewer</div>
                    <div className="people-id">
                      <span className="people-name">
                        {getUserDisplayName(
                          request["request_base"].reviewer_id
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {request["request_base"].approver_id && (
                <div className="people-item">
                  <div className="people-avatar">
                    <FiCheckCircle />
                  </div>
                  <div className="people-info">
                    <div className="people-role">Approver</div>
                    <div className="people-id">
                      <span className="people-name">
                        {getUserDisplayName(
                          request["request_base"].approver_id
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Display single executor for non-access-report request types */}
              {request["request_base"].executor_id &&
                request["request_base"].request_type !== "access-report" && (
                  <div className="people-item">
                    <div className="people-avatar">
                      <FiActivity />
                    </div>
                    <div className="people-info">
                      <div className="people-role">Executor</div>
                      <div className="people-id">
                        <span className="people-name">
                          {getUserDisplayName(
                            request["request_base"].executor_id
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

              {/* Display multiple executors for access-report request type */}
              {request["request_base"].request_type === "access-report" &&
                request["request_base"].executors &&
                request["request_base"].executors.length > 0 && (
                  <div className="executors-container">
                    <div className="executors-header">
                      <FiUsers className="executors-icon" />
                      <span className="executors-title">
                        Executors ({request["request_base"].executors.length})
                      </span>
                    </div>
                    <div
                      className={`executors-list ${
                        request["request_base"].executors.length > 3
                          ? "many-executors"
                          : ""
                      }`}
                    >
                      {request["request_base"].executors.map(
                        (executor, index) => (
                          <div
                            key={`executor-${index}`}
                            className="executor-item"
                          >
                            <div className="executor-avatar">
                              <FiUser />
                            </div>
                            <div className="executor-info">
                              <span className="executor-name">
                                {getUserDisplayNameByUsername(executor)}
                              </span>
                              {/* <span className="executor-badge">Data Access</span> */}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
            </div>
          </div>

          {/* Timeline section */}
          <div className="request-detail-section timeline-section">
            <h2>
              <FiBarChart2 /> Timeline
            </h2>
            <div className="request-timeline">
              {request["request_logs"] && request["request_logs"].length > 0 ? (
                // Sort logs by created_at date in ascending order
                [...request["request_logs"]]
                  .sort(
                    (a, b) => new Date(a.created_at) - new Date(b.created_at)
                  )
                  .map((log) => {
                    // Get appropriate icon based on log_type
                    const getLogIcon = (type) => {
                      switch (type) {
                        case "init_request":
                          return <FiClock />;
                        case "change_status":
                          return <FiActivity />;
                        case "assign_approver":
                          return <FiCheckCircle />;
                        case "assign_executor":
                          return <FiUser />;
                        case "comment_added":
                          return <FiMessageSquare />;
                        case "request_approved":
                          return <FiCheckCircle />;
                        case "request_rejected":
                          return <FiXCircle />;
                        case "request_completed":
                          return <FiCheckCircle />;
                        default:
                          return <FiInfo />;
                      }
                    };

                    // Get appropriate title based on log_type
                    const getLogTitle = (type) => {
                      switch (type) {
                        case "init_request":
                          return "Request Created";
                        case "change_status":
                          return "Status Changed";
                        case "assign_approver":
                          return "Approver Assigned";
                        case "assign_executor":
                          return "Executor Assigned";
                        case "comment_added":
                          return "Comment Added";
                        case "request_approved":
                          return "Request Approved";
                        case "request_rejected":
                          return "Request Rejected";
                        case "request_completed":
                          return "Request Completed";
                        default:
                          return "Update";
                      }
                    };

                    return (
                      <div key={log.id} className="timeline-item">
                        <div className="timeline-icon">
                          {getLogIcon(log.log_type)}
                        </div>
                        <div className="timeline-content">
                          <div className="timeline-title">
                            {getLogTitle(log.log_type)}
                          </div>
                          <div className="timeline-time">
                            {formatDate(log.created_at)}
                          </div>
                          {log.note && (
                            <div className="timeline-description">
                              {log.note}
                            </div>
                          )}
                          {log.value && (
                            <div className="timeline-value">
                              {log.log_type === "status_change" ? (
                                <div className={`timeline-status ${log.value}`}>
                                  {log.value.replace("_", " ")}
                                </div>
                              ) : (
                                log.value
                              )}
                            </div>
                          )}
                          {(log.changed_by_id || log.changed_by_name) && (
                            <div className="timeline-user">
                              By:{" "}
                              {log.changed_by_id
                                ? getUserDisplayName(log.changed_by_id)
                                : log.changed_by_name}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })
              ) : (
                // Fallback if no logs are available
                <div className="timeline-item">
                  <div className="timeline-icon">
                    <FiClock />
                  </div>
                  <div className="timeline-content">
                    <div className="timeline-title">Request Created</div>
                    <div className="timeline-time">
                      {formatDate(request["request_base"].created_at)}
                    </div>
                    <div className="timeline-description">
                      Request was submitted to the system
                    </div>
                  </div>
                </div>
              )}

              {/* Show last updated entry if it's different from created_at and not already shown in logs */}
              {request["request_base"].updated_at &&
                request["request_base"].updated_at !==
                  request["request_base"].created_at &&
                (!request["request_logs"] ||
                  !request["request_logs"].some(
                    (log) =>
                      log.created_at === request["request_base"].updated_at
                  )) && (
                  <div className="timeline-item">
                    <div className="timeline-icon">
                      <FiClock />
                    </div>
                    <div className="timeline-content">
                      <div className="timeline-title">Last Updated</div>
                      <div className="timeline-time">
                        {formatDate(request["request_base"].updated_at)}
                      </div>
                    </div>
                  </div>
                )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestDetail;
