import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiSearch,
  FiRefreshCw,
  FiChevronLeft,
  FiChevronRight,
  FiAlertCircle,
  FiFilter,
  FiGrid,
  FiColumns,
} from "react-icons/fi";
import apiService from "../../services/api.service";
import { ENDPOINTS } from "../../config/api.config";
import "../../styles/Requests.css";

const Requests = () => {
  const navigate = useNavigate();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [error, setError] = useState(null);
  const [statusCounts, setStatusCounts] = useState({});

  // Filters
  const [selectedRequestType, setSelectedRequestType] = useState(() => {
    // Load the saved request type from localStorage, default to empty string if not found
    return localStorage.getItem("requestType") || "";
  });
  const [selectedStatus, setSelectedStatus] = useState(() => {
    // Load the saved status from localStorage, default to empty string if not found
    return localStorage.getItem("requestStatus") || "";
  });
  const [dueDateFilter, setDueDateFilter] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortOrder, setSortOrder] = useState("newest");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(() => {
    const savedItemsPerPage = localStorage.getItem("requestItemsPerPage");
    return savedItemsPerPage ? parseInt(savedItemsPerPage, 10) : 10;
  });

  // View mode state (grid or kanban)
  const [viewMode, setViewMode] = useState(() => {
    const savedViewMode = localStorage.getItem("requestViewMode");
    return savedViewMode || "grid";
  });

  // Fetch users for dropdown selections (approver and executor)
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await apiService.get(ENDPOINTS.USERS.GET_BASIC("all"));

        if (response && Array.isArray(response.users)) {
          setUsers(response.users);
        } else if (response && Array.isArray(response)) {
          setUsers(response);
        } else {
        }
      } catch (err) {
        console.error("Failed to fetch users:", err);
        // setErrorWithTimeout("Failed to load user data");
      }
    };

    fetchUsers();
  }, []);

  // Function to get user display name by ID
  const getUserDisplayName = (userId) => {
    if (!userId || !users || users.length === 0) {
      return "Unknown";
    }

    const user = users.find((u) => u.id === userId);
    return user ? user.display_name : "Unknown";
  };

  useEffect(() => {
    const fetchRequests = async () => {
      try {
        const userData = localStorage.getItem("user");
        if (!userData) {
          navigate("/login");
          return;
        }

        const user = JSON.parse(userData);
        const response = await apiService.get(
          ENDPOINTS.REQUEST.GET_BY_USER(user.id)
        );
        setRequests(response);

        // Calculate status counts
        const counts = response.reduce((acc, req) => {
          acc[req.status] = (acc[req.status] || 0) + 1;
          return acc;
        }, {});
        setStatusCounts(counts);
      } catch (err) {
        console.error("Error fetching requests:", err);
        setError("Failed to load requests");
      } finally {
        setLoading(false);
      }
    };

    fetchRequests();
  }, [navigate]);

  const handleRequestClick = (id) => {
    navigate(`/requests/detail?id=${id}`);
  };

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [
    selectedRequestType,
    selectedStatus,
    dueDateFilter,
    searchTerm,
    sortOrder,
  ]);

  const filterRequests = () => {
    let filtered = requests.filter((request) => {
      // Filter by request type
      if (selectedRequestType && request.request_type !== selectedRequestType) {
        return false;
      }

      // Filter by status
      if (selectedStatus && request.status !== selectedStatus) {
        return false;
      }

      // Filter by due date
      if (dueDateFilter && request.due_date) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const requestDate = new Date(request.due_date);
        requestDate.setHours(0, 0, 0, 0);

        if (dueDateFilter === "today") {
          // Show only items due today
          const todayDate = new Date();
          todayDate.setHours(0, 0, 0, 0);

          if (requestDate.getTime() !== todayDate.getTime()) {
            return false;
          }
        } else if (dueDateFilter === "week") {
          // Show items due within the next 7 days
          const weekLater = new Date(today);
          weekLater.setDate(weekLater.getDate() + 7);

          if (requestDate < today || requestDate > weekLater) {
            return false;
          }
        } else if (dueDateFilter === "month") {
          // Show items due within the next 30 days
          const monthLater = new Date(today);
          monthLater.setDate(monthLater.getDate() + 30);

          if (requestDate < today || requestDate > monthLater) {
            return false;
          }
        } else if (dueDateFilter === "custom" && request.due_date) {
          // Handle custom date if implemented
          return true;
        }
      }

      // Filter by search term
      if (searchTerm.trim() !== "") {
        const searchLower = searchTerm.toLowerCase();
        return (
          (request.title &&
            request.title.toLowerCase().includes(searchLower)) ||
          (request.description &&
            request.description.toLowerCase().includes(searchLower)) ||
          (request.platform_name &&
            request.platform_name.toLowerCase().includes(searchLower)) ||
          (request.db_name &&
            request.db_name.toLowerCase().includes(searchLower))
        );
      }

      return true;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a.updated_at);
      const dateB = new Date(b.updated_at);

      if (sortOrder === "newest") {
        return dateB - dateA; // Newest first
      } else {
        return dateA - dateB; // Oldest first
      }
    });

    return filtered;
  };

  const filteredRequests = filterRequests();

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredRequests.slice(
    indexOfFirstItem,
    indexOfLastItem
  );
  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);

  // Pagination handlers
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const nextPage = () =>
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage((prev) => Math.max(prev - 1, 1));
  const firstPage = () => setCurrentPage(1);
  const lastPage = () => setCurrentPage(totalPages);

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    const value = parseInt(e.target.value, 10);
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when changing items per page
    localStorage.setItem("requestItemsPerPage", value.toString());
  };

  // Calculate filtered status counts (excluding the status filter itself)
  const getFilteredStatusCounts = () => {
    // Apply all filters except the status filter
    return requests
      .filter((request) => {
        // Filter by request type
        if (
          selectedRequestType &&
          request.request_type !== selectedRequestType
        ) {
          return false;
        }

        // Filter by due date
        if (dueDateFilter && request.due_date) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          const requestDate = new Date(request.due_date);
          requestDate.setHours(0, 0, 0, 0);

          if (dueDateFilter === "today") {
            // Show only items due today
            const todayDate = new Date();
            todayDate.setHours(0, 0, 0, 0);

            if (requestDate.getTime() !== todayDate.getTime()) {
              return false;
            }
          } else if (dueDateFilter === "week") {
            // Show items due within the next 7 days
            const weekLater = new Date(today);
            weekLater.setDate(weekLater.getDate() + 7);

            if (requestDate < today || requestDate > weekLater) {
              return false;
            }
          } else if (dueDateFilter === "month") {
            // Show items due within the next 30 days
            const monthLater = new Date(today);
            monthLater.setDate(monthLater.getDate() + 30);

            if (requestDate < today || requestDate > monthLater) {
              return false;
            }
          } else if (dueDateFilter === "custom" && request.due_date) {
            // Handle custom date if implemented
            return true;
          }
        }

        // Filter by search term
        if (searchTerm.trim() !== "") {
          const searchLower = searchTerm.toLowerCase();
          return (
            (request.title &&
              request.title.toLowerCase().includes(searchLower)) ||
            (request.description &&
              request.description.toLowerCase().includes(searchLower)) ||
            (request.platform_name &&
              request.platform_name.toLowerCase().includes(searchLower)) ||
            (request.db_name &&
              request.db_name.toLowerCase().includes(searchLower))
          );
        }

        return true;
      })
      .reduce((counts, request) => {
        counts[request.status] = (counts[request.status] || 0) + 1;
        return counts;
      }, {});
  };

  const filteredStatusCounts = getFilteredStatusCounts();
  const totalFilteredCount = Object.values(filteredStatusCounts).reduce(
    (sum, count) => sum + count,
    0
  );

  const clearFilters = () => {
    setSelectedRequestType("");
    setSelectedStatus("");
    localStorage.removeItem("requestStatus");
    localStorage.removeItem("requestType");
    setDueDateFilter("");
    setSearchTerm("");
    setSortOrder("newest");
    setCurrentPage(1); // Reset to first page
  };

  const refreshRequests = async () => {
    setLoading(true);
    try {
      const userData = localStorage.getItem("user");
      if (!userData) {
        navigate("/login");
        return;
      }

      const user = JSON.parse(userData);
      const response = await apiService.get(
        ENDPOINTS.REQUEST.GET_BY_USER(user.id)
      );
      setRequests(response);

      // Calculate status counts
      const counts = response.reduce((acc, req) => {
        acc[req.status] = (acc[req.status] || 0) + 1;
        return acc;
      }, {});
      setStatusCounts(counts);
    } catch (err) {
      console.error("Error refreshing requests:", err);
      setError("Failed to refresh requests");
    } finally {
      setLoading(false);
    }
  };

  const getStatusClass = (status) => {
    // Return just the status name - App.css has .status-badge.<status> classes
    return status; // App.css already has proper status-badge styling
  };

  if (loading) {
    return <div className="content-container">Loading...</div>;
  }

  if (error) {
    return (
      <div className="error-message">
        <FiAlertCircle />
        <span>{error}</span>
      </div>
    );
  }

  return (
    <div className="content-container">
      <div className="request-page-header">
        <div className="request-filters">
          <div className="filter-group">
            <label>Request Type:</label>
            <select
              value={selectedRequestType}
              onChange={(e) => {
                setSelectedRequestType(e.target.value);
                localStorage.setItem("requestType", e.target.value);
              }}
              className="request-select"
            >
              <option value="">All</option>
              <option value="adhoc-data">Adhoc Data</option>
              <option value="access-platform">Access Platform</option>
              <option value="access-database">Access Database</option>
              <option value="access-report">Access Report</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Due Date:</label>
            <select
              className="request-select"
              onChange={(e) => setDueDateFilter(e.target.value)}
              value={dueDateFilter}
            >
              <option value="">All time</option>
              <option value="today">Today</option>
              <option value="week">This week</option>
              <option value="month">This month</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Sort:</label>
            <select
              className="request-select"
              onChange={(e) => setSortOrder(e.target.value)}
              value={sortOrder}
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
            </select>
          </div>
          <div className="filter-group">
            <label>Show:</label>
            <select
              className="request-select"
              onChange={handleItemsPerPageChange}
              value={itemsPerPage}
            >
              <option value="5">5 items</option>
              <option value="10">10 items</option>
              <option value="20">20 items</option>
              <option value="50">50 items</option>
            </select>
          </div>
        </div>

        <div className="request-search">
          <div className="request-search-container">
            <span className="search-icon">
              <FiSearch />
            </span>
            <input
              type="text"
              placeholder="Search requests..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="request-search-input"
            />
          </div>
        </div>

        <div className="request-actions">
          <button
            className={`request-btn-view-toggle ${
              viewMode === "grid" ? "active" : ""
            }`}
            onClick={() => {
              setViewMode("grid");
              localStorage.setItem("requestViewMode", "grid");
            }}
            title="Grid View"
          >
            <FiGrid />
          </button>
          <button
            className={`request-btn-view-toggle ${
              viewMode === "kanban" ? "active" : ""
            }`}
            onClick={() => {
              setViewMode("kanban");
              localStorage.setItem("requestViewMode", "kanban");
            }}
            title="Kanban View"
          >
            <FiColumns />
          </button>
          <button
            className="request-btn-refresh"
            onClick={refreshRequests}
            title="Refresh"
          >
            <FiRefreshCw />
          </button>
        </div>

        {viewMode === "grid" && (
          <div className="request-status-count">
            <button
              className={`request-status-btn btn-all ${
                selectedStatus === "" ? "active" : ""
              }`}
              onClick={() => {
                setSelectedStatus("");
                localStorage.removeItem("requestStatus");
              }}
            >
              All ({totalFilteredCount})
            </button>
            <button
              className={`request-status-btn btn-pending ${
                selectedStatus === "pending" ? "active" : ""
              }`}
              onClick={() => {
                setSelectedStatus("pending");
                localStorage.setItem("requestStatus", "pending");
              }}
            >
              Pending ({filteredStatusCounts.pending || 0})
            </button>
            <button
              className={`request-status-btn btn-reviewed ${
                selectedStatus === "reviewed" ? "active" : ""
              }`}
              onClick={() => {
                setSelectedStatus("reviewed");
                localStorage.setItem("requestStatus", "reviewed");
              }}
            >
              Reviewed ({filteredStatusCounts.reviewed || 0})
            </button>
            <button
              className={`request-status-btn btn-approved ${
                selectedStatus === "approved" ? "active" : ""
              }`}
              onClick={() => {
                setSelectedStatus("approved");
                localStorage.setItem("requestStatus", "approved");
              }}
            >
              Approved ({filteredStatusCounts.approved || 0})
            </button>
            <button
              className={`request-status-btn btn-progress ${
                selectedStatus === "in_progress" ? "active" : ""
              }`}
              onClick={() => {
                setSelectedStatus("in_progress");
                localStorage.setItem("requestStatus", "in_progress");
              }}
            >
              In Progress ({filteredStatusCounts.in_progress || 0})
            </button>
            <button
              className={`request-status-btn btn-done ${
                selectedStatus === "done" ? "active" : ""
              }`}
              onClick={() => {
                setSelectedStatus("done");
                localStorage.setItem("requestStatus", "done");
              }}
            >
              Done ({filteredStatusCounts.done || 0})
            </button>
            <button
              className={`request-status-btn btn-rejected ${
                selectedStatus === "rejected" ? "active" : ""
              }`}
              onClick={() => {
                setSelectedStatus("rejected");
                localStorage.setItem("requestStatus", "rejected");
              }}
            >
              Rejected ({filteredStatusCounts.rejected || 0})
            </button>
          </div>
        )}
      </div>

      {filteredRequests.length === 0 ? (
        <div className="request-empty">
          <p>No requests found matching your filters.</p>
        </div>
      ) : viewMode === "grid" ? (
        <>
          <div className="request-list">
            {currentItems.map((request) => (
              <div
                key={request.id}
                className="request-item"
                onClick={() => handleRequestClick(request.id)}
              >
                <div className="request-item-header">
                  <h3 className="request-item-title">{request.title}</h3>
                  <span
                    className={`status-badge ${getStatusClass(request.status)}`}
                  >
                    {request.status.replace("_", " ")}
                  </span>
                </div>
                <div className="request-item-info">
                  <p className="request-item-type">
                    <span className="request-item-label">Type:</span>{" "}
                    {request.request_type}
                  </p>
                  {request.platform_name && (
                    <p className="request-item-info">
                      <span className="request-item-label">Platform:</span>{" "}
                      {request.platform_name}
                    </p>
                  )}
                  {request.db_name && (
                    <p className="request-item-info">
                      <span className="request-item-label">Database:</span>{" "}
                      {request.db_name}
                    </p>
                  )}
                  {request.requester_id && (
                    <p className="request-item-info">
                      <span className="request-item-label">Requester:</span>{" "}
                      {getUserDisplayName(request.requester_id)}
                    </p>
                  )}
                  {request.due_date && (
                    <p className="request-item-date">
                      <span className="request-item-label">Due Date:</span>{" "}
                      {request.due_date}
                    </p>
                  )}
                </div>
                <div className="request-item-footer">
                  <span className="request-item-created">
                    Created: {new Date(request.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <>
          <div className="kanban-view">
            {/* Pending Column */}
            <div className="kanban-column pending">
              <div className="kanban-column-header">
                <h3 className="kanban-column-title">Pending</h3>
                <span className="kanban-column-count">
                  {filteredStatusCounts.pending || 0}
                </span>
              </div>
              <div className="kanban-column-content">
                {filteredRequests.filter((req) => req.status === "pending")
                  .length === 0 ? (
                  <div className="kanban-empty">No pending requests</div>
                ) : (
                  filteredRequests
                    .filter((req) => req.status === "pending")
                    .map((request) => (
                      <div
                        key={request.id}
                        className="kanban-item"
                        onClick={() => handleRequestClick(request.id)}
                      >
                        <h4 className="kanban-item-title">{request.title}</h4>
                        <div className="kanban-item-info">
                          <div>Type: {request.request_type}</div>
                          {request.platform_name && (
                            <div>Platform: {request.platform_name}</div>
                          )}
                          {request.db_name && (
                            <div>Database: {request.db_name}</div>
                          )}
                        </div>
                        <div className="kanban-item-info">
                          <div>Requester: {getUserDisplayName(request.requester_id)}</div>
                        </div>
                        <div className="kanban-item-footer">
                          <span>
                            Created:{" "}
                            {new Date(request.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>

            {/* Reviewed Column */}
            <div className="kanban-column reviewed">
              <div className="kanban-column-header">
                <h3 className="kanban-column-title">Reviewed</h3>
                <span className="kanban-column-count">
                  {filteredStatusCounts.reviewed || 0}
                </span>
              </div>
              <div className="kanban-column-content">
                {filteredRequests.filter((req) => req.status === "reviewed")
                  .length === 0 ? (
                  <div className="kanban-empty">No reviewed requests</div>
                ) : (
                  filteredRequests
                    .filter((req) => req.status === "reviewed")
                    .map((request) => (
                      <div
                        key={request.id}
                        className="kanban-item"
                        onClick={() => handleRequestClick(request.id)}
                      >
                        <h4 className="kanban-item-title">{request.title}</h4>
                        <div className="kanban-item-info">
                          <div>Type: {request.request_type}</div>
                          {request.platform_name && (
                            <div>Platform: {request.platform_name}</div>
                          )}
                          {request.db_name && (
                            <div>Database: {request.db_name}</div>
                          )}
                        </div>
                        <div className="kanban-item-info">
                          <div>Requester: {getUserDisplayName(request.requester_id)}</div>
                        </div>
                        <div className="kanban-item-footer">
                          <span>
                            Created:{" "}
                            {new Date(request.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>

            {/* Approved Column */}
            <div className="kanban-column approved">
              <div className="kanban-column-header">
                <h3 className="kanban-column-title">Approved</h3>
                <span className="kanban-column-count">
                  {filteredStatusCounts.approved || 0}
                </span>
              </div>
              <div className="kanban-column-content">
                {filteredRequests.filter((req) => req.status === "approved")
                  .length === 0 ? (
                  <div className="kanban-empty">No approved requests</div>
                ) : (
                  filteredRequests
                    .filter((req) => req.status === "approved")
                    .map((request) => (
                      <div
                        key={request.id}
                        className="kanban-item"
                        onClick={() => handleRequestClick(request.id)}
                      >
                        <h4 className="kanban-item-title">{request.title}</h4>
                        <div className="kanban-item-info">
                          <div>Type: {request.request_type}</div>
                          {request.platform_name && (
                            <div>Platform: {request.platform_name}</div>
                          )}
                          {request.db_name && (
                            <div>Database: {request.db_name}</div>
                          )}
                        </div>
                        <div className="kanban-item-info">
                          <div>Requester: {getUserDisplayName(request.requester_id)}</div>
                        </div>
                        <div className="kanban-item-footer">
                          <span>
                            Created:{" "}
                            {new Date(request.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>

            {/* In Progress Column */}
            <div className="kanban-column in_progress">
              <div className="kanban-column-header">
                <h3 className="kanban-column-title">In Progress</h3>
                <span className="kanban-column-count">
                  {filteredStatusCounts.in_progress || 0}
                </span>
              </div>
              <div className="kanban-column-content">
                {filteredRequests.filter((req) => req.status === "in_progress")
                  .length === 0 ? (
                  <div className="kanban-empty">No in-progress requests</div>
                ) : (
                  filteredRequests
                    .filter((req) => req.status === "in_progress")
                    .map((request) => (
                      <div
                        key={request.id}
                        className="kanban-item"
                        onClick={() => handleRequestClick(request.id)}
                      >
                        <h4 className="kanban-item-title">{request.title}</h4>
                        <div className="kanban-item-info">
                          <div>Type: {request.request_type}</div>
                          {request.platform_name && (
                            <div>Platform: {request.platform_name}</div>
                          )}
                          {request.db_name && (
                            <div>Database: {request.db_name}</div>
                          )}
                        </div>
                        <div className="kanban-item-info">
                          <div>Requester: {getUserDisplayName(request.requester_id)}</div>
                        </div>
                        <div className="kanban-item-footer">
                          <span>
                            Created:{" "}
                            {new Date(request.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>

            {/* Done Column */}
            <div className="kanban-column done">
              <div className="kanban-column-header">
                <h3 className="kanban-column-title">Done</h3>
                <span className="kanban-column-count">
                  {filteredStatusCounts.done || 0}
                </span>
              </div>
              <div className="kanban-column-content">
                {filteredRequests.filter((req) => req.status === "done")
                  .length === 0 ? (
                  <div className="kanban-empty">No completed requests</div>
                ) : (
                  filteredRequests
                    .filter((req) => req.status === "done")
                    .map((request) => (
                      <div
                        key={request.id}
                        className="kanban-item"
                        onClick={() => handleRequestClick(request.id)}
                      >
                        <h4 className="kanban-item-title">{request.title}</h4>
                        <div className="kanban-item-info">
                          <div>Type: {request.request_type}</div>
                          {request.platform_name && (
                            <div>Platform: {request.platform_name}</div>
                          )}
                          {request.db_name && (
                            <div>Database: {request.db_name}</div>
                          )}
                        </div>
                        <div className="kanban-item-info">
                          <div>Requester: {getUserDisplayName(request.requester_id)}</div>
                        </div>
                        <div className="kanban-item-footer">
                          <span>
                            Created:{" "}
                            {new Date(request.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>

            {/* Rejected Column */}
            <div className="kanban-column rejected">
              <div className="kanban-column-header">
                <h3 className="kanban-column-title">Rejected</h3>
                <span className="kanban-column-count">
                  {filteredStatusCounts.rejected || 0}
                </span>
              </div>
              <div className="kanban-column-content">
                {filteredRequests.filter((req) => req.status === "rejected")
                  .length === 0 ? (
                  <div className="kanban-empty">No rejected requests</div>
                ) : (
                  filteredRequests
                    .filter((req) => req.status === "rejected")
                    .map((request) => (
                      <div
                        key={request.id}
                        className="kanban-item"
                        onClick={() => handleRequestClick(request.id)}
                      >
                        <h4 className="kanban-item-title">{request.title}</h4>
                        <div className="kanban-item-info">
                          <div>Type: {request.request_type}</div>
                          {request.platform_name && (
                            <div>Platform: {request.platform_name}</div>
                          )}
                          {request.db_name && (
                            <div>Database: {request.db_name}</div>
                          )}
                        </div>
                        <div className="kanban-item-info">
                          <div>Requester: {getUserDisplayName(request.requester_id)}</div>
                        </div>
                        <div className="kanban-item-footer">
                          <span>
                            Created:{" "}
                            {new Date(request.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Pagination controls - only show in grid view */}
      {viewMode === "grid" && filteredRequests.length > 0 && (
        <div className="pagination-container">
          <div className="pagination-info">
            Showing {indexOfFirstItem + 1} -{" "}
            {Math.min(indexOfLastItem, filteredRequests.length)} of{" "}
            {filteredRequests.length} requests
          </div>
          <div className="pagination">
            <button
              className="pagination-btn"
              onClick={firstPage}
              disabled={currentPage === 1}
            >
              «
            </button>
            <button
              className="pagination-btn"
              onClick={prevPage}
              disabled={currentPage === 1}
            >
              <FiChevronLeft />
            </button>
            {[...Array(Math.min(5, totalPages))].map((_, index) => {
              let pageNumber;
              if (totalPages <= 5) {
                pageNumber = index + 1;
              } else if (currentPage <= 3) {
                pageNumber = index + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNumber = totalPages - 4 + index;
              } else {
                pageNumber = currentPage - 2 + index;
              }

              return (
                <button
                  key={pageNumber}
                  className={`pagination-btn ${
                    currentPage === pageNumber ? "active" : ""
                  }`}
                  onClick={() => paginate(pageNumber)}
                >
                  {pageNumber}
                </button>
              );
            })}
            <button
              className="pagination-btn"
              onClick={nextPage}
              disabled={currentPage === totalPages}
            >
              <FiChevronRight />
            </button>
            <button
              className="pagination-btn"
              onClick={lastPage}
              disabled={currentPage === totalPages}
            >
              »
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Requests;
