import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../../styles/PlatformSelect.css';

const PlatformSelect = () => {
  const navigate = useNavigate();

  // Temporarily disabled platform IDs
  const disabledPlatforms = ['sbv-report', 'zdslab', 'merchant-campaign'];

  // Define ZDS platforms with direct image imports
  const platforms = [
    {
      id: 'whitelist-tool',
      name: 'Whitelist Tool',
      description: 'Query and manage whitelist data',
      image: '/platform-icons/whitelist-tool.png',
      fallbackImage: '/platform-icons/whitelist.png'
    },
    {
      id: 'funnel-tool',
      name: 'Funnel Tool',
      description: 'Analyze funnel metrics and user journeys',
      image: '/platform-icons/funnel-tool.png',
      fallbackImage: '/platform-icons/funnel.png'
    },
    {
      id: 'zdslab',
      name: 'ZDSLab',
      description: 'Run SQL queries and data analysis',
      image: '/platform-icons/zdslab.png',
      fallbackImage: '/platform-icons/zdslab.png'
    },
    {
      id: 'ads-report',
      name: 'Ads Report',
      description: 'View and analyze advertising campaign data',
      image: '/platform-icons/ads-report.png',
      fallbackImage: '/platform-icons/ads.png'
    },
    {
      id: 'sbv-report',
      name: 'SBV Report',
      description: 'Access SBV reporting dashboards',
      image: '/platform-icons/sbv-report.png',
      fallbackImage: '/platform-icons/sbv.png'
    },
    {
      id: 'merchant-campaign',
      name: 'Merchant Campaign Config Tool',
      description: 'Configure and manage merchant campaigns',
      image: '/platform-icons/merchant-campaign.png',
      fallbackImage: '/platform-icons/configtool.png'
    }
  ];

  // Navigate to request form with selected platform
  const handleSelectPlatform = (platformId) => {
    // Check if platform is temporarily disabled
    if (disabledPlatforms.includes(platformId)) {
      return; // Do nothing for disabled platforms
    }

    navigate('/requests/platform', {
      state: {
        requestType: 'access-platform',
        platformId: platformId,
        platformName: platforms.find(p => p.id === platformId)?.name || ''
      }
    });
  };

  // Get appropriate fallback image for a platform
  const getFallbackImage = (platform) => {
    return platform.fallbackImage || '/platform-icons/default.svg';
  };

  return (
    <div className="platform-select-container">
      <div className="platform-select-header">
        <h2>Select ZDS Platform</h2>
        <p>Choose the specific platform you need access to</p>
      </div>

      <div className="platform-grid">
        {platforms.map((platform) => {
          const isDisabled = disabledPlatforms.includes(platform.id);
          return (
            <div
              key={platform.id}
              className={`platform-card ${isDisabled ? 'platform-card-disabled' : ''}`}
              onClick={() => handleSelectPlatform(platform.id)}
              style={{
                cursor: isDisabled ? 'not-allowed' : 'pointer',
                opacity: isDisabled ? 0.5 : 1
              }}
            >
            <div className="platform-image-container">
              <img
                src={platform.image}
                alt={platform.name}
                className="platform-image"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = getFallbackImage(platform);
                }}
              />
            </div>
            <div className="platform-content">
              <h3>{platform.name}</h3>
              <p>{platform.description}</p>
              {isDisabled && (
                <div className="platform-disabled-notice">
                  <small style={{ color: '#999', fontStyle: 'italic' }}>
                    Temporarily unavailable
                  </small>
                </div>
              )}
            </div>
          </div>
          );
        })}
      </div>

      <div className="platform-actions">
        <button
          className="btn-secondary"
          onClick={() => navigate('/requests/types')}
        >
          Back
        </button>
      </div>
    </div>
  );
};

export default PlatformSelect;