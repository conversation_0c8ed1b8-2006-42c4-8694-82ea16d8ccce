import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import apiService from '../../../services/api.service';
import { ENDPOINTS } from '../../../config/api.config';

// Mock the API service
jest.mock('../../../services/api.service');

// Mock the RequestDataFileSection component by importing it from RequestDetail
// Since it's not exported separately, we'll test it through the main component
import RequestDetail from '../RequestDetail';

// Mock react-router-dom
const mockNavigate = jest.fn();
const mockLocation = { search: '?id=123' };

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
}));

describe('RequestDataFileSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock the main request data
    apiService.get.mockImplementation((url) => {
      if (url.includes('/requests/get')) {
        return Promise.resolve({
          request_base: {
            id: 123,
            title: 'Test Adhoc Data Request',
            request_type: 'adhoc-data',
            status: 'approved',
            created_at: '2024-01-01T00:00:00Z',
            requester_id: 1,
            description: 'Test description'
          },
          request_logs: []
        });
      }
      
      if (url.includes('/files/by_request')) {
        return Promise.resolve([
          {
            id: 176,
            filename: '20250529_130611_59342788-28b6-4a14-87c9-94b457c4f169.json',
            original_filename: 'openapi (11).json',
            file_path: 'uploads/user_files/2/20250529_130611_59342788-28b6-4a14-87c9-94b457c4f169.json',
            file_size: 47783,
            file_size_mb: 0.05,
            mime_type: 'application/json',
            description: 'Adhoc data file: openapi (11).json',
            user_id: 2,
            request_id: 12,
            feedback_id: null,
            status: 'active',
            created_at: '2025-05-29T13:06:11.900144',
            updated_at: null
          },
          {
            id: 177,
            filename: 'test_data.csv',
            original_filename: 'test_data.csv',
            file_path: 'uploads/user_files/2/test_data.csv',
            file_size: 1024,
            file_size_mb: 0.001,
            mime_type: 'text/csv',
            description: 'Test CSV data file',
            user_id: 2,
            request_id: 12,
            feedback_id: null,
            status: 'active',
            created_at: '2025-05-29T14:06:11.900144',
            updated_at: null
          }
        ]);
      }
      
      return Promise.resolve([]);
    });
  });

  test('renders data files section for adhoc-data request type', async () => {
    await act(async () => {
      render(<RequestDetail />);
    });

    await waitFor(() => {
      expect(screen.getByText('Data Files (2)')).toBeInTheDocument();
    });

    // Check if files are displayed
    expect(screen.getByText('openapi (11).json')).toBeInTheDocument();
    expect(screen.getByText('test_data.csv')).toBeInTheDocument();
    
    // Check file metadata
    expect(screen.getByText('46.66 KB')).toBeInTheDocument();
    expect(screen.getByText('1 KB')).toBeInTheDocument();
    expect(screen.getByText('application/json')).toBeInTheDocument();
    expect(screen.getByText('text/csv')).toBeInTheDocument();
  });

  test('shows download all button', async () => {
    await act(async () => {
      render(<RequestDetail />);
    });

    await waitFor(() => {
      expect(screen.getByText('Download All')).toBeInTheDocument();
    });
  });

  test('shows individual download buttons for each file', async () => {
    await act(async () => {
      render(<RequestDetail />);
    });

    await waitFor(() => {
      const downloadButtons = screen.getAllByText('Download');
      expect(downloadButtons).toHaveLength(3); // 2 individual + 1 download all
    });
  });

  test('does not render data files section for non-adhoc-data request types', async () => {
    // Mock different request type
    apiService.get.mockImplementation((url) => {
      if (url.includes('/requests/get')) {
        return Promise.resolve({
          request_base: {
            id: 123,
            title: 'Test Access Report Request',
            request_type: 'access-report',
            status: 'approved',
            created_at: '2024-01-01T00:00:00Z',
            requester_id: 1,
            description: 'Test description'
          },
          request_logs: []
        });
      }
      return Promise.resolve([]);
    });

    await act(async () => {
      render(<RequestDetail />);
    });

    await waitFor(() => {
      expect(screen.queryByText('Data Files')).not.toBeInTheDocument();
    });
  });

  test('shows empty state when no files are available', async () => {
    // Mock empty files response
    apiService.get.mockImplementation((url) => {
      if (url.includes('/requests/get')) {
        return Promise.resolve({
          request_base: {
            id: 123,
            title: 'Test Adhoc Data Request',
            request_type: 'adhoc-data',
            status: 'approved',
            created_at: '2024-01-01T00:00:00Z',
            requester_id: 1,
            description: 'Test description'
          },
          request_logs: []
        });
      }
      
      if (url.includes('/files/by_request')) {
        return Promise.resolve([]);
      }
      
      return Promise.resolve([]);
    });

    await act(async () => {
      render(<RequestDetail />);
    });

    await waitFor(() => {
      expect(screen.getByText('No data files uploaded for this request')).toBeInTheDocument();
    });
  });

  test('shows loading state initially', async () => {
    // Mock delayed response
    apiService.get.mockImplementation((url) => {
      if (url.includes('/requests/get')) {
        return Promise.resolve({
          request_base: {
            id: 123,
            title: 'Test Adhoc Data Request',
            request_type: 'adhoc-data',
            status: 'approved',
            created_at: '2024-01-01T00:00:00Z',
            requester_id: 1,
            description: 'Test description'
          },
          request_logs: []
        });
      }
      
      if (url.includes('/files/by_request')) {
        return new Promise(resolve => setTimeout(() => resolve([]), 1000));
      }
      
      return Promise.resolve([]);
    });

    await act(async () => {
      render(<RequestDetail />);
    });

    // Should show loading state initially
    expect(screen.getByText('Loading data files...')).toBeInTheDocument();
  });
});
