import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import apiService from "../../services/api.service";
import { FiAlertCircle } from "react-icons/fi";
import { ENDPOINTS } from "../../config/api.config";
import "../../styles/CreateRequest.css";

const RequestDatabase = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [users, setUsers] = useState([]);
  const [approverOptions, setApproverOptions] = useState([]);
  const [executorOptions, setExecutorOptions] = useState([]);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [createdRequest, setCreatedRequest] = useState(null);

  // Database options
  const dbOptions = [
    { value: "mysql-997", label: "MySQL 997" },
    { value: "mysql-998", label: "MySQL 998" },
    { value: "postgresql-997", label: "PostgreSQL 997" },
  ];

  const platformId = location.state?.platformId || "";
  const platformName = location.state?.platformName || "";

  const [formData, setFormData] = useState({
    title: null,
    request_type: "access-database",
    description: null,
    db_name: null,
    data_needed: null,
    due_date: null,
    approver_id: null,
    executor_id: "",
  });

  // Fetch users for dropdown selections (approver and executor)
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await apiService.get(
          ENDPOINTS.USERS.GET_ALL("dataquest")
        );
        if (response && Array.isArray(response.users)) {
          setUsers(response.users);
        }
      } catch (err) {
        console.error("Failed to fetch users:", err);
        setError("Failed to load user data");
      }
    };

    fetchUsers();
  }, []);

  useEffect(() => {
    const fetchApproversExecutors = async () => {
      try {
        const response = await apiService.get(ENDPOINTS.CONFIG.GET);
        const platformValue = formData.platform_name || platformId;
        // Approvers
        const approvers = (response || [])
          .filter(
            (item) =>
              item.request_type === "access-database" &&
              // item.db_name === platformValue &&
              item.role === "approver"
          )
          .map((item) => ({
            user_id: item.user_id,
            display_name: item.display_name,
          }));
        setApproverOptions(approvers);
        // Set default approver if not set
        if (approvers.length > 0 && !formData.approver_id) {
          setFormData((prev) => ({
            ...prev,
            approver_id: approvers[0].user_id,
          }));
        }
        // Executors
        const executors = (response || [])
          .filter(
            (item) =>
              item.request_type === "access-database" &&
              // item.db_name === platformValue &&
              item.role === "executor"
          )
          .map((item) => ({
            user_id: item.user_id,
            display_name: item.display_name,
          }));
        setExecutorOptions(executors);
        // Set default executor if not set
        if (executors.length > 0 && !formData.executor_id) {
          setFormData((prev) => ({
            ...prev,
            executor_id: executors[0].user_id,
          }));
        }
      } catch (err) {
        setApproverOptions([]);
        setExecutorOptions([]);
      }
    };
    fetchApproversExecutors();
    // eslint-disable-next-line
  }, [formData.platform_name, platformId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Format the data before submission
      const requestData = {
        ...formData,
      };

      // Add approver_id if provided
      if (formData.approver_id) {
        requestData.approver_id = parseInt(formData.approver_id, 10);
      }

      // Add executor_id if provided
      if (formData.executor_id) {
        requestData.executor_id = parseInt(formData.executor_id, 10);
      }

      // If due_date is empty, don't send it to avoid validation errors
      if (!requestData.due_date) {
        delete requestData.due_date;
      }

      const response = await apiService.post(
        ENDPOINTS.REQUEST.CREATE,
        requestData
      );
      setCreatedRequest(response);
      setShowSuccessModal(true);
    } catch (err) {
      console.error("Error creating request:", err);
      if (err.response && err.response.data) {
        // Format and display validation errors
        const errorDetails = err.response.data.detail;
        if (Array.isArray(errorDetails)) {
          setError(
            errorDetails
              .map((detail) => `${detail.msg} for ${detail.loc[1]}`)
              .join(", ")
          );
        } else {
          setError(err.response.data.message || "Failed to create request");
        }
      } else {
        setError("An unexpected error occurred");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="request-page-container">
      <div className="request-form-container">
        <h2>Request Database Access</h2>

        {error && (
          <div className="error-message">
            <FiAlertCircle />
            <span>{error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="title">Title*</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="db_name">Database*</label>
            <select
              id="db_name"
              name="db_name"
              value={formData.db_name}
              onChange={handleChange}
              required
            >
              <option value="">Select a Database</option>
              {dbOptions.map((db) => (
                <option key={db.value} value={db.value}>
                  {db.label}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={4}
              placeholder="Additional context or information about your request"
              style={{ maxWidth: "100%", minWidth: "100%" }}
            />
          </div>

          <div className="form-group">
            <label htmlFor="data_needed">Data Needed</label>
            <textarea
              id="data_needed"
              name="data_needed"
              value={formData.data_needed}
              onChange={handleChange}
              rows={6}
              placeholder="Please describe in detail what data you need, including relevant schemas, tables, metrics, time period, etc."
              style={{ maxWidth: "100%", minWidth: "100%" }}
            />
          </div>

          <div className="form-group">
            <label htmlFor="due_date">Due Date (optional)</label>
            <input
              type="date"
              id="due_date"
              name="due_date"
              value={formData.due_date || ""}
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label htmlFor="approver_id">Approver (optional)</label>
            <select
              id="approver_id"
              name="approver_id"
              value={formData.approver_id}
              onChange={handleChange}
            >
              <option value="">Select an Approver</option>
              {approverOptions.map((user, idx) => (
                <option
                  key={`approver-${user.user_id}-${idx}`}
                  value={user.user_id}
                >
                  {user.display_name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="executor_id">Executor (optional)</label>
            <select
              id="executor_id"
              name="executor_id"
              value={formData.executor_id}
              onChange={handleChange}
            >
              <option value="">Select an Executor</option>
              {executorOptions.map((user, idx) => (
                <option
                  key={`executor-${user.user_id}-${idx}`}
                  value={user.user_id}
                >
                  {user.display_name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="btn-secondary"
              onClick={() => navigate("/requests/types")}
            >
              Back
            </button>
            <button type="submit" className="btn-primary" disabled={loading}>
              {loading ? "Creating..." : "Create Request"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RequestDatabase;
