import React from 'react';

const RequestGuide = ({ requestType, platformName }) => {
  const getGuideContent = () => {
    switch (requestType) {
      case 'access-platform':
        return {
          title: `How to request access to ${platformName}`,
          steps: [
            {
              title: 'Title',
              description: 'Provide a clear and concise title for your request. Include the platform name and purpose.'
            },
            {
              title: 'Description',
              description: 'Explain why you need access to this platform. Include specific use cases and expected outcomes.'
            },
            {
              title: 'Approver',
              description: 'Select the appropriate approver for your request. This should be someone who can verify your need for access.'
            },
            {
              title: 'Due Date',
              description: 'Specify when you need access by. This helps prioritize your request.'
            }
          ]
        };
      case 'access-database':
        return {
          title: 'How to request database access',
          steps: [
            {
              title: 'Database Name',
              description: 'Specify which database you need access to.'
            },
            {
              title: 'Access Level',
              description: 'Choose the appropriate access level based on your needs: read-only, read-write, or full access.'
            },
            {
              title: 'Description',
              description: 'Explain why you need database access and what data you will be working with.'
            }
          ]
        };
      case 'adhoc-data':
        return {
          title: 'How to request adhoc data',
          steps: [
            {
              title: 'Data Needed',
              description: 'Clearly describe the data you need, including specific fields, time ranges, and any filters.'
            },
            {
              title: 'Purpose',
              description: 'Explain how you will use this data and what insights you hope to gain.'
            },
            {
              title: 'Due Date',
              description: 'Specify when you need the data by.'
            }
          ]
        };
      default:
        return {
          title: 'How to create a request',
          steps: [
            {
              title: 'Basic Information',
              description: 'Fill in all required fields with accurate information.'
            },
            {
              title: 'Review',
              description: 'Double-check your request before submitting to ensure all information is correct.'
            }
          ]
        };
    }
  };

  const guide = getGuideContent();

  return (
    <div className="request-guide">
      <h3>{guide.title}</h3>
      <div className="guide-steps">
        {guide.steps.map((step, index) => (
          <div key={index} className="guide-step">
            <div className="step-number">{index + 1}</div>
            <div className="step-content">
              <h4>{step.title}</h4>
              <p>{step.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RequestGuide; 