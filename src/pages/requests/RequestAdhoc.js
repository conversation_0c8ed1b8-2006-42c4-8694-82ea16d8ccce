import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import apiService from "../../services/api.service";
import { ENDPOINTS } from "../../config/api.config";
import { FiAlertCircle } from "react-icons/fi";
import "../../styles/CreateRequest.css";
import BasicQuillEditor from "../../components/BasicQuillEditor";

const RequestAdhoc = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const errorTimeoutRef = useRef(null);
  const [approverOptions, setApproverOptions] = useState([]);
  const [executorOptions, setExecutorOptions] = useState([]);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [createdRequest, setCreatedRequest] = useState(null);

  const [formData, setFormData] = useState({
    title: null,
    request_type: "adhoc-data",
    description: null,
    data_needed: null,
    due_date: null,
    approver_id: null,
    executor_id: null,
  });

  // Cleanup function for error timeout
  useEffect(() => {
    return () => {
      // Clear any pending timeouts when component unmounts
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
      }
    };
  }, []);

  // Fetch approvers & executors for dropdowns based on platform
  useEffect(() => {
    const fetchApproversExecutors = async () => {
      try {
        const response = await apiService.get(ENDPOINTS.CONFIG.GET);
        // const platformValue = formData.platform_name || platformId;
        // Approvers
        const approvers = (response || [])
          .filter(
            (item) =>
              item.request_type === "adhoc-data" &&
              // item.platform === platformValue &&
              item.role === "approver"
          )
          .map((item) => ({
            user_id: item.user_id,
            display_name: item.display_name,
          }));
        setApproverOptions(approvers);
        console.log("Response:", approvers);
        // Set default approver if not set
        if (approvers.length > 0 && !formData.approver_id) {
          setFormData((prev) => ({
            ...prev,
            approver_id: approvers[0].user_id,
          }));
        }
        // Executors
        const executors = (response || [])
          .filter(
            (item) =>
              item.request_type === "adhoc-data" &&
              // item.platform === platformValue &&
              item.role === "executor"
          )
          .map((item) => ({
            user_id: item.user_id,
            display_name: item.display_name,
          }));
        setExecutorOptions(executors);
        // Executor is optional, so we don't set a default value
        // This allows the "Select an Executor" option to be the default
      } catch (err) {
        console.error("Failed to fetch approvers and executors:", err);
        setApproverOptions([]);
        setExecutorOptions([]);
        setErrorWithTimeout("Failed to load approvers and executors");
      }
    };
    fetchApproversExecutors();
    // eslint-disable-next-line
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Function to set error with auto-hide timeout
  const setErrorWithTimeout = (errorMessage, timeout = 3000) => {
    // Clear any existing timeout
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
    }

    // Set the error message
    setError(errorMessage);

    // Set a timeout to clear the error after specified time
    if (errorMessage) {
      errorTimeoutRef.current = setTimeout(() => {
        setError(null);
      }, timeout);
    }
  };

  // Validate form fields before submission
  const validateForm = () => {
    const missingFields = [];

    // Check required fields
    if (!formData.title) {
      missingFields.push("Title");
    }

    if (!formData.description || formData.description === '<p><br></p>') {
      missingFields.push("Description");
    }

    if (!formData.approver_id) {
      missingFields.push("Approver");
    }

    if (missingFields.length > 0) {
      setErrorWithTimeout(`Please fill in all required fields: ${missingFields.join(", ")}`);
      return false;
    }

    setError(null);
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Clear any existing error
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
      errorTimeoutRef.current = null;
    }
    setError(null);

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Format the data before submission
      const requestData = {
        ...formData,
      };

      // Add approver_id if provided
      if (formData.approver_id) {
        requestData.approver_id = parseInt(formData.approver_id, 10);
      }

      // Add executor_id if provided
      if (formData.executor_id) {
        requestData.executor_id = parseInt(formData.executor_id, 10);
      }

      // If due_date is empty, don't send it to avoid validation errors
      if (!requestData.due_date) {
        delete requestData.due_date;
      }

      const response = await apiService.post(
        ENDPOINTS.REQUEST.CREATE,
        requestData
      );
      setCreatedRequest(response);
      setShowSuccessModal(true);
    } catch (err) {
      console.error("Error creating request:", err);
      if (err.response && err.response.data) {
        // Format and display validation errors
        const errorDetails = err.response.data.detail;
        if (Array.isArray(errorDetails)) {
          setErrorWithTimeout(
            errorDetails
              .map((detail) => `${detail.msg} for ${detail.loc[1]}`)
              .join(", ")
          );
        } else {
          setErrorWithTimeout(err.response.data.message || "Failed to create request");
        }
      } else {
        setErrorWithTimeout("An unexpected error occurred");
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to handle closing the success modal
  // const handleCloseSuccessModal = () => {
  //   setShowSuccessModal(false);
  //   navigate('/requests');
  // };

  return (
    <div className="request-page-container">
      <div className="request-form-container">
        <h2>Request Adhoc Data</h2>

        {error && (
          <div className="error-message">
            <FiAlertCircle />
            <span>{error}</span>
          </div>
        )}

        {/* Success Modal */}
        {showSuccessModal && (
          <div className="modal-overlay">
            <div className="modal-content success-modal">
              <h3>Request Created Successfully!</h3>
              <p>Your request has been submitted successfully.</p>
              {createdRequest && (
                <div className="request-details">
                  <p>
                    <strong>Request ID:</strong> {createdRequest.id}
                  </p>
                  <p>
                    <strong>Title:</strong> {createdRequest.title}
                  </p>
                  <p>
                    <strong>Status:</strong> {createdRequest.status}
                  </p>
                </div>
              )}
              {/* <button
                className="btn-primary"
                onClick={handleCloseSuccessModal}
              >
                View All Requests
              </button> */}
              <button
                className="btn-primary"
                onClick={() => {
                  setShowSuccessModal(false);
                  navigate(`/requests/detail?id=${createdRequest?.id}`);
                }}
              >
                Request Tracking
              </button>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="title">Title*</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
            />
          </div>

          {/* <div className="form-group">
            <label htmlFor="data_needed">Data Needed*</label>
            <textarea
              id="data_needed"
              name="data_needed"
              value={formData.data_needed}
              onChange={handleChange}
              rows={5}
              required
              placeholder="Please describe in detail what data you need, including relevant dimensions, metrics, time period, etc."
            />
          </div> */}

          <div className="form-group">
            <label htmlFor="description">Description*</label>
            <BasicQuillEditor
              value={formData.description || ""}
              onChange={(content) =>
                setFormData({ ...formData, description: content })
              }
              placeholder="Please describe in detail what data you need, including relevant dimensions, metrics, time period, etc."
            />
          </div>

          <div className="form-group">
            <label htmlFor="due_date">Due Date (optional)</label>
            <input
              type="date"
              id="due_date"
              name="due_date"
              value={formData.due_date || ""}
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label htmlFor="approver_id">Approver*</label>
            <select
              id="approver_id"
              name="approver_id"
              value={formData.approver_id}
              onChange={handleChange}
              required
            >
              <option value="">Select an Approver</option>
              {approverOptions.map((user, idx) => (
                <option
                  key={`approver-${user.user_id}-${idx}`}
                  value={user.user_id}
                >
                  {user.display_name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="executor_id">Executor (optional)</label>
            <select
              id="executor_id"
              name="executor_id"
              value={formData.executor_id}
              onChange={handleChange}
            >
              <option>Select an Executor</option>
              {executorOptions.map((user, idx) => (
                <option
                  key={`executor-${user.user_id}-${idx}`}
                  value={user.user_id}
                >
                  {user.display_name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="btn-secondary"
              onClick={() => navigate("/requests/types")}
            >
              Back
            </button>
            <button type="submit" className="btn-primary" disabled={loading}>
              {loading ? "Creating..." : "Create Request"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RequestAdhoc;
