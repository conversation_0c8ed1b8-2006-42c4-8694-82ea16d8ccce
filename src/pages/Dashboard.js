import React from "react";
import "../styles/Dashboard.css";
import {
  FiClock,
  FiCheckCircle,
  FiDatabase,
  FiBarChart2,
  FiPlusCircle,
  FiAlertCircle,
  FiFileText,
  FiServer,
  FiLayers,
} from "react-icons/fi";
import { BiTimeFive } from "react-icons/bi";

const Dashboard = ({ navigateToTickets }) => {
  // Handle card click
  const handleStatCardClick = (status) => {
    navigateToTickets(status);
  };

  // Handle quick request buttons
  const handleQuickRequest = (type) => {
    // Navigate to the appropriate request form based on type
    switch (type) {
      case "data":
        window.location.href = "/requests/adhoc";
        break;
      case "db":
        window.location.href = "/requests/database";
        break;
      case "report":
        window.location.href = "/requests/report";
        break;
      case "platform":
        window.location.href = "/requests/platform";
        break;
      default:
        window.location.href = "/requests/types";
    }
  };

  return (
    <div className="dashboard-container">
      {/* Summary Cards */}
      <div className="summary-cards">
        <div
          className="summary-card pending-card"
          onClick={() => handleStatCardClick("pending")}
        >
          <div className="card-content">
            <div className="card-title">Pending Requests</div>
            <div className="card-number">12</div>
          </div>
          <div className="card-icon">
            <FiClock />
          </div>
        </div>

        <div
          className="summary-card approved-card"
          onClick={() => handleStatCardClick("approved")}
        >
          <div className="card-content">
            <div className="card-title">Approved This Month</div>
            <div className="card-number">24</div>
          </div>
          <div className="card-icon">
            <FiCheckCircle />
          </div>
        </div>

        <div
          className="summary-card data-sources-card"
          onClick={() => navigateToTickets("all")}
        >
          <div className="card-content">
            <div className="card-title">Data Sources</div>
            <div className="card-number">8</div>
          </div>
          <div className="card-icon">
            <FiDatabase />
          </div>
        </div>

        <div
          className="summary-card reports-card"
          onClick={() => navigateToTickets("all")}
        >
          <div className="card-content">
            <div className="card-title">Tableau Reports</div>
            <div className="card-number">15</div>
          </div>
          <div className="card-icon">
            <FiBarChart2 />
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="section-container">
        <h2 className="section-title">Recent Activity</h2>
        <div className="activity-list">
          <div className="activity-item">
            <div className="activity-icon approved">
              <FiCheckCircle />
            </div>
            <div className="activity-content">
              <div className="activity-title">Request #REQ-1002 approved</div>
              <div className="activity-description">
                Access to Sales Database granted to Marketing Team
              </div>
            </div>
            <div className="activity-time">2 hours ago</div>
          </div>

          <div className="activity-item">
            <div className="activity-icon new">
              <FiPlusCircle />
            </div>
            <div className="activity-content">
              <div className="activity-title">New data source added</div>
              <div className="activity-description">
                Web Analytics (BigQuery) now available
              </div>
            </div>
            <div className="activity-time">1 day ago</div>
          </div>

          <div className="activity-item">
            <div className="activity-icon pending">
              <FiAlertCircle />
            </div>
            <div className="activity-content">
              <div className="activity-title">
                Request #REQ-1001 requires review
              </div>
              <div className="activity-description">
                Customer segmentation data request pending approval
              </div>
            </div>
            <div className="activity-time">3 days ago</div>
          </div>
        </div>
      </div>

      {/* Quick Actions and System Status */}
      <div className="dashboard-grid">
        {/* Quick Requests */}
        <div className="section-container">
          <h2 className="section-title">Quick Requests</h2>
          <div className="quick-actions">
            <button
              className="quick-action-btn data-btn"
              onClick={() => handleQuickRequest("data")}
            >
              <FiDatabase className="btn-icon" />
              <span>Request Ad-hoc Data</span>
            </button>
            <button
              className="quick-action-btn report-btn"
              onClick={() => handleQuickRequest("report")}
            >
              <FiFileText className="btn-icon" />
              <span>Request Atlas Report Access</span>
            </button>
            <button
              className="quick-action-btn platform-btn"
              onClick={() => handleQuickRequest("platform")}
            >
              <FiLayers className="btn-icon" />
              <span>Request Platform Access</span>
            </button>
            <button
              className="quick-action-btn data-btn"
              onClick={() => handleQuickRequest("data")}
            >
              <FiDatabase className="btn-icon" />
              <span>Request Database Access</span>
            </button>
          </div>
        </div>

        {/* My Access */}
        <div className="section-container">
          <h2 className="section-title">My Access</h2>
          <div className="access-section">
            <h3 className="subsection-title">Data Sources</h3>
            <div className="access-tags">
              <span className="access-tag">Sales DB</span>
              <span className="access-tag">CRM</span>
              <span className="access-tag">Web Analytics</span>
            </div>
          </div>
          <div className="access-section">
            <h3 className="subsection-title">Tableau Reports</h3>
            <div className="access-tags">
              <span className="access-tag">Sales Perf</span>
              <span className="access-tag">Cust Seg</span>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="section-container">
          <h2 className="section-title">System Status</h2>
          <div className="system-status">
            <div className="status-overview">
              <div className="status-icon operational">
                <FiCheckCircle />
              </div>
              <div className="status-text">
                <div className="status-title">All systems operational</div>
                <div className="status-time">Last checked 5 minutes ago</div>
              </div>
            </div>

            <div className="status-details">
              <div className="status-item">
                <div className="status-label">Database Server</div>
                <div className="status-bar">
                  <div
                    className="status-progress"
                    style={{ width: "100%" }}
                  ></div>
                </div>
                <div className="status-value">Online</div>
              </div>

              <div className="status-item">
                <div className="status-label">BI Platform</div>
                <div className="status-bar">
                  <div
                    className="status-progress"
                    style={{ width: "100%" }}
                  ></div>
                </div>
                <div className="status-value">Online</div>
              </div>

              <div className="status-item">
                <div className="status-label">ETL Service</div>
                <div className="status-bar">
                  <div
                    className="status-progress"
                    style={{ width: "100%" }}
                  ></div>
                </div>
                <div className="status-value">Online</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
