import React, { useState, useRef, useEffect } from 'react';
import { FiSend, FiRefreshCw } from 'react-icons/fi';
import '../styles/AskAI.css';
import { useTheme } from '../context/ThemeContext';

const AskAI = () => {
  const { theme, language } = useTheme();
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'ai',
      content: language === 'en' 
        ? 'Hello! I\'m your AI assistant. How can I help you today?' 
        : 'Xin chào! Tôi là trợ lý AI của bạn. Tôi có thể giúp gì cho bạn hôm nay?',
      timestamp: new Date(),
    },
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Focus input on component mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleInputChange = (e) => {
    setInputMessage(e.target.value);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSendMessage = () => {
    if (inputMessage.trim() === '') return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      type: 'user',
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI response after a delay
    setTimeout(() => {
      const aiResponse = {
        id: messages.length + 2,
        type: 'ai',
        content: getAIResponse(inputMessage),
        timestamp: new Date(),
      };
      setMessages((prevMessages) => [...prevMessages, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const getAIResponse = (userMessage) => {
    // Simple placeholder responses
    const responses = {
      en: [
        "I'm analyzing your request...",
        "That's an interesting question. Let me think about it.",
        "I understand your query. Here's what I found...",
        "Based on the information available, I can tell you that...",
        "I'm here to help! Let me know if you need more information.",
        "I'm still learning, but I'll do my best to assist you.",
      ],
      vi: [
        "Tôi đang phân tích yêu cầu của bạn...",
        "Đó là một câu hỏi thú vị. Hãy để tôi suy nghĩ về nó.",
        "Tôi hiểu câu hỏi của bạn. Đây là những gì tôi tìm thấy...",
        "Dựa trên thông tin có sẵn, tôi có thể nói với bạn rằng...",
        "Tôi ở đây để giúp đỡ! Hãy cho tôi biết nếu bạn cần thêm thông tin.",
        "Tôi vẫn đang học hỏi, nhưng tôi sẽ cố gắng hết sức để hỗ trợ bạn.",
      ]
    };

    const currentLangResponses = responses[language] || responses.en;
    const randomIndex = Math.floor(Math.random() * currentLangResponses.length);
    return currentLangResponses[randomIndex];
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const clearConversation = () => {
    setMessages([
      {
        id: 1,
        type: 'ai',
        content: language === 'en' 
          ? 'Hello! I\'m your AI assistant. How can I help you today?' 
          : 'Xin chào! Tôi là trợ lý AI của bạn. Tôi có thể giúp gì cho bạn hôm nay?',
        timestamp: new Date(),
      },
    ]);
  };

  return (
    <div className="askai-container">
      <div className="askai-header">
        <h2>{language === 'en' ? 'Ask AI Assistant' : 'Hỏi Trợ Lý AI'}</h2>
        <button 
          className="clear-conversation-btn" 
          onClick={clearConversation}
          title={language === 'en' ? 'Clear conversation' : 'Xóa cuộc trò chuyện'}
        >
          <FiRefreshCw />
          {language === 'en' ? 'Clear conversation' : 'Xóa cuộc trò chuyện'}
        </button>
      </div>
      
      <div className="chat-container">
        <div className="messages-container">
          {messages.map((message) => (
            <div 
              key={message.id} 
              className={`message ${message.type === 'user' ? 'user-message' : 'ai-message'}`}
            >
              <div className="message-content">
                {message.content}
              </div>
              <div className="message-timestamp">
                {formatTimestamp(message.timestamp)}
              </div>
            </div>
          ))}
          {isTyping && (
            <div className="message ai-message">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
        
        <div className="input-container">
          <textarea
            ref={inputRef}
            className="message-input"
            placeholder={language === 'en' ? 'Type your message here...' : 'Nhập tin nhắn của bạn tại đây...'}
            value={inputMessage}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            rows={1}
          />
          <button 
            className="send-button" 
            onClick={handleSendMessage}
            disabled={inputMessage.trim() === ''}
          >
            <FiSend />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AskAI;
