<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Access Management Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
        }
        .avatar-upload {
            position: relative;
            max-width: 200px;
            margin: 0 auto;
        }
        .avatar-upload .avatar-edit {
            position: absolute;
            right: 10px;
            bottom: 10px;
        }
        .avatar-upload .avatar-edit input {
            display: none;
        }
        .avatar-upload .avatar-edit label {
            display: inline-block;
            width: 34px;
            height: 34px;
            margin-bottom: 0;
            border-radius: 100%;
            background: #FFFFFF;
            border: 1px solid #d2d6dc;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
            cursor: pointer;
            font-weight: normal;
            transition: all 0.2s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .avatar-upload .avatar-preview {
            width: 150px;
            height: 150px;
            position: relative;
            border-radius: 100%;
            border: 6px solid #F8F9FC;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
        }
        .avatar-upload .avatar-preview > div {
            width: 100%;
            height: 100%;
            border-radius: 100%;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="sidebar bg-indigo-800 text-white w-64 flex-shrink-0">
            <div class="p-4 flex items-center space-x-3 border-b border-indigo-700">
                <div class="bg-white text-indigo-800 p-2 rounded-lg">
                    <i class="fas fa-database text-2xl"></i>
                </div>
                <h1 class="text-xl font-bold">DataPortal</h1>
            </div>
            <nav class="p-4">
                <div class="mb-8">
                    <h3 class="text-xs uppercase tracking-wider text-indigo-400 mb-4">Main Menu</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-indigo-700 text-white tab-link" data-tab="dashboard">
                                <i class="fas fa-tachometer-alt w-6"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-indigo-700 text-white tab-link" data-tab="profile">
                                <i class="fas fa-user-circle w-6"></i>
                                <span>Profile</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-indigo-700 text-white tab-link" data-tab="settings">
                                <i class="fas fa-cog w-6"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-indigo-700 text-white tab-link" data-tab="users">
                                <i class="fas fa-users-cog w-6"></i>
                                <span>User Management</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xs uppercase tracking-wider text-indigo-400 mb-4">Data Access</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="#adhoc" class="tab-link flex items-center space-x-3 p-2 rounded-lg hover:bg-indigo-700 text-white" data-tab="adhoc">
                                <i class="fas fa-bolt w-6"></i>
                                <span>Ad-Hoc Requests</span>
                            </a>
                        </li>
                        <li>
                            <a href="#datasource" class="tab-link flex items-center space-x-3 p-2 rounded-lg hover:bg-indigo-700 text-white" data-tab="datasource">
                                <i class="fas fa-database w-6"></i>
                                <span>Data Sources</span>
                            </a>
                        </li>
                        <li>
                            <a href="#tableau" class="tab-link flex items-center space-x-3 p-2 rounded-lg hover:bg-indigo-700 text-white" data-tab="tableau">
                                <i class="fas fa-chart-bar w-6"></i>
                                <span>Tableau Reports</span>
                            </a>
                        </li>
                        <li>
                            <a href="#platform" class="tab-link flex items-center space-x-3 p-2 rounded-lg hover:bg-indigo-700 text-white" data-tab="platform">
                                <i class="fas fa-server w-6"></i>
                                <span>Platform Access</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm p-4 flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 focus:outline-none lg:hidden">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h2 class="text-xl font-semibold text-gray-800">Data Access Management</h2>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button class="text-gray-500 focus:outline-none">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="notification-badge bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                    </div>
                    <div class="flex items-center space-x-2">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User" class="w-8 h-8 rounded-full">
                        <span class="text-gray-700">Sarah Johnson</span>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <main class="p-6">
                <!-- Dashboard Content -->
                <div id="dashboard" class="tab-content active">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">Pending Requests</p>
                                    <h3 class="text-2xl font-bold text-indigo-600">12</h3>
                                </div>
                                <div class="bg-indigo-100 p-3 rounded-full">
                                    <i class="fas fa-clock text-indigo-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">Approved This Month</p>
                                    <h3 class="text-2xl font-bold text-green-600">24</h3>
                                </div>
                                <div class="bg-green-100 p-3 rounded-full">
                                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">Data Sources</p>
                                    <h3 class="text-2xl font-bold text-blue-600">8</h3>
                                </div>
                                <div class="bg-blue-100 p-3 rounded-full">
                                    <i class="fas fa-database text-blue-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">Tableau Reports</p>
                                    <h3 class="text-2xl font-bold text-purple-600">15</h3>
                                </div>
                                <div class="bg-purple-100 p-3 rounded-full">
                                    <i class="fas fa-chart-pie text-purple-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
                        </div>
                        <div class="divide-y divide-gray-200">
                            <div class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-green-100 p-2 rounded-full text-green-600">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">Request #REQ-1002 approved</p>
                                        <p class="text-sm text-gray-500">Access to Sales Database granted to Marketing Team</p>
                                    </div>
                                    <div class="ml-auto text-sm text-gray-500">2 hours ago</div>
                                </div>
                            </div>
                            <div class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-blue-100 p-2 rounded-full text-blue-600">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">New data source added</p>
                                        <p class="text-sm text-gray-500">Web Analytics (BigQuery) now available</p>
                                    </div>
                                    <div class="ml-auto text-sm text-gray-500">1 day ago</div>
                                </div>
                            </div>
                            <div class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-yellow-100 p-2 rounded-full text-yellow-600">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">Request #REQ-1001 requires review</p>
                                        <p class="text-sm text-gray-500">Customer segmentation data request pending approval</p>
                                    </div>
                                    <div class="ml-auto text-sm text-gray-500">3 days ago</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow overflow-hidden">
                            <div class="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
                                <h3 class="text-lg font-medium text-indigo-800">Quick Requests</h3>
                            </div>
                            <div class="p-6">
                                <button class="w-full mb-3 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2">
                                    <i class="fas fa-database"></i>
                                    <span>Request Data Access</span>
                                </button>
                                <button class="w-full mb-3 bg-white border border-indigo-600 text-indigo-600 hover:bg-indigo-50 px-4 py-2 rounded-lg flex items-center justify-center space-x-2">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Request Report Access</span>
                                </button>
                                <button class="w-full bg-white border border-indigo-600 text-indigo-600 hover:bg-indigo-50 px-4 py-2 rounded-lg flex items-center justify-center space-x-2">
                                    <i class="fas fa-server"></i>
                                    <span>Request Platform Access</span>
                                </button>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow overflow-hidden">
                            <div class="px-6 py-4 bg-blue-50 border-b border-blue-100">
                                <h3 class="text-lg font-medium text-blue-800">My Access</h3>
                            </div>
                            <div class="p-6">
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Data Sources</h4>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Sales DB</span>
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">CRM</span>
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Web Analytics</span>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Tableau Reports</h4>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">Sales Perf</span>
                                        <span class="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">Cust Seg</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow overflow-hidden">
                            <div class="px-6 py-4 bg-green-50 border-b border-green-100">
                                <h3 class="text-lg font-medium text-green-800">System Status</h3>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center mb-4">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">All systems operational</h4>
                                        <p class="text-sm text-gray-500">Last checked 5 minutes ago</p>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="font-medium text-gray-700">Database Server</span>
                                            <span class="text-green-600">Online</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="bg-green-600 h-1.5 rounded-full" style="width: 92%"></div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="font-medium text-gray-700">BI Platform</span>
                                            <span class="text-green-600">Online</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="bg-green-600 h-1.5 rounded-full" style="width: 85%"></div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="font-medium text-gray-700">ETL Service</span>
                                            <span class="text-green-600">Online</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="bg-green-600 h-1.5 rounded-full" style="width: 78%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Content -->
                <div id="profile" class="tab-content">
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">User Profile</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Personal details and account information.</p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="md:flex">
                                <div class="md:w-1/3 mb-6 md:mb-0">
                                    <div class="avatar-upload">
                                        <div class="avatar-preview">
                                            <div id="imagePreview" style="background-image: url('https://randomuser.me/api/portraits/women/44.jpg');"></div>
                                        </div>
                                        <div class="avatar-edit">
                                            <input type='file' id="imageUpload" accept=".png, .jpg, .jpeg" />
                                            <label for="imageUpload"><i class="fas fa-pencil-alt text-gray-600"></i></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="md:w-2/3 md:pl-8">
                                    <form>
                                        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                            <div class="sm:col-span-3">
                                                <label for="first-name" class="block text-sm font-medium text-gray-700">First name</label>
                                                <input type="text" name="first-name" id="first-name" autocomplete="given-name" value="Sarah" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            </div>

                                            <div class="sm:col-span-3">
                                                <label for="last-name" class="block text-sm font-medium text-gray-700">Last name</label>
                                                <input type="text" name="last-name" id="last-name" autocomplete="family-name" value="Johnson" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            </div>

                                            <div class="sm:col-span-4">
                                                <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                                                <input type="email" name="email" id="email" autocomplete="email" value="<EMAIL>" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            </div>

                                            <div class="sm:col-span-3">
                                                <label for="department" class="block text-sm font-medium text-gray-700">Department</label>
                                                <select id="department" name="department" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                    <option>Marketing</option>
                                                    <option>Sales</option>
                                                    <option selected>IT</option>
                                                    <option>Operations</option>
                                                    <option>Finance</option>
                                                </select>
                                            </div>

                                            <div class="sm:col-span-3">
                                                <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
                                                <select id="role" name="role" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                    <option>Data Analyst</option>
                                                    <option selected>Data Engineer</option>
                                                    <option>BI Developer</option>
                                                    <option>Data Scientist</option>
                                                    <option>Admin</option>
                                                </select>
                                            </div>

                                            <div class="sm:col-span-6">
                                                <label for="bio" class="block text-sm font-medium text-gray-700">About</label>
                                                <div class="mt-1">
                                                    <textarea id="bio" name="bio" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border border-gray-300 rounded-md">Data engineer with 5 years of experience in building data pipelines and analytics platforms.</textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-8 flex justify-end">
                                            <button type="button" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                                Cancel
                                            </button>
                                            <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                                Save
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Section -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg mt-6">
                        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Security</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage your account security settings.</p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="space-y-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Password</h4>
                                        <p class="text-sm text-gray-500">Last changed 3 months ago</p>
                                    </div>
                                    <button type="button" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Change Password
                                    </button>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Two-factor Authentication</h4>
                                        <p class="text-sm text-gray-500">Currently disabled</p>
                                    </div>
                                    <button type="button" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Enable 2FA
                                    </button>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Active Sessions</h4>
                                        <p class="text-sm text-gray-500">2 active sessions</p>
                                    </div>
                                    <button type="button" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        View Sessions
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Content -->
                <div id="settings" class="tab-content">
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Account Settings</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage your account preferences and notification settings.</p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <form>
                                <div class="space-y-6">
                                    <div>
                                        <h3 class="text-lg font-medium leading-6 text-gray-900">Preferences</h3>
                                        <p class="mt-1 text-sm text-gray-500">Customize your dashboard experience.</p>
                                    </div>

                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <div class="sm:col-span-3">
                                            <label for="timezone" class="block text-sm font-medium text-gray-700">Timezone</label>
                                            <select id="timezone" name="timezone" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                <option>(GMT-12:00) International Date Line West</option>
                                                <option>(GMT-11:00) Midway Island, Samoa</option>
                                                <option>(GMT-10:00) Hawaii</option>
                                                <option>(GMT-09:00) Alaska</option>
                                                <option>(GMT-08:00) Pacific Time (US & Canada)</option>
                                                <option>(GMT-07:00) Arizona</option>
                                                <option>(GMT-07:00) Mountain Time (US & Canada)</option>
                                                <option>(GMT-06:00) Central Time (US & Canada)</option>
                                                <option selected>(GMT-05:00) Eastern Time (US & Canada)</option>
                                                <option>(GMT-04:00) Atlantic Time (Canada)</option>
                                            </select>
                                        </div>

                                        <div class="sm:col-span-3">
                                            <label for="language" class="block text-sm font-medium text-gray-700">Language</label>
                                            <select id="language" name="language" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                <option selected>English</option>
                                                <option>Spanish</option>
                                                <option>French</option>
                                                <option>German</option>
                                                <option>Chinese</option>
                                            </select>
                                        </div>

                                        <div class="sm:col-span-6">
                                            <label for="theme" class="block text-sm font-medium text-gray-700">Theme</label>
                                            <select id="theme" name="theme" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                <option selected>Light</option>
                                                <option>Dark</option>
                                                <option>System</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="pt-8">
                                    <div>
                                        <h3 class="text-lg font-medium leading-6 text-gray-900">Notifications</h3>
                                        <p class="mt-1 text-sm text-gray-500">Configure how you receive notifications.</p>
                                    </div>
                                    <div class="mt-6">
                                        <fieldset>
                                            <legend class="text-base font-medium text-gray-900">By Email</legend>
                                            <div class="mt-4 space-y-4">
                                                <div class="flex items-start">
                                                    <div class="flex items-center h-5">
                                                        <input id="comments" name="comments" type="checkbox" checked class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                    </div>
                                                    <div class="ml-3 text-sm">
                                                        <label for="comments" class="font-medium text-gray-700">Request approvals</label>
                                                        <p class="text-gray-500">Get notified when your requests are approved or rejected.</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-start">
                                                    <div class="flex items-center h-5">
                                                        <input id="candidates" name="candidates" type="checkbox" checked class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                    </div>
                                                    <div class="ml-3 text-sm">
                                                        <label for="candidates" class="font-medium text-gray-700">Pending approvals</label>
                                                        <p class="text-gray-500">Get notified when you have requests to review.</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-start">
                                                    <div class="flex items-center h-5">
                                                        <input id="offers" name="offers" type="checkbox" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                    </div>
                                                    <div class="ml-3 text-sm">
                                                        <label for="offers" class="font-medium text-gray-700">System updates</label>
                                                        <p class="text-gray-500">Get notified about system maintenance and updates.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                        <fieldset class="mt-6">
                                            <legend class="text-base font-medium text-gray-900">Push Notifications</legend>
                                            <div class="mt-4 space-y-4">
                                                <div class="flex items-center">
                                                    <input id="push-everything" name="push-notifications" type="radio" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                                                    <label for="push-everything" class="ml-3 block text-sm font-medium text-gray-700">Everything</label>
                                                </div>
                                                <div class="flex items-center">
                                                    <input id="push-email" name="push-notifications" type="radio" checked class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                                                    <label for="push-email" class="ml-3 block text-sm font-medium text-gray-700">Same as email</label>
                                                </div>
                                                <div class="flex items-center">
                                                    <input id="push-nothing" name="push-notifications" type="radio" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                                                    <label for="push-nothing" class="ml-3 block text-sm font-medium text-gray-700">No push notifications</label>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>
                                </div>

                                <div class="mt-8 flex justify-end">
                                    <button type="button" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Cancel
                                    </button>
                                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Save
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- User Management Content -->
                <div id="users" class="tab-content">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-medium text-gray-900">User Management</h3>
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
                            <i class="fas fa-plus"></i>
                            <span>Add User</span>
                        </button>
                    </div>
                    
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">All Users</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage user accounts and permissions.</p>
                        </div>
                        <div class="bg-white px-4 py-3 border-b border-gray-200 sm:px-6">
                            <div class="flex flex-col sm:flex-row justify-between">
                                <div class="relative flex-grow mb-3 sm:mb-0">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                    <input type="text" name="search" id="search" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Search users...">
                                </div>
                                <div class="flex space-x-3">
                                    <select id="filter-role" name="filter-role" class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                        <option selected>All Roles</option>
                                        <option>Admin</option>
                                        <option>Data Analyst</option>
                                        <option>Data Engineer</option>
                                        <option>BI Developer</option>
                                        <option>Viewer</option>
                                    </select>
                                    <select id="filter-status" name="filter-status" class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                        <option selected>All Statuses</option>
                                        <option>Active</option>
                                        <option>Pending</option>
                                        <option>Suspended</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Active</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Robert Johnson</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">Admin</div>
                                            <div class="text-sm text-gray-500">IT Department</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 hours ago</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                                            <button class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/44.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Sarah Parker</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">Data Engineer</div>
                                            <div class="text-sm text-gray-500">Engineering</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1 day ago</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                                            <button class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/75.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Michael Chen</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">BI Developer</div>
                                            <div class="text-sm text-gray-500">Analytics</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                                            <button class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/68.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Emily Wilson</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">Data Analyst</div>
                                            <div class="text-sm text-gray-500">Marketing</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3 days ago</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                                            <button class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/12.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">David Kim</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">Viewer</div>
                                            <div class="text-sm text-gray-500">Sales</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Suspended</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1 week ago</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                                            <button class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">24</span> results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Previous</span>
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                        <a href="#" aria-current="page" class="z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">2</a>
                                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">3</a>
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Next</span>
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Access Tabs (Original Content) -->
                <div id="adhoc" class="tab-content">
                    <!-- Ad-Hoc Requests content remains the same -->
                </div>

                <div id="datasource" class="tab-content">
                    <!-- Data Sources content remains the same -->
                </div>

                <div id="tableau" class="tab-content">
                    <!-- Tableau Reports content remains the same -->
                </div>

                <div id="platform" class="tab-content">
                    <!-- Platform Access content remains the same -->
                </div>
            </main>
        </div>
    </div>

    <script>
        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Set default tab
            let defaultTab = 'dashboard';
            
            // Get all tab links and content
            const tabLinks = document.querySelectorAll('.tab-link, .tab-nav');
            const tabContents = document.querySelectorAll('.tab-content');
            
            // Function to switch tabs
            function switchTab(tabId) {
                // Hide all tab contents
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                
                // Show selected tab content
                document.getElementById(tabId).classList.add('active');
                
                // Update active state of tab links
                tabLinks.forEach(link => {
                    if (link.dataset.tab === tabId) {
                        link.classList.add('border-indigo-500', 'text-indigo-600', 'bg-indigo-700');
                        link.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                    } else {
                        link.classList.remove('border-indigo-500', 'text-indigo-600', 'bg-indigo-700');
                        if (!link.classList.contains('tab-nav')) {
                            link.classList.add('border-transparent', 'text-white', 'hover:text-white', 'hover:bg-indigo-700');
                        } else {
                            link.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                        }
                    }
                });
                
                // Update URL hash
                window.location.hash = tabId;
            }
            
            // Set up click event listeners for all tab links
            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tabId = this.dataset.tab;
                    switchTab(tabId);
                });
            });
            
            // Check URL hash on page load
            if (window.location.hash) {
                const hash = window.location.hash.substring(1);
                if (document.getElementById(hash)) {
                    defaultTab = hash;
                }
            }
            
            // Initialize with default tab
            switchTab(defaultTab);
            
            // Mobile menu toggle (placeholder - would need implementation)
            const mobileMenuButton = document.querySelector('header button.lg\\:hidden');
            const sidebar = document.querySelector('.sidebar');
            
            if (mobileMenuButton && sidebar) {
                mobileMenuButton.addEventListener('click', function() {
                    sidebar.classList.toggle('hidden');
                });
            }

            // Avatar upload preview
            function readURL(input) {
                if (input.files && input.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('imagePreview').style.backgroundImage = 'url('+e.target.result +')';
                    }
                    reader.readAsDataURL(input.files[0]);
                }
            }
            
            document.getElementById("imageUpload").addEventListener('change', function() {
                readURL(this);
            });
        });
    </script>
</body>
</html>