import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useTheme } from "../context/ThemeContext";
import {
  FiCheckCircle,
  FiRefreshCw,
  FiTool,
  FiFileText,
  FiServer,
  FiAlertCircle,
  FiClock,
  FiChevronDown,
  FiChevronUp,
  FiSearch,
} from "react-icons/fi";
import "../styles/Notifications.css";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";

const Notifications = () => {
  const navigate = useNavigate();
  const { theme, language } = useTheme();
  const [notifications, setNotifications] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState("all"); // all, unread, read
  const [typeFilter, setTypeFilter] = useState("all"); // all, system, request, test
  const [dateFilter, setDateFilter] = useState("all"); // all, today, yesterday, week, month
  const [searchTerm, setSearchTerm] = useState("");
  const [collapsedGroups, setCollapsedGroups] = useState({
    today: false,
    yesterday: false,
    thisWeek: false,
    older: false,
  });
  const hasInitialFetchedRef = useRef(false); // To track if we've already fetched notifications

  // Get user from localStorage
  const user = JSON.parse(localStorage.getItem("user")) || {};

  // Helper function to extract request ID from notification content
  const extractRequestIdFromContent = (content) => {
    if (!content) return null;

    // Pattern to match "Request #123" or "Request #REQ-123" etc.
    const requestIdMatch = content.match(/Request\s*#(\w+)/i);
    if (requestIdMatch && requestIdMatch[1]) {
      return requestIdMatch[1];
    }

    return null;
  };

  const handleNotificationClick = async (notification) => {
    // Mark notification as read if not already read
    if (!notification.is_read) {
      try {
        await apiService.post(ENDPOINTS.NOTIFICATION.MARK_AS_READ(notification.id));

        // Update local state
        setNotifications(prev => prev.map(notif =>
          notif.id === notification.id ? { ...notif, is_read: true } : notif
        ));

      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }

    // Navigate based on notification type and content
    if (notification.type === 'request') {
      // First try to use related_id if available
      let requestId = notification.related_id;

      // If no related_id, try to extract from content
      if (!requestId) {
        requestId = extractRequestIdFromContent(notification.content);
      }

      // If we found a request ID, navigate to request detail
      if (requestId) {
        navigate(`/requests/detail?id=${requestId}`);
      }
    } else if (notification.type === 'system' && notification.title && notification.title.toLowerCase().includes('release note')) {
      navigate('/release-notes');
    }
    // For other notification types, stay on the notifications page (no navigation)
  };

  const toggleGroupCollapse = (groupName) => {
    setCollapsedGroups((prev) => ({
      ...prev,
      [groupName]: !prev[groupName],
    }));
  };

  const fetchNotifications = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // console.log('Fetching notifications in Notifications.js for user:', user.id);
      // With axios, the response data is returned directly
      const data = await apiService.get(ENDPOINTS.NOTIFICATION.GET(user.id));
      // console.log('Notifications fetched successfully:', data);
      setNotifications(data || []);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      // Handle axios error format
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        (language === "en"
          ? "Failed to fetch notifications"
          : "Không thể tải thông báo");
      setError(errorMessage);
      // Set empty array to prevent undefined errors
      setNotifications([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user && user.id && !hasInitialFetchedRef.current) {
      // console.log('Initial notifications fetch in Notifications.js');
      fetchNotifications();
      hasInitialFetchedRef.current = true;
    }
  }, [user.id]);

  const markAsRead = async (notificationId, event) => {
    if (event) {
      event.stopPropagation();
    }

    try {
      // With axios, no need to handle response.ok or call .json()
      await apiService.post(
        ENDPOINTS.NOTIFICATION.MARK_AS_READ(notificationId)
      );

      // Update local state after successful API call
      setNotifications((prev) =>
        prev.map((notif) =>
          notif.id === notificationId ? { ...notif, is_read: true } : notif
        )
      );
    } catch (error) {
      console.error("Error marking notification as read:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        (language === "en"
          ? "Failed to mark as read"
          : "Không thể đánh dấu đã đọc");
      alert(errorMessage);
    }
  };

  const markAllAsRead = async () => {
    const unreadNotifications = notifications.filter((n) => !n.is_read);

    if (unreadNotifications.length === 0) return;

    try {
      // Create array of promises for each unread notification
      const markReadPromises = unreadNotifications.map((notification) =>
        apiService.post(ENDPOINTS.NOTIFICATION.MARK_AS_READ(notification.id))
      );

      // Wait for all requests to complete
      await Promise.all(markReadPromises);

      // Update local state after all requests succeed
      setNotifications((prev) =>
        prev.map((notif) => ({ ...notif, is_read: true }))
      );
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        (language === "en"
          ? "Failed to mark all as read"
          : "Không thể đánh dấu tất cả đã đọc");
      alert(errorMessage);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const getTypeColor = (type) => {
    switch (type) {
      case "test":
        return "#f44336"; // red
      case "maintenance":
      case "request":
        return "#4CAF50"; // green
      case "reminder":
        return "#FF9800"; // orange
      case "system":
        return "#9C27B0"; // purple
      default:
        return "#4CAF50"; // green
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case "maintenance":
        return <FiTool className="notification-icon" />;
      case "request":
        return <FiFileText className="notification-icon" />;
      case "reminder":
        return <FiClock className="notification-icon" />;
      case "system":
        return <FiServer className="notification-icon" />;
      case "test":
        return <FiAlertCircle className="notification-icon" />;
      default:
        return <FiAlertCircle className="notification-icon" />;
    }
  };

  const formatNotificationTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return language === "en" ? "just now" : "vừa xong";
    if (diffMins < 60)
      return `${diffMins}${language === "en" ? "m" : " phút"} ago`;
    if (diffHours < 24)
      return `${diffHours}${language === "en" ? "h" : " giờ"} ago`;
    if (diffDays < 7)
      return `${diffDays}${language === "en" ? "d" : " ngày"} ago`;

    return date.toLocaleDateString();
  };

  const getFilteredNotifications = () => {
    let filtered = [...notifications];

    // Apply read/unread filter
    if (filter === "unread") {
      filtered = filtered.filter((n) => !n.is_read);
    } else if (filter === "read") {
      filtered = filtered.filter((n) => n.is_read);
    }

    // Apply type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter((n) => n.type === typeFilter);
    }

    // Apply date filter
    if (dateFilter !== "all") {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      // Get current week's Monday
      const thisWeekStart = new Date(today);
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const diff = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Adjust for Monday as first day
      thisWeekStart.setDate(today.getDate() - diff);
      thisWeekStart.setHours(0, 0, 0, 0);

      // Get first day of current month
      const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      thisMonthStart.setHours(0, 0, 0, 0);

      // End of today (for comparison)
      const endOfToday = new Date(today);
      endOfToday.setHours(23, 59, 59, 999);

      filtered = filtered.filter((n) => {
        const notifDate = new Date(n.created_at);

        if (dateFilter === "today") {
          return notifDate >= today && notifDate <= endOfToday;
        } else if (dateFilter === "yesterday") {
          const endOfYesterday = new Date(yesterday);
          endOfYesterday.setHours(23, 59, 59, 999);
          return notifDate >= yesterday && notifDate <= endOfYesterday;
        } else if (dateFilter === "week") {
          return notifDate >= thisWeekStart && notifDate <= endOfToday;
        } else if (dateFilter === "month") {
          return notifDate >= thisMonthStart && notifDate <= endOfToday;
        }

        return true;
      });
    }

    // Apply search filter
    if (searchTerm.trim() !== "") {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(
        (n) =>
          n.title.toLowerCase().includes(term) ||
          n.content.toLowerCase().includes(term) ||
          n.type.toLowerCase().includes(term)
      );
    }

    return filtered;
  };

  // Group notifications by date
  const groupNotificationsByDate = (notifications) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const thisWeek = new Date(today);
    thisWeek.setDate(thisWeek.getDate() - 7);

    const groups = {
      today: [],
      yesterday: [],
      thisWeek: [],
      older: [],
    };

    notifications.forEach((notification) => {
      const notifDate = new Date(notification.created_at);
      notifDate.setHours(0, 0, 0, 0);

      if (notifDate.getTime() === today.getTime()) {
        groups.today.push(notification);
      } else if (notifDate.getTime() === yesterday.getTime()) {
        groups.yesterday.push(notification);
      } else if (notifDate >= thisWeek) {
        groups.thisWeek.push(notification);
      } else {
        groups.older.push(notification);
      }
    });

    return groups;
  };

  const renderNotificationGroup = (notifications, groupTitle) => {
    if (notifications.length === 0) return null;

    // Map group titles to state keys
    const groupKey =
      groupTitle === (language === "en" ? "Today" : "Hôm nay")
        ? "today"
        : groupTitle === (language === "en" ? "Yesterday" : "Hôm qua")
        ? "yesterday"
        : groupTitle === (language === "en" ? "This Week" : "Tuần này")
        ? "thisWeek"
        : "older";

    const isCollapsed = collapsedGroups[groupKey];

    return (
      <div className="notification-group" key={groupTitle}>
        <div
          className="notification-group-header"
          onClick={() => toggleGroupCollapse(groupKey)}
        >
          <div className="notification-group-title">
            <h4>{groupTitle}</h4>
            <span className="notification-count-badge">
              {notifications.length}
            </span>
          </div>
          <span className="noti-collapse-indicator">
            {isCollapsed ? <FiChevronDown /> : <FiChevronUp />}
          </span>
        </div>

        {!isCollapsed && (
          <div className="notification-group-content">
            {notifications.map((notification, index) => (
              <div
                key={notification.id}
                className={`notification-item ${
                  notification.is_read ? "read" : "unread"
                } ${index % 2 === 1 ? "highlight" : ""}`}
                onClick={() => handleNotificationClick(notification)}
                style={{ cursor: 'pointer' }}
              >
                <div className="notification-icon-container">
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="notification-content">
                  <div className="notification-header">
                    <span
                      className="notification-dot"
                      style={{
                        backgroundColor: getTypeColor(notification.type),
                      }}
                    ></span>
                    <h4 className="notification-title">{notification.title}</h4>
                    <span className="notification-time">
                      {formatNotificationTime(notification.created_at)}
                    </span>
                  </div>
                  <p className="notification-message">{notification.content}</p>
                </div>
                {!notification.is_read && (
                  <button
                    className="noti-btn-mark-read"
                    onClick={(e) => markAsRead(notification.id, e)}
                    title={
                      language === "en" ? "Mark as read" : "Đánh dấu đã đọc"
                    }
                  >
                    <FiCheckCircle />
                  </button>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const filteredNotifications = getFilteredNotifications();
  const unreadCount = notifications.filter((n) => !n.is_read).length;
  const groupedNotifications = groupNotificationsByDate(filteredNotifications);

  return (
    <div className="notifications-page">
      <div className="notifications-page-header">
        <div className="notifications-page-title">
          {/* <h2>{language === 'en' ? 'Notifications' : 'Thông báo'}</h2> */}
          <span className="notifications-count">
            {filteredNotifications.length}{" "}
            {language === "en" ? "notifications" : "thông báo"}
            {unreadCount > 0 && (
              <span className="noti-unread-badge">
                {unreadCount} {language === "en" ? "unread" : "chưa đọc"}
              </span>
            )}
          </span>
        </div>

        <div className="notifications-filters">
          <div className="notification-filter-group">
            <label>{language === "en" ? "Status:" : "Trạng thái:"}</label>
            <select value={filter} onChange={(e) => setFilter(e.target.value)}>
              <option value="all">
                {language === "en" ? "All" : "Tất cả"}
              </option>
              <option value="unread">
                {language === "en" ? "Unread" : "Chưa đọc"}
              </option>
              <option value="read">
                {language === "en" ? "Read" : "Đã đọc"}
              </option>
            </select>
          </div>

          <div className="notification-filter-group">
            <label>{language === "en" ? "Type:" : "Loại:"}</label>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="all">
                {language === "en" ? "All" : "Tất cả"}
              </option>
              <option value="system">
                {language === "en" ? "System" : "Hệ thống"}
              </option>
              <option value="request">
                {language === "en" ? "Request" : "Yêu cầu"}
              </option>
              <option value="maintenance">
                {language === "en" ? "Maintenance" : "Bảo trì"}
              </option>
              <option value="reminder">
                {language === "en" ? "Reminder" : "Nhắc nhở"}
              </option>
              <option value="test">
                {language === "en" ? "Test" : "Kiểm tra"}
              </option>
            </select>
          </div>

          <div className="notification-filter-group date-notification-filter-group">
            <label>{language === "en" ? "Date:" : "Ngày:"}</label>
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            >
              <option value="all">
                {language === "en" ? "All time" : "Mọi thời gian"}
              </option>
              <option value="today">
                {language === "en" ? "Today" : "Hôm nay"}
              </option>
              <option value="yesterday">
                {language === "en" ? "Yesterday" : "Hôm qua"}
              </option>
              <option value="week">
                {language === "en" ? "This week" : "Tuần này"}
              </option>
              <option value="month">
                {language === "en" ? "This month" : "Tháng này"}
              </option>
            </select>
          </div>
        </div>

        <div className="notifications-search">
          <div className="noti-search-container">
            <span className="search-icon">
              <FiSearch />
            </span>
            <input
              type="text"
              placeholder={
                language === "en"
                  ? "Search notifications..."
                  : "Tìm kiếm thông báo..."
              }
              value={searchTerm}
              onChange={handleSearch}
              className="noti-search-input"
            />
          </div>
        </div>

        <div className="notifications-actions">
          <button
            className="noti-btn-refresh"
            onClick={fetchNotifications}
            title={language === "en" ? "Refresh" : "Làm mới"}
          >
            <FiRefreshCw />
          </button>

          {unreadCount > 0 && (
            <button className="noti-btn-mark-all-read" onClick={markAllAsRead}>
              <FiCheckCircle />
              <span>
                {language === "en"
                  ? "Mark all as read"
                  : "Đánh dấu tất cả đã đọc"}
              </span>
            </button>
          )}
        </div>
      </div>

      <div className="notifications-container">
        {isLoading && (
          <div className="notifications-loading">
            {language === "en"
              ? "Loading notifications..."
              : "Đang tải thông báo..."}
          </div>
        )}

        {error && (
          <div className="error-message">
            <FiAlertCircle />
            <span>{error}</span>
          </div>
        )}

        {!isLoading && !error && filteredNotifications.length === 0 && (
          <div className="notifications-empty">
            {language === "en"
              ? "No notifications found"
              : "Không tìm thấy thông báo nào"}
          </div>
        )}

        {!isLoading && !error && filteredNotifications.length > 0 && (
          <div className="notifications-list">
            {renderNotificationGroup(
              groupedNotifications.today,
              language === "en" ? "Today" : "Hôm nay"
            )}
            {renderNotificationGroup(
              groupedNotifications.yesterday,
              language === "en" ? "Yesterday" : "Hôm qua"
            )}
            {renderNotificationGroup(
              groupedNotifications.thisWeek,
              language === "en" ? "This Week" : "Tuần này"
            )}
            {renderNotificationGroup(
              groupedNotifications.older,
              language === "en" ? "Older" : "Cũ hơn"
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Notifications;
