import React, { useState, useEffect } from "react";
import { useTheme } from "../context/ThemeContext";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiCalendar,
  FiTag,
  FiClock,
  FiRefreshCw,
  FiAlertCircle
} from "react-icons/fi";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import "../styles/ReleaseNotes.css";
import MDEditor from "@uiw/react-md-editor";
import "@uiw/react-md-editor/markdown-editor.css";
import "@uiw/react-markdown-preview/markdown.css";
import VersionTree from "../components/VersionTree";

// Hàm kiểm tra xem người dùng có phải là admin hay không
const isUserAdmin = () => {
  // Lấy thông tin người dùng từ localStorage
  const userStr = localStorage.getItem("user");
  if (!userStr) return false;

  try {
    const user = JSON.parse(userStr);
    // <PERSON><PERSON><PERSON> tra xem role có phải là admin hay không
    return user.role === "admin";
  } catch (error) {
    console.error("Error parsing user data:", error);
    return false;
  }
};

const ReleaseNotes = () => {
  const { theme, language } = useTheme();
  const [releases, setReleases] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState("released"); // all, upcoming, released
  const [versionFilter, setVersionFilter] = useState(null); // Filter by version
  const [editingId, setEditingId] = useState(null); // Track which release is being edited
  const [isAdding, setIsAdding] = useState(false); // Track if we're adding a new release
  const [previousCompletePercent, setPreviousCompletePercent] = useState(0); // Lưu giá trị tiến trình trước khi chuyển sang released
  const [isAdmin, setIsAdmin] = useState(false); // Kiểm tra xem người dùng có phải là admin hay không

  // Form state
  const [formData, setFormData] = useState({
    id: null,
    version: "",
    status: "upcoming",
    release_date: "",
    complete_percent: 0,
    content: "",
  });

  // Không sử dụng mock data nữa, tất cả dữ liệu sẽ được lấy từ API

  useEffect(() => {
    // Fetch releases from API
    const fetchReleases = async () => {
      try {
        setIsLoading(true);
        const response = await apiService.get(ENDPOINTS.RELEASE.GET_ALL);

        if (response) {
          if (response.data) {
            // Kiểm tra xem response.data có phải là mảng không
            if (Array.isArray(response.data)) {
              setReleases(response.data);
            } else if (typeof response.data === "object") {
              // Nếu response.data là object, kiểm tra xem có thuộc tính nào chứa mảng không

              // Kiểm tra các thuộc tính phổ biến có thể chứa dữ liệu
              const possibleDataKeys = [
                "items",
                "results",
                "releases",
                "data",
                "content",
              ];
              for (const key of possibleDataKeys) {
                if (response.data[key] && Array.isArray(response.data[key])) {
                  setReleases(response.data[key]);
                  break;
                }
              }

              // Nếu không tìm thấy mảng trong các thuộc tính phổ biến, thử chuyển đổi object thành mảng
              if (!Array.isArray(releases) || releases.length === 0) {
                const dataArray = Object.values(response.data).filter(
                  (item) => typeof item === "object" && item !== null
                );
                if (dataArray.length > 0) {
                  setReleases(dataArray);
                } else {
                  setReleases([]);
                }
              }
            } else {
              setReleases([]);
            }
          } else {
            // Kiểm tra xem response có phải là mảng không
            if (Array.isArray(response)) {
              setReleases(response);
            } else {
              setReleases([]);
            }
          }
        } else {
          setReleases([]);
        }
        setError(null);
      } catch (err) {
        console.error("Error fetching releases:", err);
        setError("Failed to load releases. Please try again later.");
        setReleases([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReleases();
  }, []);

  // Kiểm tra xem người dùng có phải là admin hay không khi component được mount
  useEffect(() => {
    setIsAdmin(isUserAdmin());
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Nếu đang thay đổi status thành released, lưu giá trị tiến trình hiện tại và đặt complete_percent = 100
    if (name === "status" && value === "released") {
      setPreviousCompletePercent(formData.complete_percent);
      setFormData({
        ...formData,
        [name]: value,
        complete_percent: 100,
      });
    }
    // Nếu đang thay đổi status từ released về upcoming, khôi phục giá trị tiến trình trước đó
    else if (
      name === "status" &&
      value === "upcoming" &&
      formData.status === "released"
    ) {
      setFormData({
        ...formData,
        [name]: value,
        complete_percent: previousCompletePercent,
      });
    }
    // Các trường hợp khác
    else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleContentChange = (value) => {
    setFormData({
      ...formData,
      content: value,
    });
  };

  const handleAddRelease = () => {
    // Đặt lại giá trị tiến trình trước đó về 0 khi thêm mới
    setPreviousCompletePercent(0);

    setFormData({
      id: null,
      version: "",
      status: "upcoming",
      release_date: new Date().toISOString().split("T")[0],
      complete_percent: 0,
      content: "",
    });
    setIsAdding(true);
  };

  const handleEditRelease = (release) => {
    // Lưu giá trị tiến trình hiện tại để có thể khôi phục nếu cần
    setPreviousCompletePercent(release.complete_percent || 0);

    setFormData({
      id: release.id,
      version: release.version,
      status: release.status,
      release_date: release.release_date,
      complete_percent: release.complete_percent || 0,
      content: release.content,
    });
    setEditingId(release.id);
  };

  const handleCancelEdit = (e) => {
    if (e) e.preventDefault(); // Ngăn chặn hành vi mặc định của nút

    setEditingId(null);
    setIsAdding(false);
    setFormData({
      id: null,
      version: "",
      status: "upcoming",
      release_date: "",
      complete_percent: 0,
      content: "",
    });
  };

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();

    // Validate form
    if (!formData.version || !formData.release_date || !formData.content) {
      setError("Please fill in all required fields.");
      return;
    }

    try {
      setIsLoading(true);

      if (formData.id) {
        // Update existing release
        // Sử dụng ENDPOINTS.RELEASE.UPDATE với id
        const updateEndpoint = ENDPOINTS.RELEASE.UPDATE(formData.id);
        await apiService.post(updateEndpoint, formData);

        // Hiển thị thông báo thành công
        setError(null);
        // Thêm thông báo thành công tạm thời
        const successMessage = document.createElement("div");
        successMessage.className = "success-message";
        successMessage.textContent =
          language === "en"
            ? "Release updated successfully!"
            : "Cập nhật phiên bản thành công!";
        document
          .querySelector(".release-notes-container")
          .appendChild(successMessage);

        // Tự động ẩn thông báo sau 3 giây
        setTimeout(() => {
          if (successMessage.parentNode) {
            successMessage.parentNode.removeChild(successMessage);
          }
        }, 3000);
      } else {
        // Create new release
        // Sử dụng ENDPOINTS.RELEASE.CREATE
        await apiService.post(ENDPOINTS.RELEASE.CREATE, formData);

        // Hiển thị thông báo thành công
        setError(null);
        // Thêm thông báo thành công tạm thời
        const successMessage = document.createElement("div");
        successMessage.className = "success-message";
        successMessage.textContent =
          language === "en"
            ? "Release created successfully!"
            : "Tạo phiên bản mới thành công!";
        document
          .querySelector(".release-notes-container")
          .appendChild(successMessage);

        // Tự động ẩn thông báo sau 3 giây
        setTimeout(() => {
          if (successMessage.parentNode) {
            successMessage.parentNode.removeChild(successMessage);
          }
        }, 3000);
      }

      // Refresh the list after create/update
      const response = await apiService.get(ENDPOINTS.RELEASE.GET_ALL);
      if (response) {
        if (response.data) {
          // Kiểm tra xem response.data có phải là mảng không
          if (Array.isArray(response.data)) {
            setReleases(response.data);
          } else if (typeof response.data === "object") {
            // Nếu response.data là object, kiểm tra xem có thuộc tính nào chứa mảng không

            // Kiểm tra các thuộc tính phổ biến có thể chứa dữ liệu
            const possibleDataKeys = [
              "items",
              "results",
              "releases",
              "data",
              "content",
            ];
            for (const key of possibleDataKeys) {
              if (response.data[key] && Array.isArray(response.data[key])) {
                setReleases(response.data[key]);
                break;
              }
            }

            // Nếu không tìm thấy mảng trong các thuộc tính phổ biến, thử chuyển đổi object thành mảng
            if (!Array.isArray(releases) || releases.length === 0) {
              const dataArray = Object.values(response.data).filter(
                (item) => typeof item === "object" && item !== null
              );
              if (dataArray.length > 0) {
                setReleases(dataArray);
              }
            }
          }
        } else if (Array.isArray(response)) {
          setReleases(response);
        }
      }

      // Reset form and close
      setFormData({
        id: null,
        version: "",
        status: "upcoming",
        release_date: "",
        complete_percent: 0,
        content: "",
      });
      setEditingId(null);
      setIsAdding(false);
    } catch (err) {
      setError(
        language === "en"
          ? "Failed to save release. Please try again."
          : "Không thể lưu phiên bản. Vui lòng thử lại."
      );
      console.error("Error saving release:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteRelease = async (id) => {
    const confirmMessage =
      language === "en"
        ? "Are you sure you want to delete this release?"
        : "Bạn có chắc chắn muốn xóa phiên bản này không?";

    if (window.confirm(confirmMessage)) {
      try {
        setIsLoading(true);

        // Sử dụng ENDPOINTS.RELEASE.DELETE với id
        const deleteEndpoint = ENDPOINTS.RELEASE.DELETE(id);
        await apiService.post(deleteEndpoint);

        // Hiển thị thông báo thành công
        setError(null);
        // Thêm thông báo thành công tạm thời
        const successMessage = document.createElement("div");
        successMessage.className = "success-message";
        successMessage.textContent =
          language === "en"
            ? "Release deleted successfully!"
            : "Xóa phiên bản thành công!";
        document
          .querySelector(".release-notes-container")
          .appendChild(successMessage);

        // Tự động ẩn thông báo sau 3 giây
        setTimeout(() => {
          if (successMessage.parentNode) {
            successMessage.parentNode.removeChild(successMessage);
          }
        }, 3000);

        // Refresh the list after delete
        const response = await apiService.get(ENDPOINTS.RELEASE.GET_ALL);
        if (response) {
          if (response.data) {
            // Kiểm tra xem response.data có phải là mảng không
            if (Array.isArray(response.data)) {
              setReleases(response.data);
            } else if (typeof response.data === "object") {
              // Nếu response.data là object, kiểm tra xem có thuộc tính nào chứa mảng không

              // Kiểm tra các thuộc tính phổ biến có thể chứa dữ liệu
              const possibleDataKeys = [
                "items",
                "results",
                "releases",
                "data",
                "content",
              ];
              for (const key of possibleDataKeys) {
                if (response.data[key] && Array.isArray(response.data[key])) {
                  setReleases(response.data[key]);
                  break;
                }
              }

              // Nếu không tìm thấy mảng trong các thuộc tính phổ biến, thử chuyển đổi object thành mảng
              if (!Array.isArray(releases) || releases.length === 0) {
                const dataArray = Object.values(response.data).filter(
                  (item) => typeof item === "object" && item !== null
                );
                if (dataArray.length > 0) {
                  setReleases(dataArray);
                }
              }
            }
          } else if (Array.isArray(response)) {
            setReleases(response);
          } else {
            // If API returns empty data, filter locally
            const filteredReleases = releases.filter(
              (release) => release.id !== id
            );
            setReleases(filteredReleases);
          }
        } else {
          // If no response, filter locally
          const filteredReleases = releases.filter(
            (release) => release.id !== id
          );
          setReleases(filteredReleases);
        }
      } catch (err) {
        setError(
          language === "en"
            ? "Failed to delete release. Please try again."
            : "Không thể xóa phiên bản. Vui lòng thử lại."
        );
        console.error("Error deleting release:", err);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // No longer needed with inline editing

  const filteredReleases = releases.filter((release) => {
    // Filter by status
    if (statusFilter !== "all") {
      // Xử lý cả "upcoming" và "upcomming" (lỗi chính tả từ API)
      if (
        statusFilter === "upcoming" &&
        !(release.status === "upcoming" || release.status === "upcomming")
      ) {
        return false;
      } else if (statusFilter === "released" && release.status !== "released") {
        return false;
      }
    }

    // Filter by version
    if (versionFilter) {
      // Check if version starts with the filter (e.g., v1, v1.2, etc.)
      if (!release.version.startsWith(versionFilter)) {
        return false;
      }
    }

    return true;
  });

  // Sort releases by release date (newest first)
  const sortedReleases = [...filteredReleases].sort(
    (a, b) => new Date(b.release_date) - new Date(a.release_date)
  );

  // Handle version selection from the tree
  const handleVersionSelect = (version) => {
    setVersionFilter(version);
  };

  return (
    <div className={`release-notes-container ${theme}`}>
      <div className="release-notes-header">
        <div className="status-filter">
          <button
            className={statusFilter === "all" ? "active" : ""}
            onClick={() => setStatusFilter("all")}
          >
            {language === "en" ? "All" : "Tất cả"}
          </button>
          <button
            className={statusFilter === "released" ? "active" : ""}
            onClick={() => setStatusFilter("released")}
          >
            {language === "en" ? "Released" : "Đã phát hành"}
          </button>
          <button
            className={statusFilter === "upcoming" ? "active" : ""}
            onClick={() => setStatusFilter("upcoming")}
          >
            {language === "en" ? "Upcoming" : "Sắp ra mắt"}
          </button>
        </div>
        <div className="action-buttons">
          <button
            className="refresh-btn"
            onClick={async () => {
              try {
                setIsLoading(true);
                const response = await apiService.get(
                  ENDPOINTS.RELEASE.GET_ALL
                );

                if (response) {
                  if (response.data) {
                    // Kiểm tra xem response.data có phải là mảng không
                    if (Array.isArray(response.data)) {
                      setReleases(response.data);
                    } else if (typeof response.data === "object") {
                      // Nếu response.data là object, kiểm tra xem có thuộc tính nào chứa mảng không

                      // Kiểm tra các thuộc tính phổ biến có thể chứa dữ liệu
                      const possibleDataKeys = [
                        "items",
                        "results",
                        "releases",
                        "data",
                        "content",
                      ];
                      for (const key of possibleDataKeys) {
                        if (
                          response.data[key] &&
                          Array.isArray(response.data[key])
                        ) {
                          setReleases(response.data[key]);
                          break;
                        }
                      }

                      // Nếu không tìm thấy mảng trong các thuộc tính phổ biến, thử chuyển đổi object thành mảng
                      const currentReleases = [...releases];
                      if (
                        !Array.isArray(currentReleases) ||
                        currentReleases.length === 0
                      ) {
                        const dataArray = Object.values(response.data).filter(
                          (item) => typeof item === "object" && item !== null
                        );
                        if (dataArray.length > 0) {
                          setReleases(dataArray);
                        } else {
                          setReleases([]);
                        }
                      }
                    } else {
                      setReleases([]);
                    }
                  } else {
                    // Kiểm tra xem response có phải là mảng không
                    if (Array.isArray(response)) {
                      setReleases(response);
                    } else {
                      setReleases([]);
                    }
                  }
                } else {
                  setReleases([]);
                }
                setError(null);
              } catch (err) {
                console.error("Error refreshing releases:", err);
                setError("Failed to refresh releases. Please try again later.");
                setReleases([]);
              } finally {
                setIsLoading(false);
              }
            }}
          >
            <FiRefreshCw /> {language === "en" ? "Refresh" : "Làm mới"}
          </button>
          {isAdmin && (
            <button className="add-release-btn" onClick={handleAddRelease}>
              <FiPlus /> {language === "en" ? "Add Release" : "Thêm phiên bản"}
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}

      {isLoading ? (
        <div className="loading-spinner">
          {/* {language === 'en' ? 'Loading releases...' : 'Đang tải ghi chú phát hành...'} */}
        </div>
      ) : (
        <div className="releases-list" style={{ marginTop: "24px" }}>
          <div className="release-items-container">
            {isAdding && (
              <div className="release-item release-form">
                <form onSubmit={handleSubmit}>
                  <div className="release-item-header edit-mode">
                    <div className="release-item-row">
                      <div className="release-item-version">
                        <input
                          type="text"
                          name="version"
                          value={formData.version}
                          onChange={handleInputChange}
                          placeholder={
                            language === "en"
                              ? "Version (e.g., v1.0.0)"
                              : "Phiên bản (vd: v1.0.0)"
                          }
                          required
                        />
                      </div>
                      <div className="release-item-status">
                        <label>
                          <input
                            type="radio"
                            name="status"
                            value="upcoming"
                            checked={formData.status === "upcoming"}
                            onChange={handleInputChange}
                          />
                          {language === "en" ? "Upcoming" : "Sắp ra mắt"}
                        </label>
                        <label>
                          <input
                            type="radio"
                            name="status"
                            value="released"
                            checked={formData.status === "released"}
                            onChange={handleInputChange}
                          />
                          {language === "en" ? "Released" : "Đã phát hành"}
                        </label>
                      </div>
                      <div className="release-item-date">
                        <input
                          type="date"
                          name="release_date"
                          value={formData.release_date}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                    </div>
                    <div className="release-progress edit-mode">
                      <div className="progress-container">
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{ width: `${formData.complete_percent}%` }}
                          ></div>
                          <div
                            className="progress-thumb"
                            style={{ left: `${formData.complete_percent}%` }}
                          ></div>
                          <input
                            type="range"
                            name="complete_percent"
                            min="0"
                            max="100"
                            value={formData.complete_percent}
                            onChange={handleInputChange}
                            className="progress-slider"
                          />
                        </div>
                      </div>
                      <span className="progress-text">
                        {formData.complete_percent}%{" "}
                        {language === "en" ? "Complete" : "Hoàn thành"}
                      </span>
                    </div>
                  </div>
                  <div className="release-item-content">
                    <div data-color-mode={theme === "dark" ? "dark" : "light"}>
                      <MDEditor
                        value={formData.content}
                        onChange={handleContentChange}
                        height={600}
                        preview="live"
                        textareaProps={{
                          placeholder:
                            language === "en"
                              ? "Write your release notes content here..."
                              : "Viết nội dung ghi chú phát hành ở đây...",
                        }}
                        visibleDragbar={false}
                      />
                    </div>
                  </div>
                  <div className="release-item-actions">
                    <button
                      type="button"
                      className="cancel-btn"
                      onClick={handleCancelEdit}
                    >
                      {language === "en" ? "Cancel" : "Hủy"}
                    </button>
                    <button type="submit" className="save-btn">
                      {language === "en" ? "Save" : "Lưu"}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {sortedReleases.map((release) => (
              <div
                key={release.id}
                className={`release-item ${
                  release.status === "upcomming" ? "upcoming" : release.status
                }`}
              >
                {editingId === release.id ? (
                  <form onSubmit={handleSubmit}>
                    <div className="release-item-header edit-mode">
                      <div className="release-item-row">
                        <div className="release-item-version">
                          <input
                            type="text"
                            name="version"
                            value={formData.version}
                            onChange={handleInputChange}
                            placeholder={
                              language === "en"
                                ? "Version (e.g., v1.0.0)"
                                : "Phiên bản (vd: v1.0.0)"
                            }
                            required
                          />
                        </div>
                        <div className="release-item-status">
                          <label>
                            <input
                              type="radio"
                              name="status"
                              value="upcoming"
                              checked={formData.status === "upcoming"}
                              onChange={handleInputChange}
                            />
                            {language === "en" ? "Upcoming" : "Sắp ra mắt"}
                          </label>
                          <label>
                            <input
                              type="radio"
                              name="status"
                              value="released"
                              checked={formData.status === "released"}
                              onChange={handleInputChange}
                            />
                            {language === "en" ? "Released" : "Đã phát hành"}
                          </label>
                        </div>
                        <div className="release-item-date">
                          <input
                            type="date"
                            name="release_date"
                            value={formData.release_date}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>
                      <div className="release-progress edit-mode">
                        <div className="progress-container">
                          <div className="progress-bar">
                            <div
                              className="progress-fill"
                              style={{ width: `${formData.complete_percent}%` }}
                            ></div>
                            <div
                              className="progress-thumb"
                              style={{ left: `${formData.complete_percent}%` }}
                            ></div>
                            <input
                              type="range"
                              name="complete_percent"
                              min="0"
                              max="100"
                              value={formData.complete_percent}
                              onChange={handleInputChange}
                              className="progress-slider"
                            />
                          </div>
                        </div>
                        <span className="progress-text">
                          {formData.complete_percent}%{" "}
                          {language === "en" ? "Complete" : "Hoàn thành"}
                        </span>
                      </div>
                    </div>
                    <div className="release-item-content">
                      <div
                        data-color-mode={theme === "dark" ? "dark" : "light"}
                      >
                        <MDEditor
                          value={formData.content}
                          onChange={handleContentChange}
                          height={600}
                          preview="live"
                          textareaProps={{
                            placeholder:
                              language === "en"
                                ? "Write your release notes content here..."
                                : "Viết nội dung ghi chú phát hành ở đây...",
                          }}
                          visibleDragbar={false}
                        />
                      </div>
                    </div>
                    <div className="release-item-actions">
                      <button
                        type="button"
                        className="cancel-btn"
                        onClick={handleCancelEdit}
                      >
                        {language === "en" ? "Cancel" : "Hủy"}
                      </button>
                      <button type="submit" className="save-btn">
                        {language === "en" ? "Save" : "Lưu"}
                      </button>
                    </div>
                  </form>
                ) : (
                  <>
                    <div className="release-item-header">
                      <div className="release-item-version">
                        <h2>{release.version}</h2>
                        <div className="release-item-meta">
                          <span
                            className={`release-status ${
                              release.status === "upcomming"
                                ? "upcoming"
                                : release.status
                            }`}
                          >
                            {release.status === "released"
                              ? language === "en"
                                ? "Released"
                                : "Đã phát hành"
                              : language === "en"
                              ? "Upcoming"
                              : "Sắp ra mắt"}
                          </span>
                          <span className="release-date">
                            <FiCalendar /> {release.release_date}
                          </span>
                        </div>
                      </div>
                      <div className="release-progress">
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{
                              width: `${release.complete_percent || 0}%`,
                            }}
                          ></div>
                        </div>
                        <span className="progress-text">
                          {release.complete_percent || 0}%{" "}
                          {language === "en" ? "Complete" : "Hoàn thành"}
                        </span>
                      </div>
                    </div>
                    <div className="release-item-content">
                      <div
                        data-color-mode={theme === "dark" ? "dark" : "light"}
                      >
                        <MDEditor.Markdown
                          source={release.content}
                          style={{ backgroundColor: "var(--section-bg)" }}
                        />
                      </div>
                    </div>
                    {isAdmin && (
                      <div className="release-item-actions">
                        <button
                          className="edit-btn"
                          onClick={() => handleEditRelease(release)}
                        >
                          <FiEdit2 /> {language === "en" ? "Edit" : "Sửa"}
                        </button>
                        <button
                          className="delete-btn"
                          onClick={() => handleDeleteRelease(release.id)}
                        >
                          <FiTrash2 /> {language === "en" ? "Delete" : "Xóa"}
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            ))}

            {sortedReleases.length === 0 && !isAdding && (
              <div className="no-releases-message">
                {language === "en"
                  ? "No releases found matching the current filters."
                  : "Không tìm thấy phiên bản nào phù hợp với bộ lọc hiện tại."}
              </div>
            )}
          </div>

          {/* Version Tree component */}
          <VersionTree
            releases={releases}
            selectedVersion={versionFilter}
            onSelectVersion={handleVersionSelect}
          />
        </div>
      )}

      {/* All editing is now done inline */}
    </div>
  );
};

export default ReleaseNotes;
