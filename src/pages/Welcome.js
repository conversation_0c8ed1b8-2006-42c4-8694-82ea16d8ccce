import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiCheckCircle,
  FiLoader,
  FiArrowRight,
  FiUser,
  FiSettings,
  FiDatabase,
} from "react-icons/fi";
import { useTheme } from "../context/ThemeContext";
import "../styles/Welcome.css";

const Welcome = ({ user, onComplete }) => {
  const navigate = useNavigate();
  const { theme, language } = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [completedSteps, setCompletedSteps] = useState([]);

  // Define steps based on login type
  const getSteps = () => {
    const baseSteps = [
      {
        id: "profile-update",
        title:
          language === "en"
            ? "Update User Profile"
            : "Cập nhật thông tin người dùng",
        description:
          language === "en"
            ? "Updating your profile information..."
            : "<PERSON>ang cập nhật thông tin cá nhân...",
        icon: <FiUser />,
        duration: 2000,
      },
    ];

    if (user?.loginType === "new") {
      return [
        {
          id: "app-init",
          title:
            language === "en" ? "Initialize Application" : "Khởi tạo ứng dụng",
          description:
            language === "en"
              ? "Setting up your workspace..."
              : "Đang thiết lập không gian làm việc...",
          icon: <FiSettings />,
          duration: 2500,
        },
        ...baseSteps,
      ];
    } else if (user?.loginType === "update") {
      return [
        {
          id: "app-update",
          title: language === "en" ? "Update Application" : "Cập nhật ứng dụng",
          description:
            language === "en"
              ? "Applying latest updates..."
              : "Đang áp dụng các cập nhật mới nhất...",
          icon: <FiDatabase />,
          duration: 2000,
        },
        ...baseSteps,
      ];
    }

    return baseSteps;
  };

  const steps = getSteps();

  // Auto-start the process when component mounts
  useEffect(() => {
    if (user?.loginType !== "no-change") {
      startProcess();
    }
  }, [user?.loginType]);

  const startProcess = async () => {
    setIsProcessing(true);

    for (let i = 0; i < steps.length; i++) {
      setCurrentStep(i);

      // Simulate processing time
      await new Promise((resolve) => setTimeout(resolve, steps[i].duration));

      setCompletedSteps((prev) => [...prev, steps[i].id]);
    }

    setIsProcessing(false);
  };

  const handleComplete = () => {
    // Clear the loginType to prevent showing welcome again
    const updatedUser = { ...user };
    delete updatedUser.loginType;
    localStorage.setItem("user", JSON.stringify(updatedUser));

    if (onComplete) {
      onComplete(updatedUser);
    } else {
      // Fallback navigation if onComplete is not provided
      navigate("/");
    }
  };

  const getWelcomeContent = () => {
    if (user?.loginType === "new") {
      return {
        title:
          language === "en"
            ? "Welcome!"
            : "Chào mừng bạn!",
        subtitle:
          language === "en"
            ? "Your data management journey starts here"
            : "Hành trình quản lý dữ liệu của bạn bắt đầu từ đây",
        description:
          language === "en"
            ? "ZDS DataQuest is your comprehensive platform for data requests, analytics, and insights. We're setting up everything for your first experience."
            : "ZDS DataQuest là nền tảng toàn diện cho các yêu cầu dữ liệu, phân tích và thông tin chi tiết. Chúng tôi đang thiết lập mọi thứ cho trải nghiệm đầu tiên của bạn.",
      };
    } else if (user?.loginType === "update") {
      return {
        title: language === "en" ? "Welcome back!" : "Chào mừng trở lại!",
        subtitle:
          language === "en"
            ? "We're updating your experience"
            : "Chúng tôi đang cập nhật trải nghiệm của bạn",
        description:
          language === "en"
            ? "We've made some improvements to ZDS DataQuest. We're applying the latest updates to enhance your experience."
            : "Chúng tôi đã thực hiện một số cải tiến cho ZDS DataQuest. Chúng tôi đang áp dụng các cập nhật mới nhất để nâng cao trải nghiệm của bạn.",
      };
    }

    return null;
  };

  const welcomeContent = getWelcomeContent();

  // If no-change, don't render anything (will be handled by parent)
  if (user?.loginType === "no-change" || !welcomeContent) {
    return null;
  }

  return (
    <div className="welcome-container">
      <div className="welcome-content">
        <div className="welcome-header">
          <div className="welcome-logo">
            <h1>ZDS DataQuest</h1>
          </div>
          <div className="welcome-text">
            <h2>{welcomeContent.title}</h2>
            <h3>{welcomeContent.subtitle}</h3>
            <p>{welcomeContent.description}</p>
          </div>
        </div>

        <div className="welcome-progress">
          <div className="progress-header">
            <h4>
              {language === "en" ? "Setup Progress" : "Tiến trình thiết lập"}
            </h4>
          </div>

          <div className="progress-steps">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`progress-step ${
                  completedSteps.includes(step.id)
                    ? "completed"
                    : currentStep === index
                    ? "active"
                    : "pending"
                }`}
              >
                <div className="step-icon">
                  {completedSteps.includes(step.id) ? (
                    <FiCheckCircle />
                  ) : currentStep === index ? (
                    <FiLoader className="spin" />
                  ) : (
                    step.icon
                  )}
                </div>
                <div className="step-content">
                  <h5>{step.title}</h5>
                  <p>{step.description}</p>
                </div>
              </div>
            ))}
          </div>

          {!isProcessing && completedSteps.length === steps.length && (
            <div className="progress-complete">
              <div className="complete-message">
                <FiCheckCircle className="complete-icon" />
                <h4>
                  {language === "en"
                    ? "Setup Complete!"
                    : "Thiết lập hoàn tất!"}
                </h4>
                <p>
                  {language === "en"
                    ? "You're ready to start using ZDS DataQuest"
                    : "Bạn đã sẵn sàng sử dụng ZDS DataQuest"}
                </p>
              </div>
              <button className="btn-get-started" onClick={handleComplete}>
                <span>{language === "en" ? "Get Started" : "Bắt đầu"}</span>
                <FiArrowRight />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Welcome;
