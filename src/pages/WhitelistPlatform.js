import React, { useState, useEffect, useRef } from 'react';
import { FiDatabase, FiClock, FiBookmark, FiPlay, FiSave, FiTrash2, FiEdit, FiCopy, FiRefreshCw, FiSend, FiX, FiChevronDown, FiFilter } from 'react-icons/fi';
import '../styles/WhitelistPlatform.css';
import { useTheme } from '../context/ThemeContext';

const WhitelistPlatform = () => {
  const { theme, language } = useTheme();
  const [activeTab, setActiveTab] = useState('query');
  const [queryInput, setQueryInput] = useState('');
  const [promptInput, setPromptInput] = useState('');
  const [queryResult, setQueryResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [history, setHistory] = useState([]);
  const [savedQueries, setSavedQueries] = useState([]);
  const [queryName, setQueryName] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [builderSql, setBuilderSql] = useState('');
  const [isParsingError, setIsParsingError] = useState(false);
  const [isEditingSql, setIsEditingSql] = useState(false);
  const queryInputRef = useRef(null);
  const builderSqlRef = useRef(null);

  // Query Builder state
  const [mainOperator, setMainOperator] = useState('AND'); // Main operator for the entire query
  const [queryGroups, setQueryGroups] = useState([
    {
      id: 1,
      logicalOperator: 'AND',
      rules: [
        { id: 101, field: 'status', operator: 'is', value: 'active' }
      ],
      groups: [] // Nested groups
    }
  ]);

  // Available fields for query builder
  const availableFields = [
    { id: 'status', label: 'Status' },
    { id: 'user_id', label: 'User ID' },
    { id: 'email', label: 'Email' },
    { id: 'name', label: 'Name' },
    { id: 'created_at', label: 'Created At' },
    { id: 'last_login', label: 'Last Login' },
    { id: 'role', label: 'Role' },
    { id: 'department', label: 'Department' },
    { id: 'web_sessions', label: 'Web Sessions' },
    { id: 'last_seen', label: 'Last Seen' },
    { id: 'plan', label: 'Plan' }
  ];

  // Available operators for query builder
  const availableOperators = [
    { id: 'is', label: 'is' },
    { id: 'is_not', label: 'is not' },
    { id: 'contains', label: 'contains' },
    { id: 'starts_with', label: 'starts with' },
    { id: 'ends_with', label: 'ends with' },
    { id: 'greater_than', label: '>' },
    { id: 'less_than', label: '<' },
    { id: 'greater_than_equal', label: '>=' },
    { id: 'less_than_equal', label: '<=' }
  ];

  // Predefined values for certain fields
  const fieldValues = {
    status: ['active', 'inactive', 'pending', 'expired'],
    role: ['admin', 'user', 'manager', 'guest'],
    plan: ['free', 'basic', 'premium', 'enterprise', 'trial'],
    last_seen: ['Less than 7 days ago', 'Less than 30 days ago', 'More than 30 days ago']
  };

  // Load history and saved queries from localStorage on component mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('queryHistory');
    if (savedHistory) {
      setHistory(JSON.parse(savedHistory));
    }

    const savedQueriesList = localStorage.getItem('savedQueries');
    if (savedQueriesList) {
      setSavedQueries(JSON.parse(savedQueriesList));
    }
  }, []);

  // Save history and saved queries to localStorage when they change
  useEffect(() => {
    localStorage.setItem('queryHistory', JSON.stringify(history));
  }, [history]);

  useEffect(() => {
    localStorage.setItem('savedQueries', JSON.stringify(savedQueries));
  }, [savedQueries]);

  // Auto-resize textarea
  useEffect(() => {
    if (queryInputRef.current) {
      queryInputRef.current.style.height = 'auto';
      queryInputRef.current.style.height = `${queryInputRef.current.scrollHeight}px`;
    }
  }, [queryInput]);

  // Auto-resize builder SQL textarea
  useEffect(() => {
    if (builderSqlRef.current) {
      builderSqlRef.current.style.height = 'auto';
      builderSqlRef.current.style.height = `${builderSqlRef.current.scrollHeight}px`;
    }
  }, [builderSql]);

  // Generate SQL whenever query groups change
  useEffect(() => {
    if (!isEditingSql) {
      const sql = generateSqlFromBuilder();
      setBuilderSql(sql);
    }
  }, [queryGroups, isEditingSql]);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleQueryInputChange = (e) => {
    setQueryInput(e.target.value);
  };

  const handleBuilderSqlChange = (e) => {
    setIsEditingSql(true);
    setBuilderSql(e.target.value);
  };

  const handleBuilderSqlBlur = () => {
    try {
      const parsedGroups = parseSqlToQueryGroups(builderSql);
      setQueryGroups(parsedGroups);
      setIsParsingError(false);
    } catch (err) {
      console.error('Error parsing SQL:', err);
      setIsParsingError(true);
    } finally {
      setIsEditingSql(false);
    }
  };

  const handlePromptInputChange = (e) => {
    setPromptInput(e.target.value);
  };

  const handleRunQuery = () => {
    if (!queryInput.trim()) return;

    setIsLoading(true);
    setError(null);

    // Simulate API call with a timeout
    setTimeout(() => {
      try {
        // Mock result - in a real app, this would be the result from the API
        const mockResult = {
          columns: ['id', 'name', 'email', 'created_at'],
          rows: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', created_at: '2023-01-15' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', created_at: '2023-02-20' },
            { id: 3, name: 'Bob Johnson', email: '<EMAIL>', created_at: '2023-03-10' },
          ],
          rowCount: 3,
          executionTime: '0.123s'
        };

        setQueryResult(mockResult);

        // Add to history
        const newHistoryItem = {
          id: Date.now(),
          query: queryInput,
          timestamp: new Date().toISOString(),
          result: mockResult
        };

        setHistory(prevHistory => [newHistoryItem, ...prevHistory]);
        setIsLoading(false);
      } catch (err) {
        setError('Error executing query: ' + err.message);
        setIsLoading(false);
      }
    }, 1000);
  };

  const handleGenerateQuery = () => {
    if (!promptInput.trim()) return;

    setIsGenerating(true);

    // Simulate AI query generation with a timeout
    setTimeout(() => {
      // Mock generated query based on prompt
      let generatedQuery = '';

      if (promptInput.toLowerCase().includes('select')) {
        generatedQuery = `SELECT * FROM users WHERE email LIKE '%${promptInput.split(' ').pop()}%' LIMIT 10;`;
      } else if (promptInput.toLowerCase().includes('count')) {
        generatedQuery = `SELECT COUNT(*) FROM users WHERE status = 'active';`;
      } else {
        generatedQuery = `-- Generated query based on: "${promptInput}"\nSELECT u.id, u.name, u.email, u.created_at\nFROM users u\nJOIN user_profiles p ON u.id = p.user_id\nWHERE u.status = 'active'\nORDER BY u.created_at DESC\nLIMIT 100;`;
      }

      setQueryInput(generatedQuery);
      setIsGenerating(false);
    }, 1500);
  };

  const handleSaveQuery = () => {
    if (!queryInput.trim() || !queryName.trim()) return;

    const newSavedQuery = {
      id: Date.now(),
      name: queryName,
      query: queryInput,
      timestamp: new Date().toISOString()
    };

    setSavedQueries(prevSavedQueries => [newSavedQuery, ...prevSavedQueries]);
    setShowSaveDialog(false);
    setQueryName('');
  };

  const handleDeleteSavedQuery = (id) => {
    setSavedQueries(prevSavedQueries => prevSavedQueries.filter(query => query.id !== id));
  };

  const handleLoadQuery = (query) => {
    setQueryInput(query);
    setActiveTab('query');
  };

  const handleCopyQuery = (query) => {
    navigator.clipboard.writeText(query);
  };

  const handleClearHistory = () => {
    if (window.confirm(language === 'en' ? 'Are you sure you want to clear all history?' : 'Bạn có chắc chắn muốn xóa tất cả lịch sử không?')) {
      setHistory([]);
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  // Generate SQL from the query builder
  const generateSqlFromBuilder = () => {
    if (queryGroups.length === 0 || queryGroups[0].rules.length === 0) {
      return 'SELECT * FROM users LIMIT 100;';
    }

    let sql = 'SELECT * FROM users WHERE ';

    // Function to recursively process groups and their nested groups
    const processGroup = (group, isFirstGroup = false) => {
      let groupSql = '';

      // Open parentheses for this group
      groupSql += '(';

      // Process rules in this group
      group.rules.forEach((rule, ruleIndex) => {
        if (ruleIndex > 0) {
          groupSql += ` ${group.logicalOperator} `;
        }

        // Handle different operators
        switch (rule.operator) {
          case 'is':
            groupSql += `${rule.field} = '${rule.value}'`;
            break;
          case 'is_not':
            groupSql += `${rule.field} != '${rule.value}'`;
            break;
          case 'contains':
            groupSql += `${rule.field} LIKE '%${rule.value}%'`;
            break;
          case 'starts_with':
            groupSql += `${rule.field} LIKE '${rule.value}%'`;
            break;
          case 'ends_with':
            groupSql += `${rule.field} LIKE '%${rule.value}'`;
            break;
          case 'greater_than':
            groupSql += `${rule.field} > ${isNaN(rule.value) ? `'${rule.value}'` : rule.value}`;
            break;
          case 'less_than':
            groupSql += `${rule.field} < ${isNaN(rule.value) ? `'${rule.value}'` : rule.value}`;
            break;
          case 'greater_than_equal':
            groupSql += `${rule.field} >= ${isNaN(rule.value) ? `'${rule.value}'` : rule.value}`;
            break;
          case 'less_than_equal':
            groupSql += `${rule.field} <= ${isNaN(rule.value) ? `'${rule.value}'` : rule.value}`;
            break;
          default:
            groupSql += `${rule.field} = '${rule.value}'`;
        }
      });

      // Process nested groups if any
      if (group.groups && group.groups.length > 0) {
        // If we already have rules, add the logical operator
        if (group.rules.length > 0) {
          groupSql += ` ${group.logicalOperator} `;
        }

        // Process each nested group
        group.groups.forEach((nestedGroup, nestedIndex) => {
          if (nestedIndex > 0) {
            groupSql += ` ${group.logicalOperator} `;
          }
          groupSql += processGroup(nestedGroup);
        });
      }

      // Close parentheses for this group
      groupSql += ')';

      return groupSql;
    };

    // Process top-level groups
    queryGroups.forEach((group, groupIndex) => {
      if (groupIndex > 0) {
        sql += ` ${mainOperator} `; // Top-level groups are connected with the main operator
      }

      sql += processGroup(group, groupIndex === 0);
    });

    sql += ' LIMIT 100;';
    return sql;
  };

  // Parse SQL to query groups
  const parseSqlToQueryGroups = (sql) => {
    // Basic validation
    if (!sql.trim().toLowerCase().startsWith('select')) {
      throw new Error('Invalid SQL: Must start with SELECT');
    }

    // Extract the WHERE clause
    const whereMatch = sql.match(/WHERE\s+(.+?)(?:\s+LIMIT|\s*;|$)/i);
    if (!whereMatch) {
      return [{
        id: Date.now(),
        logicalOperator: 'AND',
        rules: [{ id: Date.now() + 1, field: availableFields[0].id, operator: 'is', value: '' }],
        groups: []
      }];
    }

    const whereClause = whereMatch[1].trim();

    // Determine the main operator (AND or OR)
    const hasMainOr = /\(.*\)\s+OR\s+\(.*\)/i.test(whereClause);
    setMainOperator(hasMainOr ? 'OR' : 'AND');

    // Function to parse a group string into a group object
    const parseGroup = (groupStr) => {
      // Remove outer parentheses if present
      let cleanGroupStr = groupStr;
      if (cleanGroupStr.startsWith('(') && cleanGroupStr.endsWith(')')) {
        cleanGroupStr = cleanGroupStr.substring(1, cleanGroupStr.length - 1);
      }

      // Determine logical operator (AND or OR)
      const hasOr = /\s+OR\s+/i.test(cleanGroupStr);
      const logicalOperator = hasOr ? 'OR' : 'AND';

      // Split by the logical operator to get rules and nested groups
      const separator = hasOr ? /\s+OR\s+/i : /\s+AND\s+/i;
      const parts = cleanGroupStr.split(separator);

      const rules = [];
      const nestedGroups = [];

      // Process each part (could be a rule or a nested group)
      parts.forEach(part => {
        // If it starts with a parenthesis, it's likely a nested group
        if (part.trim().startsWith('(')) {
          nestedGroups.push(parseGroup(part));
        } else {
          // It's a rule
          let field = '';
          let operator = 'is';
          let value = '';

          // Handle different operators
          if (part.includes(' = ')) {
            [field, value] = part.split(' = ');
            operator = 'is';
          } else if (part.includes(' != ')) {
            [field, value] = part.split(' != ');
            operator = 'is_not';
          } else if (part.includes(' LIKE ')) {
            [field, value] = part.split(' LIKE ');

            // Clean up value
            value = value.trim();
            if (value.startsWith("'") && value.endsWith("'")) {
              value = value.substring(1, value.length - 1);
            }

            if (value.startsWith('%') && value.endsWith('%')) {
              operator = 'contains';
              value = value.substring(1, value.length - 1);
            } else if (value.startsWith('%')) {
              operator = 'ends_with';
              value = value.substring(1);
            } else if (value.endsWith('%')) {
              operator = 'starts_with';
              value = value.substring(0, value.length - 1);
            }
          } else if (part.includes(' > ')) {
            [field, value] = part.split(' > ');
            operator = 'greater_than';
          } else if (part.includes(' < ')) {
            [field, value] = part.split(' < ');
            operator = 'less_than';
          } else if (part.includes(' >= ')) {
            [field, value] = part.split(' >= ');
            operator = 'greater_than_equal';
          } else if (part.includes(' <= ')) {
            [field, value] = part.split(' <= ');
            operator = 'less_than_equal';
          }

          // Clean up field and value
          field = field.trim();

          if (typeof value === 'string') {
            value = value.trim();
            // Remove quotes if present
            if (value.startsWith("'") && value.endsWith("'")) {
              value = value.substring(1, value.length - 1);
            }
          }

          if (field) {
            rules.push({
              id: Date.now() + Math.random(),
              field,
              operator,
              value
            });
          }
        }
      });

      return {
        id: Date.now() + Math.random(),
        logicalOperator,
        rules,
        groups: nestedGroups
      };
    };

    // Split by top-level AND or OR to get groups
    const separator = hasMainOr ? /\s+OR\s+/i : /\s+AND\s+/i;
    const groupsStr = whereClause.split(separator);

    return groupsStr.map(groupStr => parseGroup(groupStr));
  };

  return (
    <div className="whitelist-platform-container">
      <div className="tabs-container">
        <div className="tabs-header">
          <button
            className={`tab-button ${activeTab === 'query' ? 'active' : ''}`}
            onClick={() => handleTabChange('query')}
          >
            <FiDatabase className="tab-icon" />
            <span>{language === 'en' ? 'Query' : 'Truy vấn'}</span>
          </button>
          <button
            className={`tab-button ${activeTab === 'builder' ? 'active' : ''}`}
            onClick={() => handleTabChange('builder')}
          >
            <FiFilter className="tab-icon" />
            <span>{language === 'en' ? 'Query Builder' : 'Trình tạo truy vấn'}</span>
          </button>
          <button
            className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
            onClick={() => handleTabChange('history')}
          >
            <FiClock className="tab-icon" />
            <span>{language === 'en' ? 'History' : 'Lịch sử'}</span>
          </button>
          <button
            className={`tab-button ${activeTab === 'saved' ? 'active' : ''}`}
            onClick={() => handleTabChange('saved')}
          >
            <FiBookmark className="tab-icon" />
            <span>{language === 'en' ? 'Saved Queries' : 'Truy vấn đã lưu'}</span>
          </button>
        </div>

        <div className="tab-content">
          {activeTab === 'query' && (
            <div className="query-tab">
              <div className="prompt-section">
                <h3>{language === 'en' ? 'Generate Query' : 'Tạo truy vấn'}</h3>
                <div className="prompt-input-container">
                  <input
                    type="text"
                    className="prompt-input"
                    placeholder={language === 'en' ? 'Enter a prompt to generate PostgreSQL query...' : 'Nhập mô tả để tạo truy vấn PostgreSQL...'}
                    value={promptInput}
                    onChange={handlePromptInputChange}
                  />
                  <button
                    className="generate-button"
                    onClick={handleGenerateQuery}
                    disabled={!promptInput.trim() || isGenerating}
                  >
                    {isGenerating ? <FiRefreshCw className="spin" /> : <FiSend />}
                    <span>{language === 'en' ? 'Generate' : 'Tạo'}</span>
                  </button>
                </div>
              </div>

              <div className="query-section">
                <h3>{language === 'en' ? 'SQL Query' : 'Truy vấn SQL'}</h3>
                <div className="query-input-container">
                  <textarea
                    ref={queryInputRef}
                    className="query-input"
                    placeholder={language === 'en' ? 'Enter your PostgreSQL query here...' : 'Nhập truy vấn PostgreSQL của bạn tại đây...'}
                    value={queryInput}
                    onChange={handleQueryInputChange}
                    rows={5}
                  />
                  <div className="query-actions">
                    <button
                      className="run-button"
                      onClick={handleRunQuery}
                      disabled={!queryInput.trim() || isLoading}
                    >
                      {isLoading ? <FiRefreshCw className="spin" /> : <FiPlay />}
                      <span>{language === 'en' ? 'Run Query' : 'Chạy truy vấn'}</span>
                    </button>
                    <button
                      className="save-button"
                      onClick={() => setShowSaveDialog(true)}
                      disabled={!queryInput.trim()}
                    >
                      <FiSave />
                      <span>{language === 'en' ? 'Save Query' : 'Lưu truy vấn'}</span>
                    </button>
                  </div>
                </div>
              </div>

              {error && (
                <div className="error-message">
                  {error}
                </div>
              )}

              {queryResult && (
                <div className="query-result">
                  <div className="result-header">
                    <h3>{language === 'en' ? 'Query Result' : 'Kết quả truy vấn'}</h3>
                    <div className="result-meta">
                      <span>{queryResult.rowCount} {language === 'en' ? 'rows' : 'dòng'}</span>
                      <span>{queryResult.executionTime}</span>
                    </div>
                  </div>

                  <div className="result-table-container">
                    <table className="result-table">
                      <thead>
                        <tr>
                          {queryResult.columns.map((column, index) => (
                            <th key={index}>{column}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {queryResult.rows.map((row, rowIndex) => (
                          <tr key={rowIndex}>
                            {queryResult.columns.map((column, colIndex) => (
                              <td key={colIndex}>{row[column]}</td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'builder' && (
            <div className="builder-tab">
              <div className="builder-header">
                <h3>{language === 'en' ? 'Query Builder' : 'Trình tạo truy vấn'}</h3>
              </div>

              <div className="query-builder-container">
                {queryGroups.map((group, groupIndex) => (
                  <div key={group.id} className="query-group">
                    <div className="group-header">
                      <div className="group-header-buttons">
                        <div className="logical-operator-container">
                          <button
                            className={`logical-operator-btn ${group.logicalOperator === 'AND' ? 'active' : ''}`}
                            onClick={() => {
                              const updatedGroups = [...queryGroups];
                              updatedGroups[groupIndex].logicalOperator = 'AND';
                              setQueryGroups(updatedGroups);
                            }}
                          >
                            AND
                          </button>
                          <button
                            className={`logical-operator-btn ${group.logicalOperator === 'OR' ? 'active' : ''}`}
                            onClick={() => {
                              const updatedGroups = [...queryGroups];
                              updatedGroups[groupIndex].logicalOperator = 'OR';
                              setQueryGroups(updatedGroups);
                            }}
                          >
                            OR
                          </button>
                        </div>

                        <button
                          className="action-btn add-rule-btn"
                          onClick={() => {
                            const updatedGroups = [...queryGroups];
                            updatedGroups[groupIndex].rules.push({
                              id: Date.now(),
                              field: availableFields[0].id,
                              operator: availableOperators[0].id,
                              value: ''
                            });
                            setQueryGroups(updatedGroups);
                          }}
                          title={language === 'en' ? 'Add Rule' : 'Thêm điều kiện'}
                        >
                          + Rule
                        </button>

                        <button
                          className="action-btn add-group-btn"
                          onClick={() => {
                            const updatedGroups = [...queryGroups];
                            const newGroup = {
                              id: Date.now(),
                              logicalOperator: 'AND',
                              rules: [{ id: Date.now() + 1, field: availableFields[0].id, operator: availableOperators[0].id, value: '' }],
                              groups: []
                            };

                            // Add the new group as a child of the current group
                            updatedGroups[groupIndex].groups.push(newGroup);
                            setQueryGroups(updatedGroups);
                          }}
                          title={language === 'en' ? 'Add Nested Group' : 'Thêm nhóm con'}
                        >
                          + Group
                        </button>

                        <button
                          className="action-btn remove-group-btn"
                          onClick={() => {
                            if (queryGroups.length > 1) {
                              const updatedGroups = [...queryGroups];
                              updatedGroups.splice(groupIndex, 1);
                              setQueryGroups(updatedGroups);
                            } else {
                              // If it's the last group, just clear its rules
                              const updatedGroups = [...queryGroups];
                              updatedGroups[0].rules = [{
                                id: Date.now(),
                                field: availableFields[0].id,
                                operator: availableOperators[0].id,
                                value: ''
                              }];
                              setQueryGroups(updatedGroups);
                            }
                          }}
                          title={language === 'en' ? 'Remove Group' : 'Xóa nhóm'}
                        >
                          <FiTrash2 size={14} />
                        </button>
                      </div>
                    </div>

                    <div className="rules-container">
                      {group.rules.map((rule, ruleIndex) => (
                        <div key={rule.id} className="rule-row">
                          <div className="rule-connector-line"></div>

                          <div className="rule-content">
                            <div className="rule-field-container">
                              <div className="select-container">
                                <select
                                  value={rule.field}
                                  onChange={(e) => {
                                    const updatedGroups = [...queryGroups];
                                    updatedGroups[groupIndex].rules[ruleIndex].field = e.target.value;
                                    setQueryGroups(updatedGroups);
                                  }}
                                >
                                  {availableFields.map(field => (
                                    <option key={field.id} value={field.id}>{field.label}</option>
                                  ))}
                                </select>
                                <FiChevronDown className="select-arrow" />
                              </div>
                            </div>

                            <div className="rule-operator-container">
                              <div className="select-container">
                                <select
                                  value={rule.operator}
                                  onChange={(e) => {
                                    const updatedGroups = [...queryGroups];
                                    updatedGroups[groupIndex].rules[ruleIndex].operator = e.target.value;
                                    setQueryGroups(updatedGroups);
                                  }}
                                >
                                  {availableOperators.map(operator => (
                                    <option key={operator.id} value={operator.id}>{operator.label}</option>
                                  ))}
                                </select>
                                <FiChevronDown className="select-arrow" />
                              </div>
                            </div>

                            <div className="rule-value-container">
                              {fieldValues[rule.field] ? (
                                <div className="select-container">
                                  <select
                                    value={rule.value}
                                    onChange={(e) => {
                                      const updatedGroups = [...queryGroups];
                                      updatedGroups[groupIndex].rules[ruleIndex].value = e.target.value;
                                      setQueryGroups(updatedGroups);
                                    }}
                                  >
                                    <option value="">Select a value</option>
                                    {fieldValues[rule.field].map((value, idx) => (
                                      <option key={idx} value={value}>{value}</option>
                                    ))}
                                  </select>
                                  <FiChevronDown className="select-arrow" />
                                </div>
                              ) : (
                                <input
                                  type="text"
                                  value={rule.value}
                                  onChange={(e) => {
                                    const updatedGroups = [...queryGroups];
                                    updatedGroups[groupIndex].rules[ruleIndex].value = e.target.value;
                                    setQueryGroups(updatedGroups);
                                  }}
                                  placeholder={language === 'en' ? 'Enter value...' : 'Nhập giá trị...'}
                                />
                              )}
                            </div>

                            <button
                              className="remove-rule-btn"
                              onClick={() => {
                                const updatedGroups = [...queryGroups];
                                updatedGroups[groupIndex].rules.splice(ruleIndex, 1);
                                // If no rules left in the group, remove the group (unless it's the only group)
                                if (updatedGroups[groupIndex].rules.length === 0 &&
                                    updatedGroups[groupIndex].groups.length === 0 &&
                                    updatedGroups.length > 1) {
                                  updatedGroups.splice(groupIndex, 1);
                                }
                                setQueryGroups(updatedGroups);
                              }}
                            >
                              <FiX size={14} />
                            </button>
                          </div>
                        </div>
                      ))}

                      {/* Nested Groups */}
                      {group.groups && group.groups.length > 0 && (
                        <div className="sub-groups-container">
                          {group.groups.map((nestedGroup, nestedGroupIndex) => (
                            <div key={nestedGroup.id} className="query-group sub-group" data-parent-id={group.id}>
                              <div className="group-header">
                                <div className="group-header-buttons">
                                  <div className="logical-operator-container">
                                    <button
                                      className={`logical-operator-btn ${nestedGroup.logicalOperator === 'AND' ? 'active' : ''}`}
                                      onClick={() => {
                                        const updatedGroups = [...queryGroups];
                                        updatedGroups[groupIndex].groups[nestedGroupIndex].logicalOperator = 'AND';
                                        setQueryGroups(updatedGroups);
                                      }}
                                    >
                                      AND
                                    </button>
                                    <button
                                      className={`logical-operator-btn ${nestedGroup.logicalOperator === 'OR' ? 'active' : ''}`}
                                      onClick={() => {
                                        const updatedGroups = [...queryGroups];
                                        updatedGroups[groupIndex].groups[nestedGroupIndex].logicalOperator = 'OR';
                                        setQueryGroups(updatedGroups);
                                      }}
                                    >
                                      OR
                                    </button>
                                  </div>

                                  <button
                                    className="action-btn add-rule-btn"
                                    onClick={() => {
                                      const updatedGroups = [...queryGroups];
                                      updatedGroups[groupIndex].groups[nestedGroupIndex].rules.push({
                                        id: Date.now(),
                                        field: availableFields[0].id,
                                        operator: availableOperators[0].id,
                                        value: ''
                                      });
                                      setQueryGroups(updatedGroups);
                                    }}
                                    title={language === 'en' ? 'Add Rule' : 'Thêm điều kiện'}
                                  >
                                    + Rule
                                  </button>

                                  <button
                                    className="action-btn add-group-btn"
                                    onClick={() => {
                                      const updatedGroups = [...queryGroups];
                                      const newGroup = {
                                        id: Date.now(),
                                        logicalOperator: 'AND',
                                        rules: [{ id: Date.now() + 1, field: availableFields[0].id, operator: availableOperators[0].id, value: '' }],
                                        groups: []
                                      };

                                      // Add the new group as a child of this nested group
                                      updatedGroups[groupIndex].groups[nestedGroupIndex].groups.push(newGroup);
                                      setQueryGroups(updatedGroups);
                                    }}
                                    title={language === 'en' ? 'Add Nested Group' : 'Thêm nhóm con'}
                                  >
                                    + Group
                                  </button>

                                  <button
                                    className="action-btn remove-group-btn"
                                    onClick={() => {
                                      const updatedGroups = [...queryGroups];
                                      updatedGroups[groupIndex].groups.splice(nestedGroupIndex, 1);
                                      setQueryGroups(updatedGroups);
                                    }}
                                    title={language === 'en' ? 'Remove Group' : 'Xóa nhóm'}
                                  >
                                    <FiTrash2 size={14} />
                                  </button>
                                </div>
                              </div>

                              <div className="rules-container">
                                {nestedGroup.rules.map((rule, ruleIndex) => (
                                  <div key={rule.id} className="rule-row">
                                    <div className="rule-connector-line"></div>

                                    <div className="rule-content">
                                      <div className="rule-field-container">
                                        <div className="select-container">
                                          <select
                                            value={rule.field}
                                            onChange={(e) => {
                                              const updatedGroups = [...queryGroups];
                                              updatedGroups[groupIndex].groups[nestedGroupIndex].rules[ruleIndex].field = e.target.value;
                                              setQueryGroups(updatedGroups);
                                            }}
                                          >
                                            {availableFields.map(field => (
                                              <option key={field.id} value={field.id}>{field.label}</option>
                                            ))}
                                          </select>
                                          <FiChevronDown className="select-arrow" />
                                        </div>
                                      </div>

                                      <div className="rule-operator-container">
                                        <div className="select-container">
                                          <select
                                            value={rule.operator}
                                            onChange={(e) => {
                                              const updatedGroups = [...queryGroups];
                                              updatedGroups[groupIndex].groups[nestedGroupIndex].rules[ruleIndex].operator = e.target.value;
                                              setQueryGroups(updatedGroups);
                                            }}
                                          >
                                            {availableOperators.map(operator => (
                                              <option key={operator.id} value={operator.id}>{operator.label}</option>
                                            ))}
                                          </select>
                                          <FiChevronDown className="select-arrow" />
                                        </div>
                                      </div>

                                      <div className="rule-value-container">
                                        {fieldValues[rule.field] ? (
                                          <div className="select-container">
                                            <select
                                              value={rule.value}
                                              onChange={(e) => {
                                                const updatedGroups = [...queryGroups];
                                                updatedGroups[groupIndex].groups[nestedGroupIndex].rules[ruleIndex].value = e.target.value;
                                                setQueryGroups(updatedGroups);
                                              }}
                                            >
                                              <option value="">Select a value</option>
                                              {fieldValues[rule.field].map((value, idx) => (
                                                <option key={idx} value={value}>{value}</option>
                                              ))}
                                            </select>
                                            <FiChevronDown className="select-arrow" />
                                          </div>
                                        ) : (
                                          <input
                                            type="text"
                                            value={rule.value}
                                            onChange={(e) => {
                                              const updatedGroups = [...queryGroups];
                                              updatedGroups[groupIndex].groups[nestedGroupIndex].rules[ruleIndex].value = e.target.value;
                                              setQueryGroups(updatedGroups);
                                            }}
                                            placeholder={language === 'en' ? 'Enter value...' : 'Nhập giá trị...'}
                                          />
                                        )}
                                      </div>

                                      <button
                                        className="remove-rule-btn"
                                        onClick={() => {
                                          const updatedGroups = [...queryGroups];
                                          updatedGroups[groupIndex].groups[nestedGroupIndex].rules.splice(ruleIndex, 1);
                                          // If no rules left in the nested group, remove it
                                          if (updatedGroups[groupIndex].groups[nestedGroupIndex].rules.length === 0 &&
                                              updatedGroups[groupIndex].groups[nestedGroupIndex].groups.length === 0) {
                                            updatedGroups[groupIndex].groups.splice(nestedGroupIndex, 1);
                                          }
                                          setQueryGroups(updatedGroups);
                                        }}
                                      >
                                        <FiX size={14} />
                                      </button>
                                    </div>
                                  </div>
                                ))}
                              </div>

                              {/* Level 3 groups */}
                              {nestedGroup.groups && nestedGroup.groups.length > 0 && (
                                <div className="sub-groups-container">
                                  {nestedGroup.groups.map((subGroup, subGroupIndex) => (
                                    <div key={subGroup.id} className="query-group sub-group level-3" data-parent-id={nestedGroup.id}>
                                      <div className="group-header">
                                        <div className="group-header-buttons">
                                          <div className="logical-operator-container">
                                            <button
                                              className={`logical-operator-btn ${subGroup.logicalOperator === 'AND' ? 'active' : ''}`}
                                              onClick={() => {
                                                const updatedGroups = [...queryGroups];
                                                updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].logicalOperator = 'AND';
                                                setQueryGroups(updatedGroups);
                                              }}
                                            >
                                              AND
                                            </button>
                                            <button
                                              className={`logical-operator-btn ${subGroup.logicalOperator === 'OR' ? 'active' : ''}`}
                                              onClick={() => {
                                                const updatedGroups = [...queryGroups];
                                                updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].logicalOperator = 'OR';
                                                setQueryGroups(updatedGroups);
                                              }}
                                            >
                                              OR
                                            </button>
                                          </div>

                                          <button
                                            className="action-btn add-rule-btn"
                                            onClick={() => {
                                              const updatedGroups = [...queryGroups];
                                              updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].rules.push({
                                                id: Date.now(),
                                                field: availableFields[0].id,
                                                operator: availableOperators[0].id,
                                                value: ''
                                              });
                                              setQueryGroups(updatedGroups);
                                            }}
                                            title={language === 'en' ? 'Add Rule' : 'Thêm điều kiện'}
                                          >
                                            + Rule
                                          </button>

                                          <button
                                            className="action-btn remove-group-btn"
                                            onClick={() => {
                                              const updatedGroups = [...queryGroups];
                                              updatedGroups[groupIndex].groups[nestedGroupIndex].groups.splice(subGroupIndex, 1);
                                              setQueryGroups(updatedGroups);
                                            }}
                                            title={language === 'en' ? 'Remove Group' : 'Xóa nhóm'}
                                          >
                                            <FiTrash2 size={14} />
                                          </button>
                                        </div>
                                      </div>

                                      <div className="rules-container">
                                        {subGroup.rules.map((rule, ruleIndex) => (
                                          <div key={rule.id} className="rule-row">
                                            <div className="rule-connector-line"></div>

                                            <div className="rule-content">
                                              <div className="rule-field-container">
                                                <div className="select-container">
                                                  <select
                                                    value={rule.field}
                                                    onChange={(e) => {
                                                      const updatedGroups = [...queryGroups];
                                                      updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].rules[ruleIndex].field = e.target.value;
                                                      setQueryGroups(updatedGroups);
                                                    }}
                                                  >
                                                    {availableFields.map(field => (
                                                      <option key={field.id} value={field.id}>{field.label}</option>
                                                    ))}
                                                  </select>
                                                  <FiChevronDown className="select-arrow" />
                                                </div>
                                              </div>

                                              <div className="rule-operator-container">
                                                <div className="select-container">
                                                  <select
                                                    value={rule.operator}
                                                    onChange={(e) => {
                                                      const updatedGroups = [...queryGroups];
                                                      updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].rules[ruleIndex].operator = e.target.value;
                                                      setQueryGroups(updatedGroups);
                                                    }}
                                                  >
                                                    {availableOperators.map(operator => (
                                                      <option key={operator.id} value={operator.id}>{operator.label}</option>
                                                    ))}
                                                  </select>
                                                  <FiChevronDown className="select-arrow" />
                                                </div>
                                              </div>

                                              <div className="rule-value-container">
                                                {fieldValues[rule.field] ? (
                                                  <div className="select-container">
                                                    <select
                                                      value={rule.value}
                                                      onChange={(e) => {
                                                        const updatedGroups = [...queryGroups];
                                                        updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].rules[ruleIndex].value = e.target.value;
                                                        setQueryGroups(updatedGroups);
                                                      }}
                                                    >
                                                      <option value="">Select a value</option>
                                                      {fieldValues[rule.field].map((value, idx) => (
                                                        <option key={idx} value={value}>{value}</option>
                                                      ))}
                                                    </select>
                                                    <FiChevronDown className="select-arrow" />
                                                  </div>
                                                ) : (
                                                  <input
                                                    type="text"
                                                    value={rule.value}
                                                    onChange={(e) => {
                                                      const updatedGroups = [...queryGroups];
                                                      updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].rules[ruleIndex].value = e.target.value;
                                                      setQueryGroups(updatedGroups);
                                                    }}
                                                    placeholder={language === 'en' ? 'Enter value...' : 'Nhập giá trị...'}
                                                  />
                                                )}
                                              </div>

                                              <button
                                                className="remove-rule-btn"
                                                onClick={() => {
                                                  const updatedGroups = [...queryGroups];
                                                  updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].rules.splice(ruleIndex, 1);
                                                  // If no rules left in the level 3 group, remove it
                                                  if (updatedGroups[groupIndex].groups[nestedGroupIndex].groups[subGroupIndex].rules.length === 0) {
                                                    updatedGroups[groupIndex].groups[nestedGroupIndex].groups.splice(subGroupIndex, 1);
                                                  }
                                                  setQueryGroups(updatedGroups);
                                                }}
                                              >
                                                <FiX size={14} />
                                              </button>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {/* Bottom actions removed as they're now in each group header */}
              </div>

              <div className="sql-preview-section">
                <h3>{language === 'en' ? 'SQL Query' : 'Truy vấn SQL'}</h3>
                <div className={`sql-preview-container ${isParsingError ? 'error' : ''}`}>
                  <textarea
                    ref={builderSqlRef}
                    className="sql-preview"
                    value={builderSql}
                    onChange={handleBuilderSqlChange}
                    onBlur={handleBuilderSqlBlur}
                    placeholder={language === 'en' ? 'SQL query will appear here...' : 'Truy vấn SQL sẽ xuất hiện ở đây...'}
                  />
                  {isParsingError && (
                    <div className="parsing-error-message">
                      {language === 'en' ? 'Error parsing SQL. Please check your syntax.' : 'Lỗi phân tích SQL. Vui lòng kiểm tra cú pháp của bạn.'}
                    </div>
                  )}
                </div>
              </div>

              <div className="builder-footer">
                <div className="builder-footer-actions">
                  <button
                    className="clear-btn"
                    onClick={() => {
                      // Reset to a single group with a single rule
                      setQueryGroups([{
                        id: Date.now(),
                        logicalOperator: 'AND',
                        rules: [{ id: Date.now() + 1, field: availableFields[0].id, operator: availableOperators[0].id, value: '' }],
                        groups: []
                      }]);
                      setIsParsingError(false);
                    }}
                  >
                    {language === 'en' ? 'Clear' : 'Xóa'}
                  </button>

                  <button
                    className="apply-btn"
                    onClick={() => {
                      // Use the current builder SQL
                      setQueryInput(builderSql);
                      setActiveTab('query');
                    }}
                  >
                    {language === 'en' ? 'Apply' : 'Áp dụng'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="history-tab">
              <div className="history-header">
                <h3>{language === 'en' ? 'Query History' : 'Lịch sử truy vấn'}</h3>
                {history.length > 0 && (
                  <button
                    className="clear-history-button"
                    onClick={handleClearHistory}
                  >
                    <FiTrash2 />
                    <span>{language === 'en' ? 'Clear History' : 'Xóa lịch sử'}</span>
                  </button>
                )}
              </div>

              {history.length === 0 ? (
                <div className="empty-state">
                  {language === 'en' ? 'No query history yet. Run some queries to see them here.' : 'Chưa có lịch sử truy vấn. Chạy một số truy vấn để xem chúng ở đây.'}
                </div>
              ) : (
                <div className="history-list">
                  {history.map((item) => (
                    <div key={item.id} className="history-item">
                      <div className="history-item-header">
                        <span className="history-timestamp">{formatTimestamp(item.timestamp)}</span>
                        <div className="history-actions">
                          <button
                            className="history-action-button"
                            onClick={() => handleLoadQuery(item.query)}
                            title={language === 'en' ? 'Load Query' : 'Tải truy vấn'}
                          >
                            <FiPlay />
                          </button>
                          <button
                            className="history-action-button"
                            onClick={() => handleCopyQuery(item.query)}
                            title={language === 'en' ? 'Copy Query' : 'Sao chép truy vấn'}
                          >
                            <FiCopy />
                          </button>
                          <button
                            className="history-action-button"
                            onClick={() => {
                              setQueryInput(item.query);
                              setShowSaveDialog(true);
                            }}
                            title={language === 'en' ? 'Save Query' : 'Lưu truy vấn'}
                          >
                            <FiSave />
                          </button>
                        </div>
                      </div>
                      <pre className="history-query">{item.query}</pre>
                      <div className="history-result-meta">
                        <span>{item.result.rowCount} {language === 'en' ? 'rows' : 'dòng'}</span>
                        <span>{item.result.executionTime}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'saved' && (
            <div className="saved-tab">
              <div className="saved-header">
                <h3>{language === 'en' ? 'Saved Queries' : 'Truy vấn đã lưu'}</h3>
              </div>

              {savedQueries.length === 0 ? (
                <div className="empty-state">
                  {language === 'en' ? 'No saved queries yet. Save some queries to see them here.' : 'Chưa có truy vấn đã lưu. Lưu một số truy vấn để xem chúng ở đây.'}
                </div>
              ) : (
                <div className="saved-list">
                  {savedQueries.map((item) => (
                    <div key={item.id} className="saved-item">
                      <div className="saved-item-header">
                        <h4 className="saved-name">{item.name}</h4>
                        <div className="saved-actions">
                          <button
                            className="saved-action-button"
                            onClick={() => handleLoadQuery(item.query)}
                            title={language === 'en' ? 'Load Query' : 'Tải truy vấn'}
                          >
                            <FiPlay />
                          </button>
                          <button
                            className="saved-action-button"
                            onClick={() => handleCopyQuery(item.query)}
                            title={language === 'en' ? 'Copy Query' : 'Sao chép truy vấn'}
                          >
                            <FiCopy />
                          </button>
                          <button
                            className="saved-action-button"
                            onClick={() => handleDeleteSavedQuery(item.id)}
                            title={language === 'en' ? 'Delete Query' : 'Xóa truy vấn'}
                          >
                            <FiTrash2 />
                          </button>
                        </div>
                      </div>
                      <pre className="saved-query">{item.query}</pre>
                      <span className="saved-timestamp">{formatTimestamp(item.timestamp)}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {showSaveDialog && (
        <div className="save-dialog-overlay">
          <div className="save-dialog">
            <h3>{language === 'en' ? 'Save Query' : 'Lưu truy vấn'}</h3>
            <input
              type="text"
              className="query-name-input"
              placeholder={language === 'en' ? 'Enter a name for this query' : 'Nhập tên cho truy vấn này'}
              value={queryName}
              onChange={(e) => setQueryName(e.target.value)}
            />
            <div className="save-dialog-actions">
              <button
                className="cancel-button"
                onClick={() => {
                  setShowSaveDialog(false);
                  setQueryName('');
                }}
              >
                {language === 'en' ? 'Cancel' : 'Hủy'}
              </button>
              <button
                className="confirm-save-button"
                onClick={handleSaveQuery}
                disabled={!queryName.trim()}
              >
                {language === 'en' ? 'Save' : 'Lưu'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WhitelistPlatform;
