import React from 'react';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import '../styles/BasicQuill.css';

/**
 * BasicQuillEditor - Một component ReactQuill đơn giản
 *
 * @param {Object} props - Component props
 * @param {string} props.value - Nội dung của editor
 * @param {Function} props.onChange - Hàm xử lý khi nội dung thay đổi
 * @param {string} props.placeholder - Placeholder text
 * @returns {JSX.Element} - Component BasicQuillEditor
 */
const BasicQuillEditor = ({
  value,
  onChange,
  placeholder = 'Nhập nội dung...',
  ...rest
}) => {
  // Cấu hình toolbar standard
  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'color': [] }, { 'background': [] }],
      ['link', 'image'],
      ['clean']
    ]
  };

  // Các định dạng được hỗ trợ
  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'color', 'background',
    'link', 'image'
  ];

  return (
    <ReactQuill
      theme="snow"
      value={value || ''}
      onChange={onChange}
      modules={modules}
      formats={formats}
      placeholder={placeholder}
      className="basic-quill-editor"
      {...rest}
    />
  );
};

export default BasicQuillEditor;
