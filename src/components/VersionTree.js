import React, { useState, useEffect } from 'react';
import { FiChevronRight, FiList } from 'react-icons/fi';
import { useTheme } from '../context/ThemeContext';

// Helper function to parse version strings
const parseVersion = (versionStr) => {
  // Remove 'v' prefix if present
  const cleanVersion = versionStr.startsWith('v') ? versionStr.substring(1) : versionStr;
  
  // Split by dots and convert to numbers
  const parts = cleanVersion.split('.').map(part => {
    const num = parseInt(part, 10);
    return isNaN(num) ? 0 : num;
  });
  
  // Ensure we have at least 3 parts (major.minor.patch)
  while (parts.length < 3) {
    parts.push(0);
  }
  
  return {
    major: parts[0],
    minor: parts[1],
    patch: parts[2],
    original: versionStr
  };
};

// Helper function to organize versions into a tree structure
const organizeVersions = (versions) => {
  const tree = {};
  
  versions.forEach(version => {
    const parsed = parseVersion(version);
    
    if (!tree[parsed.major]) {
      tree[parsed.major] = {
        versions: [],
        minors: {}
      };
    }
    
    if (!tree[parsed.major].minors[parsed.minor]) {
      tree[parsed.major].minors[parsed.minor] = {
        versions: []
      };
    }
    
    // Add to appropriate collections
    tree[parsed.major].versions.push(version);
    tree[parsed.major].minors[parsed.minor].versions.push(version);
  });
  
  return tree;
};

const TreeNode = ({ label, children, isActive, onClick, isExpanded, onToggle }) => {
  return (
    <div className="version-tree-node">
      <div 
        className={`version-tree-parent ${isActive ? 'active' : ''}`}
        onClick={onClick}
      >
        {children && (
          <span 
            className={`version-tree-toggle ${isExpanded ? 'expanded' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              onToggle();
            }}
          >
            <FiChevronRight />
          </span>
        )}
        <span>{label}</span>
      </div>
      {children && (
        <div className={`version-tree-children ${isExpanded ? 'expanded' : ''}`}>
          {children}
        </div>
      )}
    </div>
  );
};

const VersionTree = ({ releases, selectedVersion, onSelectVersion }) => {
  const { language, theme } = useTheme();
  const [expandedNodes, setExpandedNodes] = useState({});
  const [versionTree, setVersionTree] = useState({});
  
  useEffect(() => {
    // Extract unique versions from releases
    const versions = [...new Set(releases.map(release => release.version))];
    
    // Organize versions into a tree structure
    const tree = organizeVersions(versions);
    setVersionTree(tree);
    
    // Expand the first major version by default
    if (Object.keys(tree).length > 0) {
      const firstMajor = Object.keys(tree).sort((a, b) => b - a)[0];
      setExpandedNodes(prev => ({
        ...prev,
        [`major-${firstMajor}`]: true
      }));
    }
  }, [releases]);
  
  const toggleNode = (nodeId) => {
    setExpandedNodes(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }));
  };
  
  const renderMajorVersions = () => {
    return Object.keys(versionTree)
      .sort((a, b) => b - a) // Sort in descending order
      .map(major => {
        const majorNodeId = `major-${major}`;
        const isExpanded = !!expandedNodes[majorNodeId];
        
        return (
          <TreeNode
            key={majorNodeId}
            label={`v${major}.x`}
            isActive={selectedVersion && selectedVersion.startsWith(`v${major}.`)}
            onClick={() => onSelectVersion(`v${major}`)}
            isExpanded={isExpanded}
            onToggle={() => toggleNode(majorNodeId)}
          >
            {renderMinorVersions(major)}
          </TreeNode>
        );
      });
  };
  
  const renderMinorVersions = (major) => {
    return Object.keys(versionTree[major].minors)
      .sort((a, b) => b - a) // Sort in descending order
      .map(minor => {
        const minorNodeId = `minor-${major}-${minor}`;
        const isExpanded = !!expandedNodes[minorNodeId];
        
        return (
          <TreeNode
            key={minorNodeId}
            label={`v${major}.${minor}.x`}
            isActive={selectedVersion && selectedVersion.startsWith(`v${major}.${minor}.`)}
            onClick={() => onSelectVersion(`v${major}.${minor}`)}
            isExpanded={isExpanded}
            onToggle={() => toggleNode(minorNodeId)}
          >
            {renderPatchVersions(major, minor)}
          </TreeNode>
        );
      });
  };
  
  const renderPatchVersions = (major, minor) => {
    return versionTree[major].minors[minor].versions
      .sort((a, b) => {
        const parsedA = parseVersion(a);
        const parsedB = parseVersion(b);
        return parsedB.patch - parsedA.patch;
      })
      .map(version => (
        <TreeNode
          key={version}
          label={version}
          isActive={selectedVersion === version}
          onClick={() => onSelectVersion(version)}
        />
      ));
  };
  
  return (
    <div className="version-tree-container">
      <div className="version-tree-title">
        <FiList />
        {language === 'en' ? 'Version Filter' : 'Bộ lọc phiên bản'}
      </div>
      <div className="version-tree">
        <TreeNode
          label={language === 'en' ? 'All Versions' : 'Tất cả phiên bản'}
          isActive={!selectedVersion}
          onClick={() => onSelectVersion(null)}
        />
        {renderMajorVersions()}
      </div>
    </div>
  );
};

export default VersionTree;
