import React from 'react';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import 'react-quill-new/dist/quill.bubble.css';
import '../styles/CustomQuill.css';
import { editorConfigs } from '../utils/quillThemes';

/**
 * CustomQuillEditor - A reusable React Quill editor component with customizable themes
 * 
 * @param {Object} props - Component props
 * @param {string} props.value - The editor content
 * @param {Function} props.onChange - Function to handle content changes
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.configType - Editor configuration type ('simple', 'standard', 'rich', 'bubble')
 * @param {Object} props.customStyle - Additional custom styles to apply
 * @param {string} props.label - Label text for the editor
 * @param {string} props.name - Name attribute for the editor
 * @param {boolean} props.required - Whether the editor is required
 * @param {string} props.className - Additional CSS class names
 * @returns {JSX.Element} - The CustomQuillEditor component
 */
const CustomQuillEditor = ({
  value,
  onChange,
  placeholder,
  configType = 'standard',
  customStyle = {},
  label,
  name,
  required = false,
  className = '',
  ...rest
}) => {
  // Get the editor configuration based on the configType
  const config = editorConfigs[configType] || editorConfigs.standard;
  
  // Merge custom styles with the configuration styles
  const mergedStyle = { ...config.style, ...customStyle };
  
  return (
    <div className={`quill-editor-wrapper ${className}`}>
      {label && (
        <label htmlFor={name} className="quill-editor-label">
          {label}{required && <span className="required-mark">*</span>}
        </label>
      )}
      
      <ReactQuill
        theme={config.theme}
        value={value || ''}
        onChange={onChange}
        modules={config.modules}
        formats={config.formats}
        placeholder={placeholder || config.placeholder}
        style={mergedStyle}
        className="custom-quill-editor"
        {...rest}
      />
    </div>
  );
};

export default CustomQuillEditor;
