import React, { useEffect, useRef } from 'react';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import '../styles/CustomQuill.css';
import { createCustomTheme, registerCustomFormats, addCustomHandlers } from '../utils/quillCustomTheme';

/**
 * AdvancedQuillEditor - A React Quill editor with custom theme and formats
 * 
 * This component demonstrates how to create and use a custom theme for ReactQuill
 * along with custom formats and handlers.
 * 
 * @param {Object} props - Component props
 * @param {string} props.value - The editor content
 * @param {Function} props.onChange - Function to handle content changes
 * @param {string} props.placeholder - Placeholder text
 * @returns {JSX.Element} - The AdvancedQuillEditor component
 */
const AdvancedQuillEditor = ({
  value,
  onChange,
  placeholder = 'Start writing...',
  ...rest
}) => {
  const quillRef = useRef(null);
  
  useEffect(() => {
    if (quillRef.current) {
      const Quill = quillRef.current.getEditor().constructor;
      
      // Register custom theme
      createCustomTheme(Quill);
      
      // Register custom formats
      registerCustomFormats(Quill);
      
      // Add custom handlers
      const quillInstance = quillRef.current.getEditor();
      addCustomHandlers(quillInstance);
    }
  }, []);
  
  // Custom modules configuration
  const modules = {
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'color': [] }, { 'background': [] }],
        // Custom highlight format (will be registered by registerCustomFormats)
        [{ 'highlight': ['yellow', 'pink', 'lightblue'] }],
        ['link', 'image'],
        ['clean']
      ]
    }
  };
  
  // Custom formats including our custom highlight format
  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'color', 'background', 'highlight',
    'link', 'image'
  ];
  
  return (
    <div className="advanced-quill-wrapper">
      <ReactQuill
        ref={quillRef}
        theme="snow" // We'll apply our custom theme via the useEffect
        value={value || ''}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        className="custom-quill-editor advanced-editor"
        {...rest}
      />
      <div className="editor-info">
        <p>This editor uses a custom theme with advanced formatting options.</p>
        <p>Try using the highlight option in the toolbar!</p>
      </div>
    </div>
  );
};

export default AdvancedQuillEditor;
