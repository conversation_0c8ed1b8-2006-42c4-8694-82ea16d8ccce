import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import "../styles/Sidebar.css";
import { useTheme } from "../context/ThemeContext";
import {
  FiFileText,
  FiUsers,
  FiBarChart2,
  FiSettings,
  FiLogOut,
  FiHelpCircle,
  FiCheckCircle,
  FiClock,
  FiPlus,
  FiList,
  FiActivity,
  FiBell,
  FiInbox,
  FiMenu,
  FiChevronLeft,
  FiChevronRight,
  FiMoon,
  FiSun,
  FiHome,
  FiGrid,
  FiMessageSquare,
  FiDatabase,
  FiBookOpen,
  FiMessageCircle,
  FiLayers,
} from "react-icons/fi";
import { RiDashboardLine } from "react-icons/ri";
import { BiUserCircle } from "react-icons/bi";

const Sidebar = ({ user, onLogout, isCollapsed, toggleSidebar }) => {
  const { theme, toggleTheme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [hoveredItem, setHoveredItem] = useState(null);

  // Get current active page from URL path
  const getActivePage = () => {
    const path = location.pathname.split("/")[1] || "dashboard";
    return path;
  };

  // Define menu items based on user role
  const getMenuItems = () => {
    if (!user) return [];

    switch (user.role) {
      case "admin":
        return [
          {
            id: "dashboard",
            label: "Dashboard",
            icon: <RiDashboardLine />,
            path: "/dashboard",
          },
          {
            id: "requests",
            label: "Requests",
            icon: <FiInbox />,
            path: "/requests",
          },
          {
            id: "users",
            label: "User Management",
            icon: <FiUsers />,
            path: "/users",
          },
          // { id: 'tickets', label: 'Ticket Management', icon: <FiList />, path: '/tickets' },
          {
            id: "whitelist",
            label: "Whitelist Platform",
            icon: <FiDatabase />,
            path: "/whitelist",
          },
          {
            id: "events",
            label: "Event Management",
            icon: <FiGrid />,
            path: "/events",
          },
          {
            id: "reports",
            label: "Report",
            icon: <FiBarChart2 />,
            path: "/reports",
          },
          {
            id: "notifications",
            label: "Notifications",
            icon: <FiBell />,
            path: "/notifications",
          },
          {
            id: "askai",
            label: "Ask AI",
            icon: <FiMessageSquare />,
            path: "/askai",
          },
          {
            id: "settings",
            label: "Settings",
            icon: <FiSettings />,
            path: "/settings",
          },
          {
            id: "release-notes",
            label: "Release Notes",
            icon: <FiBookOpen />,
            path: "/release-notes",
          },
          {
            id: "feedback",
            label: "Feedbacks",
            icon: <FiMessageCircle />,
            path: "/feedback",
          },
          {
            id: "audit",
            label: "Audit Logs",
            icon: <FiActivity />,
            path: "/audit",
          },
        ];
      case "approver":
        return [
          {
            id: "dashboard",
            label: "Dashboard",
            icon: <RiDashboardLine />,
            path: "/dashboard",
          },
          {
            id: "requests",
            label: "Requests",
            icon: <FiInbox />,
            path: "/requests",
          },
          {
            id: "pending",
            label: "Pending Tickets",
            icon: <FiClock />,
            path: "/pending",
          },
          {
            id: "approved",
            label: "Approved Tickets",
            icon: <FiCheckCircle />,
            path: "/approved",
          },
          {
            id: "whitelist",
            label: "Whitelist Platform",
            icon: <FiDatabase />,
            path: "/whitelist",
          },
          {
            id: "notifications",
            label: "Notifications",
            icon: <FiBell />,
            path: "/notifications",
          },
          {
            id: "askai",
            label: "Ask AI",
            icon: <FiMessageSquare />,
            path: "/askai",
          },
          {
            id: "release-notes",
            label: "Release Notes",
            icon: <FiBookOpen />,
            path: "/release-notes",
          },
          {
            id: "feedback",
            label: "Feedbacks",
            icon: <FiMessageCircle />,
            path: "/feedback",
          },
        ];
      case "user":
        return [
          {
            id: "dashboard",
            label: "Dashboard",
            icon: <RiDashboardLine />,
            path: "/dashboard",
          },
          {
            id: "requests",
            label: "My Requests",
            icon: <FiInbox />,
            path: "/requests",
          },
          {
            id: "access",
            label: "My Access",
            icon: <FiLayers />,
            path: "/acess-management",
          },
          {
            id: "data",
            label: "My Data",
            icon: <FiLayers />,
            path: "/data-management",
          },
          // { id: 'your-tickets', label: 'Your Tickets', icon: <FiFileText />, path: '/your-tickets' },
          // {
          //   id: "whitelist",
          //   label: "Whitelist Platform",
          //   icon: <FiDatabase />,
          //   path: "/whitelist",
          // },
          {
            id: "notifications",
            label: "Notifications",
            icon: <FiBell />,
            path: "/notifications",
          },
          // {
          //   id: "askai",
          //   label: "Ask AI",
          //   icon: <FiMessageSquare />,
          //   path: "/askai",
          // },
          {
            id: "release-notes",
            label: "Release Notes",
            icon: <FiBookOpen />,
            path: "/release-notes",
          },
          {
            id: "feedback",
            label: "Feedbacks",
            icon: <FiMessageCircle />,
            path: "/feedback",
          },
        ];
      default:
        return [];
    }
  };

  const menuItems = getMenuItems();
  const activePage = getActivePage();

  const handleMenuClick = (path) => {
    navigate(path);
  };

  const handleMouseEnter = (id) => {
    setHoveredItem(id);
  };

  const handleMouseLeave = () => {
    setHoveredItem(null);
  };

  return (
    <div className={`sidebar ${isCollapsed ? "collapsed" : ""}`}>
      <button
        className="collapse-sidebar-fixed-btn"
        onClick={toggleSidebar}
        title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
      >
        {isCollapsed ? <FiChevronRight size={14} /> : <FiChevronLeft size={14} />}
      </button>
      <div className="sidebar-inner">
        <div className="sidebar-header">
        <div
          className="logo-container"
          onClick={() => navigate("/dashboard")}
          title="Go to Dashboard"
        >
          <img src="/Logo FA-11.png" alt="ZDS Logo" className="sidebar-logo" />
          <h2 className="logo">{!isCollapsed ? "ZDS DataQuest" : ""}</h2>
        </div>
        {/* Removed old toggle button */}
      </div>

      <div
        className="user-info"
        onClick={() => navigate("/profile")}
        title="View Profile"
      >
        <div className="user-avatar">
          <BiUserCircle className="user-avatar-icon" />
        </div>
        <div className="user-details">
          <h3>{user ? user.name : "User"}</h3>
          <p>
            {user
              ? user.role === "admin"
                ? "Administrator"
                : user.role === "approver"
                ? "Approver"
                : "User"
              : ""}
          </p>
        </div>
      </div>

      <div className="sidebar-divider"></div>

      <nav className="sidebar-menu">
        <ul>
          {menuItems.map((item) => (
            <li
              key={item.id}
              className={activePage === item.id ? "active" : ""}
              onClick={() => handleMenuClick(item.path)}
              title={isCollapsed ? item.label : ""}
              onMouseEnter={() => handleMouseEnter(item.id)}
              onMouseLeave={handleMouseLeave}
            >
              <div className="menu-item-content">
                <span className="menu-icon">{item.icon}</span>
                <span className="menu-label">{item.label}</span>
              </div>
              {activePage === item.id && (
                <div className="active-indicator"></div>
              )}
            </li>
          ))}
        </ul>
      </nav>

      <div className="sidebar-divider"></div>

      <div className="sidebar-footer">
        <button
          className="theme-toggle-btn"
          onClick={toggleTheme}
          title={
            theme === "light" ? "Switch to dark mode" : "Switch to light mode"
          }
        >
          <span className="footer-icon">
            {theme === "light" ? (
              <FiMoon className="theme-icon" />
            ) : (
              <FiSun className="theme-icon" />
            )}
          </span>
          {!isCollapsed && (
            <span className="footer-text">
              {theme === "light" ? "Dark Mode" : "Light Mode"}
            </span>
          )}
        </button>
        <button className="logout-btn" onClick={onLogout}>
          <span className="footer-icon">
            <FiLogOut className="logout-icon" />
          </span>
          {!isCollapsed && <span className="footer-text">Logout</span>}
        </button>
        {/* Removed expand button from footer */}
      </div>
      </div>
    </div>
  );
};

export default Sidebar;
