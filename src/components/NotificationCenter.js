import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiBell, FiCheckCircle, FiHome, FiDollarSign, FiClock, FiAlertCircle, FiBriefcase, FiTool, FiServer, FiFileText } from 'react-icons/fi';
import { useTheme } from '../context/ThemeContext';
import apiService from '../services/api.service';
import { ENDPOINTS, API_CONFIG } from '../config/api.config';
// import { createDebugApiService } from '../services/debug.service';

// Comment out debug service
// const debugApiService = createDebugApiService(apiService);

const NotificationCenter = ({ user }) => {
  const navigate = useNavigate();
  const { language } = useTheme();
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);
  const [notificationError, setNotificationError] = useState(null);
  const notificationRef = useRef(null);
  const hasInitialFetchedRef = useRef(false);
  const notificationPollingRef = useRef(null);

  // Fetch notifications from API
  const fetchNotifications = async () => {
    if (!user || !user.id) return;

    setIsLoadingNotifications(true);
    setNotificationError(null);

    try {
      // Get the correct endpoint URL without adding prefix
      const endpoint = ENDPOINTS.NOTIFICATION.GET(user.id);
      // console.log('Fetching notifications for user ID:', user.id);
      // console.log('Endpoint:', endpoint);

      // Use regular API service instead of debug service
      const data = await apiService.get(endpoint);

      if (!data) {
        console.warn('Received null or undefined data from API');
        setNotifications([]);
        return;
      }

      // Ensure we have an array of notifications
      setNotifications(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch notifications';
      setNotificationError(errorMessage);
      setNotifications([]);
    } finally {
      setIsLoadingNotifications(false);
    }
  };

  // Fetch notifications on component mount and when user changes
  useEffect(() => {
    if (user && user.id) {
      // Check if we've already fetched notifications for this user
      // This prevents duplicate fetches in StrictMode during development
      if (!hasInitialFetchedRef.current) {
        // console.log('Initial notifications fetch for user:', user.id);
        fetchNotifications();
        hasInitialFetchedRef.current = true;
      }

      // Set up polling for new notifications (every 30 seconds)
      // Clear any existing interval first
      if (notificationPollingRef.current) {
        clearInterval(notificationPollingRef.current);
      }

      notificationPollingRef.current = setInterval(() => {
        // console.log('Polling for new notifications');
        fetchNotifications();
      }, 30000);

      return () => {
        if (notificationPollingRef.current) {
          clearInterval(notificationPollingRef.current);
        }
      };
    }
  }, [user]);

  // Close notification dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [notificationRef]);

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  const markAsRead = async (notificationId, event) => {
    event.stopPropagation(); // Prevent navigating to notifications page when clicking the mark as read button

    try {
      const endpoint = ENDPOINTS.NOTIFICATION.MARK_AS_READ(notificationId);
      // console.log('Marking as read:', `${API_CONFIG.BASE_URL}${endpoint}`);

      // Call the API to mark notification as read
      await apiService.post(endpoint);

      // Update local state after successful API call
      setNotifications(prev => prev.map(notif =>
        notif.id === notificationId ? { ...notif, is_read: true } : notif
      ));

    } catch (error) {
      console.error('Error marking notification as read:', error);
      // Don't show an alert in header to avoid disrupting the user experience
    }
  };

  const markAllAsRead = async () => {
    const unreadNotifications = notifications.filter(n => !n.is_read);

    if (unreadNotifications.length === 0) return;

    try {
      // Create array of promises for each unread notification
      const markReadPromises = unreadNotifications.map(notification => {
        const endpoint = ENDPOINTS.NOTIFICATION.MARK_AS_READ(notification.id);
        return apiService.post(endpoint);
      });

      // Wait for all requests to complete
      await Promise.all(markReadPromises);

      // Update local state after all requests succeed
      setNotifications(prev => prev.map(notif => ({ ...notif, is_read: true })));

    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Helper function to extract request ID from notification content
  const extractRequestIdFromContent = (content) => {
    if (!content) return null;

    // Pattern to match "Request #123" or "Request #REQ-123" etc.
    const requestIdMatch = content.match(/Request\s*#(\w+)/i);
    if (requestIdMatch && requestIdMatch[1]) {
      return requestIdMatch[1];
    }

    return null;
  };

  const handleNotificationClick = async (notification) => {
    // Mark notification as read if not already read
    if (!notification.is_read) {
      try {
        const endpoint = ENDPOINTS.NOTIFICATION.MARK_AS_READ(notification.id);
        await apiService.post(endpoint);

        // Update local state
        setNotifications(prev => prev.map(notif =>
          notif.id === notification.id ? { ...notif, is_read: true } : notif
        ));

      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }

    // Close the dropdown
    setShowNotifications(false);

    // Navigate based on notification type and content
    if (notification.type === 'request') {
      // First try to use related_id if available
      let requestId = notification.related_id;

      // If no related_id, try to extract from content
      if (!requestId) {
        requestId = extractRequestIdFromContent(notification.content);
      }

      // If we found a request ID, navigate to request detail
      if (requestId) {
        navigate(`/requests/detail?id=${requestId}`);
      } else {
        // Fallback to notifications page if no request ID found
        navigate('/notifications');
      }
    } else if (notification.type === 'system' && notification.title && notification.title.toLowerCase().includes('release note')) {
      navigate('/release-notes');
    } else {
      navigate('/notifications');
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'maintenance':
        return <FiTool className="notification-icon" />;
      case 'request':
        return <FiFileText className="notification-icon" />;
      case 'reminder':
        return <FiClock className="notification-icon" />;
      case 'system':
        return <FiServer className="notification-icon" />;
      case 'test':
        return <FiAlertCircle className="notification-icon" />;
      default:
        return <FiBell className="notification-icon" />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'test': return '#f44336'; // red
      case 'maintenance':
      case 'request': return '#4CAF50'; // green
      case 'reminder': return '#FF9800'; // orange
      case 'system': return '#9C27B0'; // purple
      default: return '#4CAF50'; // green
    }
  };

  const unreadCount = notifications.filter(n => !n.is_read).length;

  const formatNotificationTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return language === 'en' ? 'just now' : 'vừa xong';
    if (diffMins < 60) return `${diffMins}${language === 'en' ? 'm' : ' phút'} ago`;
    if (diffHours < 24) return `${diffHours}${language === 'en' ? 'h' : ' giờ'} ago`;
    if (diffDays < 7) return `${diffDays}${language === 'en' ? 'd' : ' ngày'} ago`;

    return date.toLocaleDateString();
  };

  // Group notifications by date
  const groupNotificationsByDate = (notifications) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const thisWeek = new Date(today);
    thisWeek.setDate(thisWeek.getDate() - 7);

    const groups = {
      today: [],
      yesterday: [],
      thisWeek: [],
      older: []
    };

    notifications.forEach(notification => {
      const notifDate = new Date(notification.created_at);
      notifDate.setHours(0, 0, 0, 0);

      if (notifDate.getTime() === today.getTime()) {
        groups.today.push(notification);
      } else if (notifDate.getTime() === yesterday.getTime()) {
        groups.yesterday.push(notification);
      } else if (notifDate >= thisWeek) {
        groups.thisWeek.push(notification);
      } else {
        groups.older.push(notification);
      }
    });

    return groups;
  };

  const renderNotificationGroup = (notifications, groupTitle) => {
    if (notifications.length === 0) return null;

    return (
      <div className="notification-group">
        <div className="notification-group-header">
          <h4>{groupTitle}</h4>
        </div>
        {notifications.map((notification, index) => (
          <div
            key={notification.id}
            className={`notification-item ${notification.is_read ? 'read' : 'unread'} ${index % 2 === 1 ? 'highlight' : ''}`}
            onClick={() => handleNotificationClick(notification)}
          >
            <div className="notification-icon-container">
              {getNotificationIcon(notification.type)}
            </div>
            <div className="notification-content">
              <div className="notification-header">
                <span className="notification-dot" style={{ backgroundColor: getTypeColor(notification.type) }}></span>
                <h4 className="notification-title">{notification.title}</h4>
                <span className="notification-time">
                  {formatNotificationTime(notification.created_at)}
                </span>
              </div>
              <p className="notification-message">{notification.content}</p>
            </div>
            {!notification.is_read && (
              <button
                className="noti-btn-mark-read"
                onClick={(e) => markAsRead(notification.id, e)}
                title={language === 'en' ? 'Mark as read' : 'Đánh dấu đã đọc'}
              >
                <FiCheckCircle />
              </button>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="notification-container" ref={notificationRef}>
      <button className="notification-bell" onClick={toggleNotifications}>
        <FiBell />
        {unreadCount > 0 && <span className="notification-badge">{unreadCount}</span>}
      </button>

      {showNotifications && (
        <div className="notifications-dropdown">
          <div className="notifications-header">
            <h3>{language === 'en' ? 'Notifications' : 'Thông báo'}</h3>
            <button className="mark-all" onClick={markAllAsRead}>
              <FiCheckCircle style={{ marginRight: '5px' }} />
              {language === 'en' ? 'Mark all as read' : 'Đánh dấu tất cả đã đọc'}
            </button>
          </div>

          <div className="notifications-list">
            {isLoadingNotifications && (
              <p className="notification-status">
                {language === 'en' ? 'Loading notifications...' : 'Đang tải thông báo...'}
              </p>
            )}

            {notificationError && (
              <p className="notification-status error">
                {language === 'en' ? 'Error loading notifications' : 'Lỗi khi tải thông báo'}
              </p>
            )}

            {!isLoadingNotifications && !notificationError && notifications.length === 0 && (
              <p className="no-notifications">
                {language === 'en' ? 'No notifications' : 'Không có thông báo'}
              </p>
            )}

            {!isLoadingNotifications && !notificationError && notifications.length > 0 && (
              <>
                {(() => {
                  const groups = groupNotificationsByDate(notifications);
                  return (
                    <>
                      {renderNotificationGroup(groups.today, language === 'en' ? 'Today' : 'Hôm nay')}
                      {renderNotificationGroup(groups.yesterday, language === 'en' ? 'Yesterday' : 'Hôm qua')}
                      {renderNotificationGroup(groups.thisWeek, language === 'en' ? 'This Week' : 'Tuần này')}
                      {renderNotificationGroup(groups.older, language === 'en' ? 'Older' : 'Cũ hơn')}
                    </>
                  );
                })()}

                <div className="notifications-footer">
                  <button
                    className="view-all-button"
                    onClick={() => {
                      setShowNotifications(false);
                      navigate('/notifications');
                    }}
                  >
                    {language === 'en' ? 'View all notifications' : 'Xem tất cả thông báo'}
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;