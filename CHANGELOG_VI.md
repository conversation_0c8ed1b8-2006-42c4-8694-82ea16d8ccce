# Nhật ký thay đổi

Tất cả những thay đổi đáng chú ý đối với ứng dụng Form Management sẽ được ghi lại trong tệp này.

Định dạng dựa trên [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
và dự án này tuân theo [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Chưa phát hành]

### Thêm mới
- Tính năng chia sẻ file nâng cao
- Quản lý quy trình yêu cầu được cải thiện
- Hỗ trợ thêm định dạng file
- Thao tác hàng loạt cho quản lý file

## [0.1.1] - 2025-05-27

### Thêm mới
- **Hệ thống Feedback được nâng cấp hoàn toàn**
  - Trang quản lý feedback mới với giao diện danh sách và chi tiết riêng biệt
  - Giao diện danh sách feedback hiện đại với layout thẻ và theo dõi trạng thái
  - Chi tiết feedback tương tác: Click vào bất kỳ feedback nào để xem thông tin đầy đủ trong modal
  - Hỗ trợ file đính kèm:
    - Xem và tải file đính kèm trực tiếp từ chi tiết feedback
    - Chức năng xem trước hình ảnh cho file visual
    - Hỗ trợ nhiều định dạng file (PDF, DOC, hình ảnh, v.v.)
  - Điều hướng được cải thiện: Trang riêng biệt để xem danh sách và tạo feedback mới
  - Chỉ báo trạng thái: Badge màu sắc cho trạng thái và độ ưu tiên để tổ chức trực quan tốt hơn

- **Yêu cầu Adhoc Data nâng cao**
  - Hệ thống Upload/Download file: Tích hợp quản lý file hoàn chỉnh cho yêu cầu adhoc data
  - Hỗ trợ file đa định dạng: Upload và quản lý các định dạng file khác nhau (CSV, Excel, JSON, PDF, hình ảnh, v.v.)
  - Thao tác file:
    - Upload file an toàn với theo dõi tiến trình
    - Tải file trực tiếp từ chi tiết yêu cầu
    - Xác thực kích thước và loại file
    - Xem trước file cho các định dạng được hỗ trợ
  - Chi tiết yêu cầu được cải thiện: Phần file riêng biệt hiển thị tất cả file dữ liệu đính kèm
  - Trải nghiệm người dùng được cải thiện: Quy trình làm việc được tối ưu hóa cho quản lý file dữ liệu

### Cải thiện
- **Cải thiện UI/UX**
  - Thiết kế giao diện hiện đại: Cập nhật thiết kế trực quan với khoảng cách và typography được cải thiện
  - Layout thẻ được nâng cấp: Hiển thị thông tin được tổ chức tốt hơn trên tất cả các trang
  - Cải thiện thiết kế responsive: Trải nghiệm mobile và tablet được cải thiện
  - Các yếu tố tương tác: Hiệu ứng hover và chuyển tiếp mượt mà được nâng cấp
  - Điều hướng tốt hơn: Cải thiện luồng trang và hành trình người dùng
  - Cải thiện khả năng tiếp cận: Độ tương phản và điều hướng bàn phím tốt hơn

- **Cải thiện kỹ thuật**
  - API quản lý file: Tích hợp backend mạnh mẽ cho các thao tác file
  - Hệ thống modal: Component modal có thể tái sử dụng cho tương tác người dùng tốt hơn
  - Quản lý state: Xử lý state được cải thiện cho tương tác UI phức tạp
  - Xử lý lỗi: Thông báo lỗi và phản hồi người dùng được nâng cấp
  - Tối ưu hóa hiệu suất: Tải trang nhanh hơn và tương tác mượt mà hơn
  - Bảo mật: Upload và download file an toàn với xác thực phù hợp

### Thay đổi
- Tách chức năng feedback thành các trang danh sách và tạo riêng biệt
- Tích hợp hệ thống quản lý file hoàn chỉnh cho yêu cầu adhoc data
- Cập nhật bảng màu và typography để dễ đọc hơn
- Cải thiện thiết kế button và các yếu tố tương tác
- Nâng cấp layout responsive cho tất cả kích thước màn hình

### Ghi chú di chuyển
- Dữ liệu feedback hiện có vẫn hoàn toàn tương thích
- Tất cả yêu cầu adhoc data trước đây vẫn duy trì chức năng
- Không cần hành động nào từ người dùng để nâng cấp

## [0.1.0] - 2023-11-15

### Thêm mới
- Cấu trúc ứng dụng ban đầu với Create React App
- Hệ thống xác thực với tích hợp LDAP
- Kiểm soát truy cập dựa trên vai trò (Admin, Approver, User)
- Bảng điều khiển với các chỉ số quan trọng
- Hệ thống quản lý yêu cầu với nhiều loại yêu cầu:
  - Yêu cầu truy cập nền tảng
  - Yêu cầu truy cập cơ sở dữ liệu
  - Yêu cầu dữ liệu Adhoc
  - Yêu cầu truy cập báo cáo
- Theo dõi yêu cầu với cập nhật trạng thái
- Nền tảng Whitelist với giao diện truy vấn SQL
- Trình tạo truy vấn để tạo truy vấn trực quan
- Lịch sử truy vấn và truy vấn đã lưu
- Chuyển đổi chủ đề Sáng/Tối
- Chuyển đổi ngôn ngữ (Tiếng Anh/Tiếng Việt)
- Hệ thống thông báo
- Điều hướng thanh bên đáp ứng
- Tùy chọn xem dạng lưới và Kanban cho yêu cầu
- Chức năng tìm kiếm
- Quản lý người dùng cho quản trị viên
- Trang cài đặt cho tùy chọn người dùng
- Tính năng Hỏi AI để hỗ trợ

### Thay đổi
- Không áp dụng (Phiên bản ban đầu)

### Sửa lỗi
- Không áp dụng (Phiên bản ban đầu)

### Bảo mật
- Xác thực dựa trên JWT với thời gian hết hạn token
- Hạn chế truy cập dựa trên vai trò
- Giao tiếp API an toàn
