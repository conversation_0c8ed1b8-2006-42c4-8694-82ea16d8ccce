# API Documentation

## Overview

The Form Management application interacts with a backend API to manage authentication, requests, users, and other data. This document outlines the available endpoints and their usage.

Base URL: `http://localhost:3014` (development) or configured via `REACT_APP_API_URL` environment variable.

## Authentication

### Login

```
POST /auth/ldaplogin
```

Authenticates a user via LDAP.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "token": "string",
  "user": {
    "id": "string",
    "username": "string",
    "name": "string",
    "role": "string",
    "company_id": "string"
  },
  "expiresAt": "string (ISO date)"
}
```

### Logout

```
POST /auth/logout
```

Invalidates the current user's token.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### Get Profile

```
GET /auth/profile
```

Retrieves the current user's profile information.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "id": "string",
  "username": "string",
  "name": "string",
  "role": "string",
  "company_id": "string",
  "email": "string"
}
```

## User Management

### Get All Users

```
GET /users/get_all?platform={platform}
```

Retrieves all users for a specific platform.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `platform`: The platform to get users for

**Response:**
```json
[
  {
    "id": "string",
    "username": "string",
    "name": "string",
    "role": "string",
    "email": "string",
    "created_at": "string (ISO date)",
    "updated_at": "string (ISO date)"
  }
]
```

### Get User Platforms

```
GET /users/get_all_platform?username={username}
```

Retrieves all platforms a user has access to.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `username`: The username to get platforms for

**Response:**
```json
[
  {
    "platform_id": "string",
    "platform_name": "string",
    "access_level": "string"
  }
]
```

### Get User

```
GET /users/get
```

Retrieves a specific user's details.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "id": "string",
  "username": "string",
  "name": "string",
  "role": "string",
  "email": "string",
  "created_at": "string (ISO date)",
  "updated_at": "string (ISO date)"
}
```

### Register User

```
POST /users/register
```

Creates a new user.

**Headers:**
```
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "username": "string",
  "name": "string",
  "email": "string",
  "role": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User created successfully",
  "user": {
    "id": "string",
    "username": "string",
    "name": "string",
    "role": "string",
    "email": "string"
  }
}
```

### Update User

```
PUT /users/update
```

Updates an existing user.

**Headers:**
```
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "id": "string",
  "name": "string",
  "email": "string",
  "role": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User updated successfully",
  "user": {
    "id": "string",
    "username": "string",
    "name": "string",
    "role": "string",
    "email": "string"
  }
}
```

## Request Management

### Create Request

```
POST /requests/create
```

Creates a new request.

**Headers:**
```
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "title": "string",
  "description": "string",
  "request_type": "string",
  "platform_name": "string (optional)",
  "db_name": "string (optional)",
  "due_date": "string (ISO date, optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Request created successfully",
  "request": {
    "id": "string",
    "title": "string",
    "description": "string",
    "request_type": "string",
    "status": "pending",
    "created_at": "string (ISO date)",
    "updated_at": "string (ISO date)"
  }
}
```

### Get Request

```
GET /requests/get?request_id={requestId}
```

Retrieves a specific request.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `request_id`: The ID of the request to retrieve

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "description": "string",
  "request_type": "string",
  "platform_name": "string (if applicable)",
  "db_name": "string (if applicable)",
  "status": "string",
  "created_at": "string (ISO date)",
  "updated_at": "string (ISO date)",
  "created_by": {
    "id": "string",
    "name": "string"
  },
  "comments": [
    {
      "id": "string",
      "text": "string",
      "created_at": "string (ISO date)",
      "user": {
        "id": "string",
        "name": "string"
      }
    }
  ]
}
```

### Get Requests by User

```
GET /requests/getbyuser?user_id={userId}
```

Retrieves all requests for a specific user.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `user_id`: The ID of the user to get requests for

**Response:**
```json
[
  {
    "id": "string",
    "title": "string",
    "description": "string",
    "request_type": "string",
    "status": "string",
    "created_at": "string (ISO date)",
    "updated_at": "string (ISO date)"
  }
]
```

### Update Request

```
PUT /requests/update?request_id={requestId}
```

Updates an existing request.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `request_id`: The ID of the request to update

**Request Body:**
```json
{
  "title": "string (optional)",
  "description": "string (optional)",
  "status": "string (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Request updated successfully",
  "request": {
    "id": "string",
    "title": "string",
    "description": "string",
    "request_type": "string",
    "status": "string",
    "created_at": "string (ISO date)",
    "updated_at": "string (ISO date)"
  }
}
```

### Delete Request

```
DELETE /requests/delete?request_id={requestId}
```

Deletes a request.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `request_id`: The ID of the request to delete

**Response:**
```json
{
  "success": true,
  "message": "Request deleted successfully"
}
```

## Notifications

### Get Notifications

```
GET /notifications/get?user_id={userId}
```

Retrieves notifications for a specific user.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `user_id`: The ID of the user to get notifications for

**Response:**
```json
[
  {
    "id": "string",
    "message": "string",
    "type": "string",
    "read": boolean,
    "created_at": "string (ISO date)",
    "related_id": "string (optional)"
  }
]
```

### Create Notification

```
POST /notifications/create
```

Creates a new notification.

**Headers:**
```
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "user_id": "string",
  "message": "string",
  "type": "string",
  "related_id": "string (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Notification created successfully",
  "notification": {
    "id": "string",
    "message": "string",
    "type": "string",
    "read": false,
    "created_at": "string (ISO date)"
  }
}
```

### Mark Notification as Read

```
PUT /notifications/mark-as-read?notification_id={notificationId}
```

Marks a notification as read.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `notification_id`: The ID of the notification to mark as read

**Response:**
```json
{
  "success": true,
  "message": "Notification marked as read"
}
```

## Error Responses

All API endpoints may return the following error responses:

### 400 Bad Request

```json
{
  "error": "Bad Request",
  "message": "Description of the error"
}
```

### 401 Unauthorized

```json
{
  "error": "Unauthorized",
  "message": "Invalid or expired token"
}
```

### 403 Forbidden

```json
{
  "error": "Forbidden",
  "message": "You do not have permission to access this resource"
}
```

### 404 Not Found

```json
{
  "error": "Not Found",
  "message": "The requested resource was not found"
}
```

### 500 Internal Server Error

```json
{
  "error": "Internal Server Error",
  "message": "An unexpected error occurred"
}
```
