# Changelog

All notable changes to the Form Management application will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Advanced file sharing capabilities
- Enhanced request workflow management
- Additional file format support
- Bulk operations for file management

## [0.1.1] - 2025-05-27

### Added
- **Enhanced Feedback System**
  - New Feedback Management Page with dedicated list and detail views
  - Modern card-based layout displaying all submitted feedback with status tracking
  - Interactive Feedback Details: Click on any feedback to view comprehensive details in a modal
  - File Attachment Support:
    - View and download attached files directly from feedback details
    - Image preview functionality for visual attachments
    - Support for multiple file formats (PDF, DOC, images, etc.)
  - Improved Navigation: Separate pages for viewing feedback list and creating new feedback
  - Status Indicators: Color-coded status and priority badges for better visual organization

- **Advanced Adhoc Data Requests**
  - File Upload/Download System: Complete file management integration for adhoc data requests
  - Multi-format File Support: Upload and manage various file formats (CSV, Excel, JSON, PDF, images, etc.)
  - File Operations:
    - Secure file upload with progress tracking
    - Direct file download from request details
    - File size and type validation
    - File preview for supported formats
  - Enhanced Request Details: Dedicated file section showing all attached data files
  - Improved User Experience: Streamlined workflow for data file management

### Improved
- **UI/UX Improvements**
  - Modern Interface Design: Updated visual design with improved spacing and typography
  - Enhanced Card Layouts: Better organized information display across all pages
  - Responsive Design Enhancements: Improved mobile and tablet experience
  - Interactive Elements: Enhanced hover effects and smooth transitions
  - Better Navigation: Improved page flow and user journey
  - Accessibility Improvements: Better contrast and keyboard navigation support

- **Technical Enhancements**
  - File Management API: Robust backend integration for file operations
  - Modal System: Reusable modal components for better user interaction
  - State Management: Improved state handling for complex UI interactions
  - Error Handling: Enhanced error messages and user feedback
  - Performance Optimization: Faster page loads and smoother interactions
  - Security: Secure file upload and download with proper authentication

### Changed
- Split feedback functionality into separate list and creation pages
- Integrated complete file management system for adhoc data requests
- Updated color schemes and typography for better readability
- Improved button designs and interactive elements
- Enhanced responsive layouts for all screen sizes

### Migration Notes
- Existing feedback data remains fully compatible
- All previous adhoc data requests maintain their functionality
- No user action required for upgrade

## [0.1.0] - 2023-11-15

### Added
- Initial application structure with Create React App
- Authentication system with LDAP integration
- Role-based access control (Admin, Approver, User)
- Dashboard with key metrics
- Request management system with multiple request types:
  - Platform Access Requests
  - Database Access Requests
  - Adhoc Data Requests
  - Report Access Requests
- Request tracking with status updates
- Whitelist Platform with SQL query interface
- Query Builder for visual query creation
- Query history and saved queries
- Dark/Light theme toggle
- Language switching (English/Vietnamese)
- Notification system
- Responsive sidebar navigation
- Grid and Kanban view options for requests
- Search functionality
- User management for administrators
- Settings page for user preferences
- Ask AI feature for assistance

### Changed
- N/A (Initial release)

### Fixed
- N/A (Initial release)

### Security
- JWT-based authentication with token expiration
- Role-based access restrictions
- Secure API communication
