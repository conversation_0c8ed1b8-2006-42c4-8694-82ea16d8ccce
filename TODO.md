# SETTING: 
- cau hinh connection den cac app/server can quan ly - mysql/postgresql
- dark/light mode

# USER:
- api: show all permission cua user
  - admin: xem duoc all
  - approver: xem duoc app ma minh approve
  - user: chi xem duoc cua user do


# REQUEST 
- request cho phone number tu file upload uid - tiennpn

- UI: nếu response ENDPOINTS.REQUEST.GET có request_type = 'access-report' thì dùng "executors": [
            "tiennpn",
            "hoangnx3"
        ] cho executor
- UI request detail: description cua request_type = 'access-report' voi nhieu executors
- them thong tin cho description cua request_type = 'access-report' khi tao moi
  - url to workbook of views: https://atlas.vng.com.vn/#/site/ZLPDataServices/workbooks/{7051}/views -- use workbook_id 
  - url to view: https://atlas.vng.com.vn/#/site/ZLPDataServices/views/{CheckWorkload/CheckWorkload} -- use path
  - url to workbook: https://atlas.vng.com.vn/#/site/ZLPDataServices/workbooks/{5942}/views -- use id