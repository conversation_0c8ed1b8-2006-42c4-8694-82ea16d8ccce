# Tài liệu chức năng - Form Management

## T<PERSON>h năng chính

### <PERSON><PERSON><PERSON> thực & Quản lý người dùng
- **Đăng nhập LDAP**: <PERSON><PERSON><PERSON> thực an toàn thông qua tích hợp LDAP
- **Kiểm soát truy cập dựa trên vai trò**: Gia<PERSON> diện và quyền khác nhau cho:
  - Admin: T<PERSON>y cập toàn bộ hệ thống
  - Approver: <PERSON><PERSON><PERSON> năng phê duyệt yêu cầu
  - User: <PERSON><PERSON><PERSON> và theo dõi yêu cầu cơ bản
- **<PERSON><PERSON> sơ người dùng**: Xem và quản lý thông tin người dùng
- **Quản lý phiên**: Tự động hết hạn và làm mới token

### Quản lý yêu cầu
- **T<PERSON>o yêu cầu**: <PERSON><PERSON><PERSON><PERSON> loại yêu cầu:
  - <PERSON><PERSON><PERSON> cầu truy cập nền tảng
  - <PERSON>êu cầu truy cập cơ sở dữ liệu
  - <PERSON>êu cầu dữ liệu Adhoc
  - Yêu cầu truy cập báo cáo
- **Theo dõi yêu cầu**: Giám sát trạng thái yêu cầu trong quy trình làm việc
- **Phê duyệt yêu cầu**: Quy trình phê duyệt với cập nhật trạng thái
- **Chi tiết yêu cầu**: Xem toàn diện thông tin yêu cầu
- **Bình luận**: Thêm bình luận vào yêu cầu để trao đổi

### Nền tảng Whitelist
- **Giao diện truy vấn SQL**: Thực thi truy vấn PostgreSQL đối với dữ liệu whitelist
- **Trình tạo truy vấn**: Giao diện trực quan để xây dựng truy vấn phức tạp
- **Lịch sử truy vấn**: Theo dõi và tái sử dụng các truy vấn trước đó
- **Truy vấn đã lưu**: Lưu và quản lý các truy vấn thường xuyên sử dụng
- **Tạo truy vấn hỗ trợ AI**: Tạo SQL từ lệnh nhắc ngôn ngữ tự nhiên

### Bảng điều khiển & Báo cáo
- **Tổng quan bảng điều khiển**: Các chỉ số quan trọng và trạng thái hệ thống
- **Phân tích yêu cầu**: Biểu diễn trực quan dữ liệu yêu cầu
- **Phân phối trạng thái**: Biểu đồ hiển thị phân tích trạng thái yêu cầu
- **Chỉ số hiệu suất**: Theo dõi hiệu suất hệ thống

### Hệ thống thông báo
- **Thông báo thời gian thực**: Cập nhật về thay đổi trạng thái yêu cầu
- **Trung tâm thông báo**: Vị trí tập trung cho tất cả thông báo
- **Trạng thái đã đọc/chưa đọc**: Theo dõi thông báo nào đã được xem

### Cài đặt & Cấu hình
- **Cài đặt chủ đề**: Chuyển đổi giữa chế độ sáng và tối
- **Cài đặt ngôn ngữ**: Chuyển đổi giữa tiếng Anh và tiếng Việt
- **Tùy chọn người dùng**: Cài đặt cá nhân hóa được lưu trong localStorage
- **Tùy chọn hiển thị**: Cấu hình chế độ xem, số mục trên mỗi trang, v.v.

## Tính năng giao diện người dùng

### Điều hướng
- **Điều hướng thanh bên**: Truy cập nhanh đến tất cả khu vực hệ thống
- **Thanh bên có thể thu gọn**: Tối đa hóa không gian màn hình khi cần thiết
- **Chức năng tìm kiếm**: Tìm tính năng và nội dung nhanh chóng
- **Đường dẫn**: Theo dõi đường dẫn điều hướng

### Trực quan hóa dữ liệu
- **Chế độ xem lưới**: Trình bày dữ liệu dạng bảng truyền thống
- **Chế độ xem Kanban**: Biểu diễn trực quan quy trình làm việc
- **Biểu đồ & Đồ thị**: Biểu diễn trực quan các chỉ số và dữ liệu
- **Chỉ báo trạng thái**: Biểu diễn trạng thái bằng mã màu

### Quản lý dữ liệu
- **Lọc**: Lọc dữ liệu theo nhiều tiêu chí
- **Sắp xếp**: Sắp xếp dữ liệu theo các trường khác nhau
- **Phân trang**: Điều hướng qua các tập dữ liệu lớn
- **Tìm kiếm**: Tìm các mục cụ thể nhanh chóng

### Thiết kế đáp ứng
- **Bố cục thích ứng**: Tối ưu hóa cho các kích thước màn hình khác nhau
- **Thành phần thân thiện với thiết bị di động**: Các phần tử giao diện thân thiện với cảm ứng
- **Trải nghiệm nhất quán**: Thiết kế đồng nhất trên các thiết bị

## Quy trình làm việc

### Vòng đời yêu cầu
1. **Tạo**: Người dùng gửi yêu cầu mới
2. **Đang chờ xử lý**: Yêu cầu đang chờ phê duyệt
3. **Phê duyệt/Từ chối**: Yêu cầu được đánh giá bởi người phê duyệt
4. **Đang xử lý**: Yêu cầu đã được phê duyệt đang được xử lý
5. **Hoàn thành**: Yêu cầu đã được hoàn thành
6. **Từ chối**: Yêu cầu bị từ chối kèm lý do

### Quy trình phê duyệt
1. Người phê duyệt nhận thông báo về yêu cầu đang chờ xử lý
2. Người phê duyệt xem xét chi tiết yêu cầu
3. Người phê duyệt có thể yêu cầu thông tin bổ sung thông qua bình luận
4. Người phê duyệt đưa ra quyết định (phê duyệt/từ chối)
5. Hệ thống cập nhật trạng thái yêu cầu và thông báo cho người yêu cầu

### Quy trình thông báo
1. Sự kiện hệ thống kích hoạt thông báo
2. Thông báo xuất hiện trong Trung tâm thông báo
3. Người dùng có thể đánh dấu thông báo là đã đọc
4. Nhấp vào thông báo sẽ điều hướng đến nội dung liên quan

## Quốc tế hóa

Ứng dụng hỗ trợ cả tiếng Anh và tiếng Việt, với bản dịch cho:
- Các phần tử điều hướng
- Nhãn biểu mẫu và placeholder
- Văn bản nút
- Chỉ báo trạng thái
- Thông báo lỗi
- Hộp thoại xác nhận
