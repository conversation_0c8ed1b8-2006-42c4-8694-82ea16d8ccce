# Hệ thống Object Storage: G<PERSON><PERSON>i pháp lưu trữ cho tương lai

## 1. Object Storage là gì?

Object Storage (lưu trữ đối tượng) là một kiến trúc lưu trữ dữ liệu được thiết kế đặc biệt để xử lý dữ liệu phi cấu trúc với quy mô lớn. Khác với hệ thống file truyền thống (mà chúng ta đang sử dụng khi lưu trữ file trực tiếp trên server), Object Storage tổ chức dữ liệu thành các "đối tượng" riêng biệt, mỗi đối tượng bao gồm:

- **Data**: Nội dung thực tế của file
- **Metadata**: Thông tin mô tả về đối tượng (k<PERSON><PERSON> thư<PERSON>, loạ<PERSON>, ng<PERSON><PERSON> tạ<PERSON>, v.v.)
- **Unique identifier**: <PERSON><PERSON><PERSON> danh duy nhất để truy cập đối tượng

## 2. Sự khác biệt giữa Object Storage và File Storage

| Đặc điểm | File Storage (Hiện tại) | Object Storage |
|----------|-------------------------|----------------|
| **Cấu trúc** | Phân cấp (thư mục/file) | Phẳng (không có thư mục) |
| **Định danh** | Đường dẫn file | Unique ID (thường là URL) |
| **Metadata** | Giới hạn (tên, ngày, quyền) | Phong phú và tùy chỉnh |
| **Khả năng mở rộng** | Giới hạn bởi hệ thống file | Gần như vô hạn |
| **Hiệu suất** | Tốt cho file nhỏ, truy cập thường xuyên | Tối ưu cho lưu trữ lớn, ít thay đổi |
| **API** | POSIX (open, read, write) | RESTful HTTP (GET, PUT, DELETE) |
| **Phân tán** | Khó phân tán | Được thiết kế để phân tán |

## 3. Các giải pháp Object Storage phổ biến

### Giải pháp self-hosted

1. **MinIO**
   - Triển khai nhẹ, tương thích S3
   - Có thể chạy trên một server đơn hoặc cluster
   - Mã nguồn mở, dễ tích hợp
   - Hỗ trợ erasure coding và bit rot protection

2. **Ceph**
   - Hệ thống lưu trữ phân tán mạnh mẽ
   - Hỗ trợ object, block, và file storage
   - Khả năng mở rộng cao, tự phục hồi
   - Phức tạp hơn để triển khai và quản lý

### Dịch vụ cloud

1. **Amazon S3**
   - Dịch vụ object storage phổ biến nhất
   - Độ tin cậy và khả năng mở rộng cực cao
   - Nhiều tính năng: versioning, lifecycle policies, encryption

2. **Google Cloud Storage**
   - Tích hợp tốt với các dịch vụ Google Cloud
   - Nhiều lớp lưu trữ (Standard, Nearline, Coldline, Archive)

3. **Azure Blob Storage**
   - Tích hợp với hệ sinh thái Microsoft
   - Hỗ trợ nhiều loại blob (block, append, page)

## 4. Lợi ích của việc chuyển sang Object Storage

### Khả năng mở rộng vượt trội

- **Không giới hạn về kích thước**: Có thể mở rộng đến petabyte dữ liệu
- **Không giới hạn số lượng file**: Không bị giới hạn bởi inodes như hệ thống file
- **Mở rộng ngang**: Thêm node mới để tăng dung lượng và throughput

### Độ tin cậy cao

- **Dự phòng tự động**: Lưu nhiều bản sao của mỗi đối tượng
- **Erasure coding**: Kỹ thuật dự phòng hiệu quả hơn replication
- **Tự phục hồi**: Tự động phát hiện và sửa chữa dữ liệu bị hỏng

### Tối ưu chi phí

- **Tiết kiệm chi phí phần cứng**: Sử dụng phần cứng thông thường
- **Pay-as-you-go**: Chỉ trả tiền cho dung lượng sử dụng (với cloud services)
- **Tiered storage**: Tự động di chuyển dữ liệu ít truy cập sang lớp lưu trữ rẻ hơn

### Tính năng nâng cao

- **Versioning**: Lưu trữ nhiều phiên bản của cùng một đối tượng
- **Lifecycle policies**: Tự động quản lý vòng đời của đối tượng
- **Object locking**: Ngăn chặn xóa hoặc ghi đè
- **Cross-region replication**: Sao chép dữ liệu giữa các vùng địa lý

## 5. Cách MinIO hoạt động

MinIO là giải pháp object storage phù hợp nhất để chuyển đổi từ hệ thống lưu trữ file trực tiếp trên server, vì nó đơn giản để triển khai và quản lý.

### Kiến trúc MinIO

```
┌─────────────────────────────────────────────────────────┐
│                      Application                         │
└───────────────────────────┬─────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                    MinIO Client SDK                      │
└───────────────────────────┬─────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                    MinIO Server                          │
│                                                         │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐    │
│  │   Bucket 1  │   │   Bucket 2  │   │   Bucket 3  │    │
│  └─────────────┘   └─────────────┘   └─────────────┘    │
│                                                         │
│  ┌─────────────────────────────────────────────────┐    │
│  │                  Erasure Coding                  │    │
│  └─────────────────────────────────────────────────┘    │
│                                                         │
│  ┌─────────────────────────────────────────────────┐    │
│  │                  Storage Layer                   │    │
│  └─────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────┘
```

### Khái niệm chính trong MinIO

1. **Bucket**: Container cho các objects, tương tự như thư mục cấp cao nhất
2. **Object**: Đơn vị lưu trữ cơ bản, tương đương với file
3. **Erasure Coding**: Kỹ thuật dự phòng dữ liệu, cho phép khôi phục dữ liệu ngay cả khi mất một số phần
4. **Distributed Mode**: Chế độ phân tán trên nhiều server

### Triển khai MinIO

#### Standalone Mode (Đơn giản nhất)

```bash
# Cài đặt MinIO
wget https://dl.min.io/server/minio/release/linux-amd64/minio
chmod +x minio

# Chạy MinIO server
./minio server /data
```

#### Distributed Mode (Khả năng mở rộng cao)

```bash
# Chạy MinIO trên 4 node với 16 drives
minio server http://host{1...4}/export{1...4}
```

## 6. Tích hợp MinIO với ứng dụng Form Management

### Thay đổi cần thiết trong backend

1. **Cài đặt MinIO client**

```python
# FastAPI backend
from minio import Minio
from minio.error import S3Error

# Khởi tạo MinIO client
minio_client = Minio(
    "minio-server:9000",
    access_key="minioadmin",
    secret_key="minioadmin",
    secure=False  # Set True nếu sử dụng HTTPS
)

# Đảm bảo bucket tồn tại
if not minio_client.bucket_exists("uploads"):
    minio_client.make_bucket("uploads")
```

2. **Sửa đổi API upload file**

```python
@router.post("/files/upload", response_model=FileResponse)
async def upload_file(
    file: UploadFile = File(...),
    request_id: Optional[int] = Form(None),
    current_user: User = Depends(get_current_user)
):
    # Tạo tên file duy nhất
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    unique_filename = f"{timestamp}_{current_user.id}_{file.filename}_{uuid.uuid4()}"
    
    # Tạo object path trong MinIO
    object_name = f"requests/{request_id}/{unique_filename}" if request_id else f"general/{unique_filename}"
    
    # Đọc file vào memory (cho file nhỏ)
    file_data = await file.read()
    file_size = len(file_data)
    
    # Upload lên MinIO
    minio_client.put_object(
        "uploads",
        object_name,
        io.BytesIO(file_data),
        file_size,
        file.content_type
    )
    
    # Lưu metadata vào database
    file_metadata = {
        "filename": unique_filename,
        "original_filename": file.filename,
        "file_path": f"uploads/{object_name}",  # MinIO path
        "file_size": file_size,
        "mime_type": file.content_type,
        "uploaded_by": current_user.id,
        # Các trường khác...
    }
    
    # Thêm vào database
    # ...
    
    return {"id": file_id, "filename": file.filename, "size": file_size}
```

3. **Sửa đổi API download file**

```python
@router.get("/files/{file_id}/download")
async def download_file(
    file_id: int,
    current_user: User = Depends(get_current_user)
):
    # Lấy thông tin file từ database
    file_info = await db.files.find_one({"_id": ObjectId(file_id)})
    if not file_info:
        raise HTTPException(status_code=404, detail="File not found")
    
    # Kiểm tra quyền truy cập
    # ...
    
    # Parse MinIO path
    parts = file_info["file_path"].split('/', 1)
    bucket_name = parts[0]
    object_name = parts[1]
    
    # Tạo presigned URL để download
    url = minio_client.presigned_get_object(
        bucket_name,
        object_name,
        expires=timedelta(minutes=30)
    )
    
    # Redirect đến presigned URL
    return RedirectResponse(url)
```

### Abstraction Layer để dễ dàng chuyển đổi

Để dễ dàng chuyển đổi giữa file system và object storage, bạn có thể tạo một abstraction layer:

```python
# storage.py
from abc import ABC, abstractmethod
import os
import io
from minio import Minio

class StorageBackend(ABC):
    @abstractmethod
    async def save_file(self, file_data, file_path, content_type=None):
        pass
    
    @abstractmethod
    async def get_file(self, file_path):
        pass
    
    @abstractmethod
    async def delete_file(self, file_path):
        pass

class FileSystemStorage(StorageBackend):
    def __init__(self, base_path="/app_storage"):
        self.base_path = base_path
    
    async def save_file(self, file_data, file_path, content_type=None):
        full_path = os.path.join(self.base_path, file_path)
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        with open(full_path, "wb") as f:
            f.write(file_data)
        
        return full_path
    
    async def get_file(self, file_path):
        full_path = os.path.join(self.base_path, file_path)
        if not os.path.exists(full_path):
            return None
        
        with open(full_path, "rb") as f:
            return f.read()
    
    async def delete_file(self, file_path):
        full_path = os.path.join(self.base_path, file_path)
        if os.path.exists(full_path):
            os.remove(full_path)
            return True
        return False

class MinioStorage(StorageBackend):
    def __init__(self, endpoint, access_key, secret_key, secure=False):
        self.client = Minio(
            endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=secure
        )
    
    async def save_file(self, file_data, file_path, content_type=None):
        # Parse bucket and object name
        parts = file_path.split('/', 1)
        bucket_name = parts[0]
        object_name = parts[1] if len(parts) > 1 else file_path
        
        # Ensure bucket exists
        if not self.client.bucket_exists(bucket_name):
            self.client.make_bucket(bucket_name)
        
        # Upload file
        self.client.put_object(
            bucket_name,
            object_name,
            io.BytesIO(file_data),
            len(file_data),
            content_type
        )
        
        return file_path
    
    async def get_file(self, file_path):
        # Parse bucket and object name
        parts = file_path.split('/', 1)
        bucket_name = parts[0]
        object_name = parts[1] if len(parts) > 1 else file_path
        
        try:
            response = self.client.get_object(bucket_name, object_name)
            data = response.read()
            response.close()
            response.release_conn()
            return data
        except Exception:
            return None
    
    async def delete_file(self, file_path):
        # Parse bucket and object name
        parts = file_path.split('/', 1)
        bucket_name = parts[0]
        object_name = parts[1] if len(parts) > 1 else file_path
        
        try:
            self.client.remove_object(bucket_name, object_name)
            return True
        except Exception:
            return False

# Factory function to get the appropriate storage backend
def get_storage_backend():
    storage_type = os.environ.get("STORAGE_TYPE", "filesystem")
    
    if storage_type == "minio":
        return MinioStorage(
            endpoint=os.environ.get("MINIO_ENDPOINT", "minio:9000"),
            access_key=os.environ.get("MINIO_ACCESS_KEY", "minioadmin"),
            secret_key=os.environ.get("MINIO_SECRET_KEY", "minioadmin"),
            secure=os.environ.get("MINIO_SECURE", "false").lower() == "true"
        )
    else:
        return FileSystemStorage(
            base_path=os.environ.get("STORAGE_BASE_PATH", "/app_storage")
        )
```

## 7. Chiến lược di chuyển từ File System sang Object Storage

### Phương pháp di chuyển dần dần

1. **Giai đoạn 1: Triển khai abstraction layer**
   - Tạo abstraction layer như trên
   - Cập nhật code để sử dụng abstraction layer thay vì truy cập file system trực tiếp
   - Tiếp tục sử dụng file system storage backend

2. **Giai đoạn 2: Triển khai MinIO**
   - Cài đặt và cấu hình MinIO server
   - Kiểm thử MinIO storage backend với dữ liệu mới

3. **Giai đoạn 3: Di chuyển dữ liệu hiện có**
   - Viết script di chuyển để copy file từ file system sang MinIO
   - Cập nhật database để trỏ đến vị trí mới trong MinIO
   - Thực hiện di chuyển theo batch, ưu tiên dữ liệu mới nhất

4. **Giai đoạn 4: Chuyển đổi hoàn toàn**
   - Chuyển đổi storage backend sang MinIO
   - Giữ file system data như backup trong một thời gian
   - Xóa dữ liệu file system sau khi đã xác nhận mọi thứ hoạt động tốt

### Script di chuyển dữ liệu mẫu

```python
import os
import asyncio
from minio import Minio
import psycopg2

# Kết nối database
conn = psycopg2.connect("dbname=formmanagement user=postgres password=password")
cur = conn.cursor()

# Khởi tạo MinIO client
minio_client = Minio(
    "minio-server:9000",
    access_key="minioadmin",
    secret_key="minioadmin",
    secure=False
)

# Đảm bảo bucket tồn tại
if not minio_client.bucket_exists("uploads"):
    minio_client.make_bucket("uploads")

async def migrate_files(batch_size=100, start_id=0):
    # Lấy batch files từ database
    cur.execute(
        "SELECT id, file_path FROM files WHERE id > %s ORDER BY id LIMIT %s",
        (start_id, batch_size)
    )
    files = cur.fetchall()
    
    if not files:
        print("Migration complete!")
        return
    
    for file_id, file_path in files:
        try:
            # Đường dẫn đầy đủ trong file system
            full_path = os.path.join("/app_storage", file_path)
            
            if not os.path.exists(full_path):
                print(f"File not found: {full_path}")
                continue
            
            # Tạo object name cho MinIO
            # Ví dụ: chuyển "/app_storage/uploads/2023/11/file.txt" thành "uploads/2023/11/file.txt"
            object_name = file_path
            
            # Xác định bucket (lấy phần đầu tiên của path)
            parts = object_name.split('/', 1)
            bucket_name = parts[0] or "uploads"
            object_name = parts[1] if len(parts) > 1 else object_name
            
            # Upload file lên MinIO
            with open(full_path, "rb") as file_data:
                file_stat = os.stat(full_path)
                minio_client.put_object(
                    bucket_name,
                    object_name,
                    file_data,
                    file_stat.st_size
                )
            
            # Cập nhật đường dẫn trong database
            new_path = f"{bucket_name}/{object_name}"
            cur.execute(
                "UPDATE files SET file_path = %s, storage_type = 'minio' WHERE id = %s",
                (new_path, file_id)
            )
            conn.commit()
            
            print(f"Migrated file ID {file_id}: {file_path} -> {new_path}")
            
            # Lấy ID cuối cùng để tiếp tục batch tiếp theo
            last_id = file_id
        
        except Exception as e:
            print(f"Error migrating file ID {file_id}: {e}")
            conn.rollback()
    
    # Gọi đệ quy để xử lý batch tiếp theo
    await migrate_files(batch_size, last_id)

# Chạy migration
asyncio.run(migrate_files())
```

## 8. Kết luận

Object Storage như MinIO cung cấp một giải pháp lưu trữ mạnh mẽ, có khả năng mở rộng cao cho ứng dụng Form Management. Mặc dù hiện tại việc lưu trữ file trực tiếp trên server là đủ, việc thiết kế hệ thống với khả năng chuyển đổi sang Object Storage trong tương lai sẽ giúp ứng dụng dễ dàng mở rộng khi lượng dữ liệu tăng lên.

Bằng cách sử dụng abstraction layer, bạn có thể bắt đầu với file system storage đơn giản và chuyển sang MinIO khi cần thiết, mà không phải thay đổi nhiều code ứng dụng. Điều này cung cấp con đường mở rộng rõ ràng khi nhu cầu lưu trữ của bạn phát triển.
