# Testing Documentation

## Overview

This document outlines the testing strategy, methodologies, and practices for the Form Management application. It serves as a guide for developers to ensure consistent and comprehensive testing across the application.

## Testing Levels

### Unit Testing

Unit tests verify that individual components and functions work as expected in isolation.

#### What to Test
- React components (rendering, props, state changes)
- Utility functions
- Hooks
- Context providers
- Service functions

#### Tools
- Jest: Testing framework
- React Testing Library: Component testing
- @testing-library/user-event: Simulating user interactions
- @testing-library/dom: DOM testing utilities

#### Example Component Test

```jsx
import { render, screen, fireEvent } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import Button from './Button';

describe('Button Component', () => {
  test('renders with correct text', () => {
    render(<Button label="Click Me" />);
    expect(screen.getByText('Click Me')).toBeInTheDocument();
  });

  test('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<Button label="Click Me" onClick={handleClick} />);
    
    act(() => {
      fireEvent.click(screen.getByText('Click Me'));
    });
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies disabled styles when disabled', () => {
    render(<Button label="Click Me" disabled />);
    expect(screen.getByText('Click Me')).toHaveClass('disabled');
  });
});
```

#### Example Utility Function Test

```jsx
import { formatDate, calculateTotal } from './utils';

describe('Utility Functions', () => {
  describe('formatDate', () => {
    test('formats date correctly', () => {
      const date = new Date('2023-01-15T12:00:00Z');
      expect(formatDate(date)).toBe('15/01/2023');
    });

    test('returns empty string for invalid date', () => {
      expect(formatDate(null)).toBe('');
      expect(formatDate(undefined)).toBe('');
    });
  });

  describe('calculateTotal', () => {
    test('calculates sum correctly', () => {
      const items = [
        { price: 10, quantity: 2 },
        { price: 15, quantity: 1 }
      ];
      expect(calculateTotal(items)).toBe(35);
    });

    test('returns 0 for empty array', () => {
      expect(calculateTotal([])).toBe(0);
    });
  });
});
```

### Integration Testing

Integration tests verify that multiple components work together correctly.

#### What to Test
- Component interactions
- Form submissions
- API service integration
- Context provider integration with components
- Navigation flows

#### Example Integration Test

```jsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '../context/ThemeContext';
import LoginForm from './LoginForm';
import apiService from '../services/api.service';

// Mock the API service
jest.mock('../services/api.service');

describe('LoginForm Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('submits form and handles successful login', async () => {
    // Mock successful API response
    apiService.post.mockResolvedValueOnce({
      token: 'test-token',
      user: {
        id: '123',
        name: 'Test User',
        role: 'user'
      },
      expiresAt: new Date(Date.now() + 3600000).toISOString()
    });

    const mockOnLogin = jest.fn();

    render(
      <BrowserRouter>
        <ThemeProvider>
          <LoginForm onLogin={mockOnLogin} />
        </ThemeProvider>
      </BrowserRouter>
    );

    // Fill in the form
    act(() => {
      fireEvent.change(screen.getByLabelText(/username/i), {
        target: { value: 'testuser' }
      });
      
      fireEvent.change(screen.getByLabelText(/password/i), {
        target: { value: 'password123' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /login/i }));
    });

    // Wait for the API call to resolve
    await waitFor(() => {
      expect(apiService.post).toHaveBeenCalledWith(
        '/auth/ldaplogin',
        { username: 'testuser', password: 'password123' }
      );
      expect(mockOnLogin).toHaveBeenCalled();
    });
  });

  test('displays error message on login failure', async () => {
    // Mock failed API response
    apiService.post.mockRejectedValueOnce({
      response: {
        data: { message: 'Invalid credentials' }
      }
    });

    render(
      <BrowserRouter>
        <ThemeProvider>
          <LoginForm onLogin={jest.fn()} />
        </ThemeProvider>
      </BrowserRouter>
    );

    // Fill in the form
    act(() => {
      fireEvent.change(screen.getByLabelText(/username/i), {
        target: { value: 'testuser' }
      });
      
      fireEvent.change(screen.getByLabelText(/password/i), {
        target: { value: 'wrongpassword' }
      });
      
      fireEvent.click(screen.getByRole('button', { name: /login/i }));
    });

    // Wait for the error message to appear
    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
    });
  });
});
```

### End-to-End Testing

End-to-end tests verify that complete user flows work correctly from start to finish.

#### What to Test
- User authentication flows
- Request creation and management
- Whitelist platform functionality
- Settings changes
- Navigation between pages

#### Tools
- Cypress: End-to-end testing framework

#### Example E2E Test

```javascript
// cypress/integration/login.spec.js
describe('Login Flow', () => {
  beforeEach(() => {
    cy.visit('/login');
  });

  it('should login successfully with valid credentials', () => {
    cy.intercept('POST', '/auth/ldaplogin', {
      statusCode: 200,
      body: {
        token: 'test-token',
        user: {
          id: '123',
          name: 'Test User',
          role: 'user'
        },
        expiresAt: new Date(Date.now() + 3600000).toISOString()
      }
    }).as('loginRequest');

    cy.get('input[name="username"]').type('testuser');
    cy.get('input[name="password"]').type('password123');
    cy.get('button[type="submit"]').click();

    cy.wait('@loginRequest');
    cy.url().should('include', '/dashboard');
    cy.get('.user-info').should('contain', 'Test User');
  });

  it('should show error message with invalid credentials', () => {
    cy.intercept('POST', '/auth/ldaplogin', {
      statusCode: 401,
      body: {
        message: 'Invalid credentials'
      }
    }).as('loginRequest');

    cy.get('input[name="username"]').type('testuser');
    cy.get('input[name="password"]').type('wrongpassword');
    cy.get('button[type="submit"]').click();

    cy.wait('@loginRequest');
    cy.get('.error-message').should('contain', 'Invalid credentials');
    cy.url().should('include', '/login');
  });
});
```

## Test Organization

### Directory Structure

```
src/
├── __tests__/              # Test utilities and setup
│   ├── setup.js            # Test setup file
│   └── test-utils.js       # Common test utilities
├── components/
│   ├── Component.js        # Component implementation
│   └── Component.test.js   # Component tests
├── services/
│   ├── service.js          # Service implementation
│   └── service.test.js     # Service tests
└── utils/
    ├── utils.js            # Utility functions
    └── utils.test.js       # Utility tests
```

### Naming Conventions

- Test files should be named with `.test.js` suffix
- Test suites should be named descriptively
- Test cases should clearly describe what they're testing

## Test Coverage

### Coverage Goals

- Unit tests: 80% coverage
- Integration tests: Key user flows
- E2E tests: Critical business paths

### Running Coverage Reports

```bash
npm test -- --coverage
```

The coverage report will be generated in the `coverage/` directory.

## Mocking

### API Mocking

Use Jest's mocking capabilities to mock API calls:

```javascript
jest.mock('../services/api.service');

// In your test
apiService.get.mockResolvedValue({ data: mockData });
```

### Component Mocking

Mock child components when testing parent components:

```javascript
jest.mock('../components/ChildComponent', () => {
  return function MockChildComponent(props) {
    return <div data-testid="mock-child">{props.label}</div>;
  };
});
```

### Context Mocking

Create test wrappers for context providers:

```javascript
// test-utils.js
export const renderWithTheme = (ui, { theme = 'light', ...options } = {}) => {
  const Wrapper = ({ children }) => (
    <ThemeProvider initialTheme={theme}>{children}</ThemeProvider>
  );
  
  return render(ui, { wrapper: Wrapper, ...options });
};
```

## Best Practices

1. **Write tests before fixing bugs**
   - Create a test that reproduces the bug
   - Fix the bug
   - Verify the test passes

2. **Keep tests independent**
   - Each test should run in isolation
   - Avoid dependencies between tests
   - Reset state between tests

3. **Test behavior, not implementation**
   - Focus on what the component does, not how it does it
   - Avoid testing implementation details
   - Write tests from the user's perspective

4. **Use realistic test data**
   - Create fixtures that resemble real data
   - Avoid using dummy values that wouldn't occur in production

5. **Test edge cases**
   - Empty states
   - Error states
   - Boundary conditions
   - Loading states

6. **Keep tests fast**
   - Minimize external dependencies
   - Use mocks for slow operations
   - Optimize test setup and teardown

## Continuous Integration

Tests are automatically run in the CI pipeline:

1. On pull requests to main branch
2. On direct commits to main branch
3. Nightly builds

### CI Configuration

Tests must pass before merging pull requests. The CI pipeline:

1. Installs dependencies
2. Runs linting
3. Runs unit and integration tests
4. Generates coverage report
5. Runs E2E tests

## Debugging Tests

### Common Issues

1. **Asynchronous operations**
   - Use `act()` for state updates
   - Use `waitFor()` for async operations
   - Ensure promises are resolved

2. **Component not found**
   - Check selector specificity
   - Verify component is actually rendered
   - Check for conditional rendering

3. **Mock not called**
   - Verify mock is set up correctly
   - Check import paths
   - Ensure the function is called with expected arguments

### Debugging Tools

- Jest's `--debug` flag
- Browser developer tools for E2E tests
- `screen.debug()` to output current DOM
- `console.log()` for debugging test flow

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro)
- [Cypress Documentation](https://docs.cypress.io/guides/overview/why-cypress)
- [Testing React Applications](https://reactjs.org/docs/testing.html)
