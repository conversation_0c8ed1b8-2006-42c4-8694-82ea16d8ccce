# Docker Setup for Form Management App

This document explains how to run the Form Management React application using Docker and Docker Compose.

## Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)

## Quick Start

### Production Mode

Run the app in production mode with optimized build:

```bash
# Build and start the application
docker-compose up -d

# View logs
docker-compose logs -f frontend

# Stop the application
docker-compose down
```

The app will be available at: http://localhost:3000

### Development Mode

Run the app in development mode with hot reload:

```bash
# Start development mode
docker-compose --profile dev up -d frontend-dev

# View logs
docker-compose logs -f frontend-dev

# Stop development mode
docker-compose --profile dev down
```

The development app will be available at: http://localhost:3001

## Available Commands

### Build Commands

```bash
# Build production image
docker-compose build frontend

# Build development image
docker-compose build frontend-dev

# Force rebuild without cache
docker-compose build --no-cache frontend
```

### Management Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart services
docker-compose restart

# View service status
docker-compose ps

# View logs
docker-compose logs -f [service-name]

# Execute commands in container
docker-compose exec frontend sh
```

### Cleanup Commands

```bash
# Remove containers and networks
docker-compose down

# Remove containers, networks, and volumes
docker-compose down -v

# Remove containers, networks, volumes, and images
docker-compose down --rmi all -v

# Clean up Docker system
docker system prune -a
```

## Configuration

### Environment Variables

You can customize the application by setting environment variables:

```bash
# Create .env file
cat > .env << EOF
REACT_APP_API_URL=http://your-backend-url:3014
NODE_ENV=production
EOF
```

### Port Configuration

To change the default ports, modify the `docker-compose.yml` file:

```yaml
services:
  frontend:
    ports:
      - "8080:80"  # Change 3000 to 8080
```

## Architecture

### Production Setup

- **Multi-stage build**: Optimized Docker image with separate build and runtime stages
- **Nginx**: Serves static files and handles routing for SPA
- **Health checks**: Built-in health monitoring
- **Security headers**: Added security headers in nginx configuration

### Development Setup

- **Hot reload**: Changes are reflected immediately
- **Volume mounting**: Source code is mounted for live editing
- **Development dependencies**: All dev tools available

## File Structure

```
.
├── Dockerfile              # Production build
├── Dockerfile.dev          # Development build
├── docker-compose.yml      # Docker Compose configuration
├── nginx.conf              # Nginx configuration
├── .dockerignore           # Docker ignore file
└── DOCKER_README.md        # This file
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Check what's using the port
   lsof -i :3000
   
   # Kill the process or change port in docker-compose.yml
   ```

2. **Build fails**
   ```bash
   # Clear Docker cache and rebuild
   docker-compose build --no-cache
   ```

3. **Container won't start**
   ```bash
   # Check logs for errors
   docker-compose logs frontend
   ```

4. **Hot reload not working in development**
   ```bash
   # Ensure CHOKIDAR_USEPOLLING=true is set
   # Check volume mounting in docker-compose.yml
   ```

### Performance Tips

1. **Use .dockerignore**: Exclude unnecessary files from build context
2. **Multi-stage builds**: Separate build and runtime environments
3. **Layer caching**: Order Dockerfile commands for optimal caching
4. **Volume mounting**: Use volumes for development to avoid rebuilds

## Health Checks

The production container includes health checks:

```bash
# Check container health
docker-compose ps

# Manual health check
curl http://localhost:3000/health
```

## Security

The nginx configuration includes:
- Security headers (XSS protection, content type options, etc.)
- Content Security Policy
- Frame options protection

## API Integration

The nginx configuration includes a proxy for API calls:
- API requests to `/api/*` are proxied to the backend
- Configure the backend URL in environment variables

## Monitoring

View real-time logs:

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f frontend

# Last 100 lines
docker-compose logs --tail=100 frontend
```
